2024-06-24 09:08:15.074900 -- AlientechLinkService __construct -- 
2024-06-24 09:08:15.076200 -- AlientechLinkService processClient -- 
2024-06-24 09:08:15.076700 -- AlientechLinkService is_null($this->client) -- 
2024-06-24 09:08:15.077700 -- initAccessToken -- 
2024-06-24 09:08:15.082500 --  -- 
2024-06-24 09:08:15.083000 -- accessToken empty -- 
2024-06-24 09:08:15.084600 -- AlientechProjectService __construct -- 
2024-06-24 09:08:15.087300 -- FileSlotService __construct -- 
2024-06-24 09:08:15.088200 -- FileSlotService __construct -- 
2024-06-24 09:08:15.088800 -- AsyncOperationService __construct -- 
2024-06-24 09:08:15.091400 -- FileSlotService __construct -- 
2024-06-24 09:08:15.091900 -- Kess3Service __construct -- 
2024-06-24 09:08:15.092400 -- Kess3Service startDecoding -- 
2024-06-24 09:08:15.092900 -- AsyncOperationService startOperationDecode -- 
2024-06-24 09:08:15.093400 -- AlientechLinkService processRequest -- 
2024-06-24 09:08:15.093900 -- isAuth -- 
2024-06-24 09:08:15.094400 -- tryAuth -- 
2024-06-24 09:08:15.244900 -- AlientechLinkService processResponse -- 
2024-06-24 09:08:15.271200 --  --- $log->save() --  ---1 -- 
2024-06-24 09:08:15.273100 --  --- $log->save() --  ---2 -- 
2024-06-24 09:08:15.273300 -- AlientechLinkService processAccessToken -- 
2024-06-24 09:08:15.276400 -- AlientechLinkService isAuth_success -- 
2024-06-24 09:10:30.015800 -- AlientechLinkService __construct -- 
2024-06-24 09:10:30.016000 -- AlientechLinkService processClient -- 
2024-06-24 09:10:30.016000 -- AlientechLinkService is_null($this->client) -- 
2024-06-24 09:10:30.016500 -- initAccessToken -- 
2024-06-24 09:10:30.020500 -- iR8V9-KAuaE -- 
2024-06-24 09:10:30.021500 -- AlientechProjectService __construct -- 
2024-06-24 09:10:30.023500 -- FileSlotService __construct -- 
2024-06-24 09:10:30.024000 -- FileSlotService __construct -- 
2024-06-24 09:10:30.024200 -- AsyncOperationService __construct -- 
2024-06-24 09:10:30.026200 -- FileSlotService __construct -- 
2024-06-24 09:10:30.026400 -- Kess3Service __construct -- 
2024-06-24 09:10:30.026500 -- Kess3Service startDecoding -- 
2024-06-24 09:10:30.026600 -- AsyncOperationService startOperationDecode -- 
2024-06-24 09:10:30.026700 -- AlientechLinkService processRequest -- 
2024-06-24 09:10:30.026700 -- isAuth -- 
2024-06-24 09:10:30.026800 -- AlientechLinkService isAuth_success -- 
2024-06-24 09:12:45.037200 -- AlientechLinkService __construct -- 
2024-06-24 09:12:45.037400 -- AlientechLinkService processClient -- 
2024-06-24 09:12:45.037400 -- AlientechLinkService is_null($this->client) -- 
2024-06-24 09:12:45.037800 -- initAccessToken -- 
2024-06-24 09:12:45.041200 -- iR8V9-KAuaE -- 
2024-06-24 09:12:45.043400 -- AlientechProjectService __construct -- 
2024-06-24 09:12:45.046400 -- FileSlotService __construct -- 
2024-06-24 09:12:45.047100 -- FileSlotService __construct -- 
2024-06-24 09:12:45.047200 -- AsyncOperationService __construct -- 
2024-06-24 09:12:45.048700 -- FileSlotService __construct -- 
2024-06-24 09:12:45.048800 -- Kess3Service __construct -- 
2024-06-24 09:12:45.049400 -- Kess3Service startDecoding -- 
2024-06-24 09:12:45.049700 -- AsyncOperationService startOperationDecode -- 
2024-06-24 09:12:45.050000 -- AlientechLinkService processRequest -- 
2024-06-24 09:12:45.050100 -- isAuth -- 
2024-06-24 09:12:45.050600 -- AlientechLinkService isAuth_success -- 
2024-06-24 09:40:40.463600 -- AlientechLinkService __construct -- 
2024-06-24 09:40:40.463900 -- AlientechLinkService processClient -- 
2024-06-24 09:40:40.464000 -- AlientechLinkService is_null($this->client) -- 
2024-06-24 09:40:40.464500 -- initAccessToken -- 
2024-06-24 09:40:40.468000 -- iR8V9-KAuaE -- 
2024-06-24 09:40:40.469600 -- AlientechProjectService __construct -- 
2024-06-24 09:40:40.472700 -- FileSlotService __construct -- 
2024-06-24 09:40:40.473400 -- FileSlotService __construct -- 
2024-06-24 09:40:40.473500 -- AsyncOperationService __construct -- 
2024-06-24 09:40:40.475500 -- FileSlotService __construct -- 
2024-06-24 09:40:40.475700 -- Kess3Service __construct -- 
2024-06-24 09:40:40.475800 -- Kess3Service startDecoding -- 
2024-06-24 09:40:40.476000 -- AsyncOperationService startOperationDecode -- 
2024-06-24 09:40:40.476100 -- AlientechLinkService processRequest -- 
2024-06-24 09:40:40.476200 -- isAuth -- 
2024-06-24 09:40:40.476300 -- AlientechLinkService isAuth_success -- 
2024-06-24 09:41:20.559500 -- AlientechLinkService __construct -- 
2024-06-24 09:41:20.559800 -- AlientechLinkService processClient -- 
2024-06-24 09:41:20.559800 -- AlientechLinkService is_null($this->client) -- 
2024-06-24 09:41:20.560200 -- initAccessToken -- 
2024-06-24 09:41:20.563800 -- iR8V9-KAuaE -- 
2024-06-24 09:41:20.564900 -- AlientechProjectService __construct -- 
2024-06-24 09:41:20.566800 -- FileSlotService __construct -- 
2024-06-24 09:41:20.567100 -- FileSlotService __construct -- 
2024-06-24 09:41:20.567200 -- AsyncOperationService __construct -- 
2024-06-24 09:41:20.568400 -- FileSlotService __construct -- 
2024-06-24 09:41:20.568500 -- Kess3Service __construct -- 
2024-06-24 09:41:20.568600 -- Kess3Service startDecoding -- 
2024-06-24 09:41:20.568700 -- AsyncOperationService startOperationDecode -- 
2024-06-24 09:41:20.568800 -- AlientechLinkService processRequest -- 
2024-06-24 09:41:20.568900 -- isAuth -- 
2024-06-24 09:41:20.568900 -- AlientechLinkService isAuth_success -- 
2024-06-24 09:55:40.803700 -- AlientechLinkService __construct -- 
2024-06-24 09:55:40.803900 -- AlientechLinkService processClient -- 
2024-06-24 09:55:40.803900 -- AlientechLinkService is_null($this->client) -- 
2024-06-24 09:55:40.804200 -- initAccessToken -- 
2024-06-24 09:55:40.807800 -- iR8V9-KAuaE -- 
2024-06-24 09:55:40.809200 -- AlientechProjectService __construct -- 
2024-06-24 09:55:40.811600 -- FileSlotService __construct -- 
2024-06-24 09:55:40.812000 -- FileSlotService __construct -- 
2024-06-24 09:55:40.812100 -- AsyncOperationService __construct -- 
2024-06-24 09:55:40.813400 -- FileSlotService __construct -- 
2024-06-24 09:55:40.813500 -- Kess3Service __construct -- 
2024-06-24 09:55:40.813600 -- Kess3Service startDecoding -- 
2024-06-24 09:55:40.813700 -- AsyncOperationService startOperationDecode -- 
2024-06-24 09:55:40.813800 -- AlientechLinkService processRequest -- 
2024-06-24 09:55:40.813900 -- isAuth -- 
2024-06-24 09:55:40.814000 -- AlientechLinkService isAuth_success -- 
2024-06-24 10:52:26.228300 -- AlientechLinkService __construct -- 
2024-06-24 10:52:26.228700 -- AlientechLinkService processClient -- 
2024-06-24 10:52:26.229000 -- AlientechLinkService is_null($this->client) -- 
2024-06-24 10:52:26.229700 -- initAccessToken -- 
2024-06-24 10:52:26.232300 -- iR8V9-KAuaE -- 
2024-06-24 10:52:26.233200 -- AlientechProjectService __construct -- 
2024-06-24 10:52:26.234300 -- FileSlotService __construct -- 
2024-06-24 10:52:26.234700 -- FileSlotService __construct -- 
2024-06-24 10:52:26.234900 -- AsyncOperationService __construct -- 
2024-06-24 10:52:26.235700 -- FileSlotService __construct -- 
2024-06-24 10:52:26.236000 -- Kess3Service __construct -- 
2024-06-24 10:52:26.236300 -- Kess3Service startDecoding -- 
2024-06-24 10:52:26.236500 -- AsyncOperationService startOperationDecode -- 
2024-06-24 10:52:26.236700 -- AlientechLinkService processRequest -- 
2024-06-24 10:52:26.237000 -- isAuth -- 
2024-06-24 10:52:26.237200 -- AlientechLinkService isAuth_success -- 
2024-06-24 15:25:28.044400 -- AlientechLinkService __construct -- 
2024-06-24 15:25:28.044600 -- AlientechLinkService processClient -- 
2024-06-24 15:25:28.044600 -- AlientechLinkService is_null($this->client) -- 
2024-06-24 15:25:28.045000 -- initAccessToken -- 
2024-06-24 15:25:28.048400 -- iR8V9-KAuaE -- 
2024-06-24 15:25:28.048900 -- AlientechProjectService __construct -- 
2024-06-24 15:25:28.049700 -- FileSlotService __construct -- 
2024-06-24 15:25:28.049900 -- FileSlotService __construct -- 
2024-06-24 15:25:28.050000 -- AsyncOperationService __construct -- 
2024-06-24 15:25:28.050600 -- FileSlotService __construct -- 
2024-06-24 15:25:28.050600 -- Kess3Service __construct -- 
2024-06-24 15:25:28.050700 -- Kess3Service startDecoding -- 
2024-06-24 15:25:28.050700 -- AsyncOperationService startOperationDecode -- 
2024-06-24 15:25:28.050700 -- AlientechLinkService processRequest -- 
2024-06-24 15:25:28.050700 -- isAuth -- 
2024-06-24 15:25:28.050800 -- AlientechLinkService isAuth_success -- 
2024-06-25 10:20:56.773600 -- AlientechLinkService __construct -- 
2024-06-25 10:20:56.773900 -- AlientechLinkService processClient -- 
2024-06-25 10:20:56.774000 -- AlientechLinkService is_null($this->client) -- 
2024-06-25 10:20:56.774500 -- initAccessToken -- 
2024-06-25 10:20:56.778200 -- iR8V9-KAuaE -- 
2024-06-25 10:20:56.779000 -- AlientechProjectService __construct -- 
2024-06-25 10:20:56.781400 -- FileSlotService __construct -- 
2024-06-25 10:20:56.781600 -- FileSlotService __construct -- 
2024-06-25 10:20:56.781700 -- AsyncOperationService __construct -- 
2024-06-25 10:20:56.782400 -- FileSlotService __construct -- 
2024-06-25 10:20:56.782500 -- Kess3Service __construct -- 
2024-06-25 10:20:56.782600 -- Kess3Service startDecoding -- 
2024-06-25 10:20:56.782600 -- AsyncOperationService startOperationDecode -- 
2024-06-25 10:20:56.782700 -- AlientechLinkService processRequest -- 
2024-06-25 10:20:56.782700 -- isAuth -- 
2024-06-25 10:20:56.782700 -- AlientechLinkService isAuth_success -- 
2024-06-25 11:47:17.406300 -- AlientechLinkService __construct -- 
2024-06-25 11:47:17.406500 -- AlientechLinkService processClient -- 
2024-06-25 11:47:17.406600 -- AlientechLinkService is_null($this->client) -- 
2024-06-25 11:47:17.406900 -- initAccessToken -- 
2024-06-25 11:47:17.410000 -- iR8V9-KAuaE -- 
2024-06-25 11:47:17.410600 -- AlientechProjectService __construct -- 
2024-06-25 11:47:17.411400 -- FileSlotService __construct -- 
2024-06-25 11:47:17.411600 -- FileSlotService __construct -- 
2024-06-25 11:47:17.411600 -- AsyncOperationService __construct -- 
2024-06-25 11:47:17.412100 -- FileSlotService __construct -- 
2024-06-25 11:47:17.412200 -- Kess3Service __construct -- 
2024-06-25 11:47:17.412200 -- Kess3Service startDecoding -- 
2024-06-25 11:47:17.412300 -- AsyncOperationService startOperationDecode -- 
2024-06-25 11:47:17.412300 -- AlientechLinkService processRequest -- 
2024-06-25 11:47:17.412300 -- isAuth -- 
2024-06-25 11:47:17.412300 -- AlientechLinkService isAuth_success -- 
2024-06-26 05:56:30.964000 -- AlientechLinkService __construct -- 
2024-06-26 05:56:30.964600 -- AlientechLinkService processClient -- 
2024-06-26 05:56:30.964800 -- AlientechLinkService is_null($this->client) -- 
2024-06-26 05:56:30.965500 -- initAccessToken -- 
2024-06-26 05:56:30.968700 -- iR8V9-KAuaE -- 
2024-06-26 05:56:30.969400 -- AlientechProjectService __construct -- 
2024-06-26 05:56:30.970600 -- FileSlotService __construct -- 
2024-06-26 05:56:30.971000 -- FileSlotService __construct -- 
2024-06-26 05:56:30.971200 -- AsyncOperationService __construct -- 
2024-06-26 05:56:30.972000 -- FileSlotService __construct -- 
2024-06-26 05:56:30.972200 -- Kess3Service __construct -- 
2024-06-26 05:56:30.972400 -- Kess3Service startDecoding -- 
2024-06-26 05:56:30.972600 -- AsyncOperationService startOperationDecode -- 
2024-06-26 05:56:30.972800 -- AlientechLinkService processRequest -- 
2024-06-26 05:56:30.973000 -- isAuth -- 
2024-06-26 05:56:30.973200 -- AlientechLinkService isAuth_success -- 
2024-06-26 07:33:21.565500 -- AlientechLinkService __construct -- 
2024-06-26 07:33:21.565700 -- AlientechLinkService processClient -- 
2024-06-26 07:33:21.565800 -- AlientechLinkService is_null($this->client) -- 
2024-06-26 07:33:21.566300 -- initAccessToken -- 
2024-06-26 07:33:21.568800 -- iR8V9-KAuaE -- 
2024-06-26 07:33:21.569400 -- AlientechProjectService __construct -- 
2024-06-26 07:33:21.570300 -- FileSlotService __construct -- 
2024-06-26 07:33:21.570500 -- FileSlotService __construct -- 
2024-06-26 07:33:21.570600 -- AsyncOperationService __construct -- 
2024-06-26 07:33:21.571200 -- FileSlotService __construct -- 
2024-06-26 07:33:21.571300 -- Kess3Service __construct -- 
2024-06-26 07:33:21.571300 -- Kess3Service startDecoding -- 
2024-06-26 07:33:21.571400 -- AsyncOperationService startOperationDecode -- 
2024-06-26 07:33:21.571400 -- AlientechLinkService processRequest -- 
2024-06-26 07:33:21.571400 -- isAuth -- 
2024-06-26 07:33:21.571500 -- AlientechLinkService isAuth_success -- 
2024-06-26 21:01:02.653200 -- AlientechLinkService __construct -- 
2024-06-26 21:01:02.653400 -- AlientechLinkService processClient -- 
2024-06-26 21:01:02.653500 -- AlientechLinkService is_null($this->client) -- 
2024-06-26 21:01:02.654300 -- initAccessToken -- 
2024-06-26 21:01:02.657700 -- iR8V9-KAuaE -- 
2024-06-26 21:01:02.658700 -- AlientechProjectService __construct -- 
2024-06-26 21:01:02.660400 -- FileSlotService __construct -- 
2024-06-26 21:01:02.660800 -- FileSlotService __construct -- 
2024-06-26 21:01:02.660900 -- AsyncOperationService __construct -- 
2024-06-26 21:01:02.661900 -- FileSlotService __construct -- 
2024-06-26 21:01:02.661900 -- Kess3Service __construct -- 
2024-06-26 21:01:02.662000 -- Kess3Service startDecoding -- 
2024-06-26 21:01:02.662100 -- AsyncOperationService startOperationDecode -- 
2024-06-26 21:01:02.662100 -- AlientechLinkService processRequest -- 
2024-06-26 21:01:02.662200 -- isAuth -- 
2024-06-26 21:01:02.662300 -- AlientechLinkService isAuth_success -- 
2024-06-26 21:21:12.600500 -- AlientechLinkService __construct -- 
2024-06-26 21:21:12.600700 -- AlientechLinkService processClient -- 
2024-06-26 21:21:12.600700 -- AlientechLinkService is_null($this->client) -- 
2024-06-26 21:21:12.601100 -- initAccessToken -- 
2024-06-26 21:21:12.603700 -- iR8V9-KAuaE -- 
2024-06-26 21:21:12.604600 -- AlientechProjectService __construct -- 
2024-06-26 21:21:12.606400 -- FileSlotService __construct -- 
2024-06-26 21:21:12.606700 -- FileSlotService __construct -- 
2024-06-26 21:21:12.606800 -- AsyncOperationService __construct -- 
2024-06-26 21:21:12.607600 -- FileSlotService __construct -- 
2024-06-26 21:21:12.607700 -- Kess3Service __construct -- 
2024-06-26 21:21:12.607700 -- Kess3Service startDecoding -- 
2024-06-26 21:21:12.607800 -- AsyncOperationService startOperationDecode -- 
2024-06-26 21:21:12.607800 -- AlientechLinkService processRequest -- 
2024-06-26 21:21:12.607800 -- isAuth -- 
2024-06-26 21:21:12.607900 -- AlientechLinkService isAuth_success -- 
2024-06-26 21:21:13.144200 -- AlientechLinkService processResponse -- 
2024-06-26 21:21:13.159400 --  --- $log->save() --  ---3 -- 
2024-06-26 21:21:13.161400 --  --- $log->save() --  ---4 -- 
2024-06-26 21:21:13.161600 -- AsyncOperationService createOperationDtoByData -- 
2024-06-26 21:21:13.161700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-26 21:21:13.168800 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-26 21:37:18.736900 -- AlientechLinkService __construct -- 
2024-06-26 21:37:18.737100 -- AlientechLinkService processClient -- 
2024-06-26 21:37:18.737100 -- AlientechLinkService is_null($this->client) -- 
2024-06-26 21:37:18.737500 -- initAccessToken -- 
2024-06-26 21:37:18.741400 -- iR8V9-KAuaE -- 
2024-06-26 21:37:18.743500 -- AlientechProjectService __construct -- 
2024-06-26 21:37:18.746500 -- FileSlotService __construct -- 
2024-06-26 21:37:18.747000 -- FileSlotService __construct -- 
2024-06-26 21:37:18.747200 -- AsyncOperationService __construct -- 
2024-06-26 21:37:18.748600 -- FileSlotService __construct -- 
2024-06-26 21:37:18.748700 -- Kess3Service __construct -- 
2024-06-26 21:37:18.748900 -- Kess3Service startDecoding -- 
2024-06-26 21:37:18.749000 -- AsyncOperationService startOperationDecode -- 
2024-06-26 21:37:18.749100 -- AlientechLinkService processRequest -- 
2024-06-26 21:37:18.749200 -- isAuth -- 
2024-06-26 21:37:18.749300 -- AlientechLinkService isAuth_success -- 
2024-06-26 21:37:20.370700 -- AlientechLinkService processResponse -- 
2024-06-26 21:37:20.388200 --  --- $log->save() --  ---5 -- 
2024-06-26 21:37:20.390200 --  --- $log->save() --  ---6 -- 
2024-06-26 21:37:20.390500 -- AsyncOperationService createOperationDtoByData -- 
2024-06-26 21:37:20.391300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-26 21:37:20.401500 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-26 21:37:22.580800 -- AlientechLinkService __construct -- 
2024-06-26 21:37:22.583000 -- AlientechLinkService processClient -- 
2024-06-26 21:37:22.583100 -- AlientechLinkService is_null($this->client) -- 
2024-06-26 21:37:22.585000 -- initAccessToken -- 
2024-06-26 21:37:22.587100 -- iR8V9-KAuaE -- 
2024-06-26 21:37:22.589400 -- AlientechProjectService __construct -- 
2024-06-26 21:37:22.592800 -- FileSlotService __construct -- 
2024-06-26 21:37:22.593000 -- FileSlotService __construct -- 
2024-06-26 21:37:22.593100 -- AsyncOperationService __construct -- 
2024-06-26 21:37:22.595400 -- ApiController actionKess3Decoded -- 
2024-06-26 21:37:22.601800 --  --- $log->save() --  ---7 -- 
2024-06-26 21:37:22.601900 -- AsyncOperationService createOperationDtoByData -- 
2024-06-26 21:37:22.602000 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-26 21:37:22.606400 -- AsyncOperationService processCompletedOperation -- 
2024-06-26 21:37:22.606500 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-26 21:37:22.609000 -- AsyncOperationService finishCompletedOperation -- 
2024-06-26 21:37:22.609100 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-06-26 21:37:22.609200 -- AlientechProjectService processSuccessOperationDecode -- 
2024-06-26 21:37:22.611900 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-26 21:37:23.771200 -- AlientechLinkService __construct -- 
2024-06-26 21:37:23.771400 -- AlientechLinkService processClient -- 
2024-06-26 21:37:23.771400 -- AlientechLinkService is_null($this->client) -- 
2024-06-26 21:37:23.771800 -- initAccessToken -- 
2024-06-26 21:37:23.774300 -- iR8V9-KAuaE -- 
2024-06-26 21:37:23.776200 -- AlientechProjectService __construct -- 
2024-06-26 21:37:23.779100 -- FileSlotService __construct -- 
2024-06-26 21:37:23.779900 -- FileSlotService __construct -- 
2024-06-26 21:37:23.780000 -- AsyncOperationService __construct -- 
2024-06-26 21:37:23.781100 -- AsyncOperationService downloadDecodedFiles -- 
2024-06-26 21:37:23.783000 -- FileService downloadFile -- 
2024-06-26 21:37:23.783100 -- AlientechLinkService processRequest -- 
2024-06-26 21:37:23.783100 -- isAuth -- 
2024-06-26 21:37:23.783200 -- AlientechLinkService isAuth_success -- 
2024-06-26 21:37:24.113800 -- AlientechLinkService processResponse -- 
2024-06-26 21:37:24.134400 --  --- $log->save() --  ---8 -- 
2024-06-26 21:37:24.136200 --  --- $log->save() --  ---9 -- 
2024-06-26 21:37:24.137100 -- FileService saveDecodedFile -- 
2024-06-26 21:37:24.157900 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-26 21:37:24.216700 -- FileService downloadFile -- 
2024-06-26 21:37:24.216800 -- AlientechLinkService processRequest -- 
2024-06-26 21:37:24.216900 -- isAuth -- 
2024-06-26 21:37:24.216900 -- AlientechLinkService isAuth_success -- 
2024-06-26 21:37:26.443800 -- AlientechLinkService processResponse -- 
2024-06-26 21:37:26.446300 --  --- $log->save() --  ---10 -- 
2024-06-26 21:37:26.662800 --  --- $log->save() --  ---11 -- 
2024-06-26 21:37:26.689700 -- FileService saveDecodedFile -- 
2024-06-26 21:37:26.704600 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-26 21:37:26.726900 -- FileService downloadFile -- 
2024-06-26 21:37:26.727000 -- AlientechLinkService processRequest -- 
2024-06-26 21:37:26.727100 -- isAuth -- 
2024-06-26 21:37:26.727100 -- AlientechLinkService isAuth_success -- 
2024-06-26 21:37:27.150200 -- AlientechLinkService processResponse -- 
2024-06-26 21:37:27.154400 --  --- $log->save() --  ---12 -- 
2024-06-26 21:37:27.203900 --  --- $log->save() --  ---13 -- 
2024-06-26 21:37:27.214500 -- FileService saveDecodedFile -- 
2024-06-26 21:37:27.225300 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-26 21:37:50.985100 -- AlientechLinkService __construct -- 
2024-06-26 21:37:50.985400 -- AlientechLinkService processClient -- 
2024-06-26 21:37:50.985400 -- AlientechLinkService is_null($this->client) -- 
2024-06-26 21:37:50.985700 -- initAccessToken -- 
2024-06-26 21:37:50.988700 -- iR8V9-KAuaE -- 
2024-06-26 21:37:50.989400 -- AlientechProjectService __construct -- 
2024-06-26 21:37:50.990500 -- FileSlotService __construct -- 
2024-06-26 21:37:50.990900 -- FileSlotService __construct -- 
2024-06-26 21:37:50.991100 -- AsyncOperationService __construct -- 
2024-06-26 21:37:50.991800 -- FileSlotService __construct -- 
2024-06-26 21:37:50.992000 -- Kess3Service __construct -- 
2024-06-26 21:37:50.992300 -- Kess3Service startEncoding -- 
2024-06-26 21:37:50.992500 -- AsyncOperationService startOperationEncode -- 
2024-06-26 21:37:51.009000 -- FileService uploadFiles -- 
2024-06-26 21:37:51.009600 -- AlientechLinkService processRequest -- 
2024-06-26 21:37:51.009900 -- isAuth -- 
2024-06-26 21:37:51.010100 -- AlientechLinkService isAuth_success -- 
2024-06-26 21:37:51.418300 -- AlientechLinkService processResponse -- 
2024-06-26 21:37:51.426000 --  --- $log->save() --  ---14 -- 
2024-06-26 21:37:51.427400 --  --- $log->save() --  ---15 -- 
2024-06-26 21:37:51.433600 -- AlientechLinkService processRequest -- 
2024-06-26 21:37:51.434200 -- isAuth -- 
2024-06-26 21:37:51.434500 -- AlientechLinkService isAuth_success -- 
2024-06-26 21:37:52.848200 -- AlientechLinkService processResponse -- 
2024-06-26 21:37:52.852200 --  --- $log->save() --  ---16 -- 
2024-06-26 21:37:52.854600 --  --- $log->save() --  ---17 -- 
2024-06-26 21:37:52.854800 -- AsyncOperationService createOperationDtoByData -- 
2024-06-26 21:37:52.854900 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-26 21:37:52.855800 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-26 21:37:54.962200 -- AlientechLinkService __construct -- 
2024-06-26 21:37:54.962500 -- AlientechLinkService processClient -- 
2024-06-26 21:37:54.962500 -- AlientechLinkService is_null($this->client) -- 
2024-06-26 21:37:54.962600 -- initAccessToken -- 
2024-06-26 21:37:54.963900 -- iR8V9-KAuaE -- 
2024-06-26 21:37:54.964200 -- AlientechProjectService __construct -- 
2024-06-26 21:37:54.964400 -- FileSlotService __construct -- 
2024-06-26 21:37:54.964500 -- FileSlotService __construct -- 
2024-06-26 21:37:54.964500 -- AsyncOperationService __construct -- 
2024-06-26 21:37:54.964700 -- ApiController actionKess3Encoded -- 
2024-06-26 21:37:54.967700 --  --- $log->save() --  ---18 -- 
2024-06-26 21:37:54.967800 -- AsyncOperationService createOperationDtoByData -- 
2024-06-26 21:37:54.967800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-26 21:37:54.968700 -- AsyncOperationService processCompletedOperation -- 
2024-06-26 21:37:54.968800 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-26 21:37:54.970500 -- AsyncOperationService finishCompletedOperation -- 
2024-06-26 21:37:54.970600 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-06-26 21:37:54.970700 -- AlientechProjectService processSuccessOperationEncode -- 
2024-06-26 21:37:54.972400 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-26 21:37:58.050600 -- AlientechLinkService __construct -- 
2024-06-26 21:37:58.050800 -- AlientechLinkService processClient -- 
2024-06-26 21:37:58.051200 -- AlientechLinkService is_null($this->client) -- 
2024-06-26 21:37:58.051800 -- initAccessToken -- 
2024-06-26 21:37:58.054600 -- iR8V9-KAuaE -- 
2024-06-26 21:37:58.055600 -- AlientechProjectService __construct -- 
2024-06-26 21:37:58.057300 -- FileSlotService __construct -- 
2024-06-26 21:37:58.057600 -- FileSlotService __construct -- 
2024-06-26 21:37:58.057700 -- AsyncOperationService __construct -- 
2024-06-26 21:37:58.058100 -- AsyncOperationService downloadEncodedFiles -- 
2024-06-26 21:37:58.058900 -- FileService downloadFile -- 
2024-06-26 21:37:58.059000 -- AlientechLinkService processRequest -- 
2024-06-26 21:37:58.059200 -- isAuth -- 
2024-06-26 21:37:58.059200 -- AlientechLinkService isAuth_success -- 
2024-06-26 21:37:58.916600 -- AlientechLinkService processResponse -- 
2024-06-26 21:37:58.934700 --  --- $log->save() --  ---19 -- 
2024-06-26 21:37:59.027800 --  --- $log->save() --  ---20 -- 
2024-06-26 21:37:59.047500 -- FileService saveEncodedFile -- 
2024-06-26 21:37:59.084700 -- FileService $initFile->file_history={"id":"7572","value":"1","file_ver":"1","comment":"123","option":"can_download"} -- 
2024-06-26 21:37:59.105000 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-06-26 21:37:59.146200 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-06-27 08:39:27.875600 -- AlientechLinkService __construct -- 
2024-06-27 08:39:27.876600 -- AlientechLinkService processClient -- 
2024-06-27 08:39:27.876800 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 08:39:27.877700 -- initAccessToken -- 
2024-06-27 08:39:27.884600 -- iR8V9-KAuaE -- 
2024-06-27 08:39:27.887200 -- AlientechProjectService __construct -- 
2024-06-27 08:39:27.890700 -- FileSlotService __construct -- 
2024-06-27 08:39:27.891200 -- FileSlotService __construct -- 
2024-06-27 08:39:27.891400 -- AsyncOperationService __construct -- 
2024-06-27 08:39:27.894300 -- FileSlotService __construct -- 
2024-06-27 08:39:27.894500 -- Kess3Service __construct -- 
2024-06-27 08:39:27.894600 -- Kess3Service startDecoding -- 
2024-06-27 08:39:27.894600 -- AsyncOperationService startOperationDecode -- 
2024-06-27 08:39:27.894700 -- AlientechLinkService processRequest -- 
2024-06-27 08:39:27.894800 -- isAuth -- 
2024-06-27 08:39:27.894900 -- AlientechLinkService isAuth_success -- 
2024-06-27 08:39:28.673100 -- AlientechLinkService processResponse -- 
2024-06-27 08:39:28.687800 --  --- $log->save() --  ---21 -- 
2024-06-27 08:39:28.687900 -- response_status=429 -- 
2024-06-27 08:39:28.688000 -- response=Http-Code: 429
Content-Type: text/plain; charset=utf-8
Retry-After: 0
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Thu, 27 Jun 2024 08:39:28 GMT
Connection: close

TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-06-27 08:39:28.688000 -- content=TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-06-27 08:39:28.688100 -- FileSlotService closeAllFileSlots -- 
2024-06-27 08:39:28.688100 -- FileSlotService getSlots -- 
2024-06-27 08:39:28.688200 -- AlientechLinkService processRequest -- 
2024-06-27 08:39:28.688300 -- isAuth -- 
2024-06-27 08:39:28.688300 -- AlientechLinkService isAuth_success -- 
2024-06-27 08:39:28.820800 -- AlientechLinkService processResponse -- 
2024-06-27 08:39:28.823200 --  --- $log->save() --  ---22 -- 
2024-06-27 08:39:28.825300 --  --- $log->save() --  ---23 -- 
2024-06-27 08:39:28.826200 -- slot [7558a28b-caed-435b-9cd0-27c5076a68f3] dateDiff {"y":0,"m":0,"d":0,"h":11,"i":2,"s":8,"f":0.569472,"invert":1,"days":0,"from_string":false} -- 
2024-06-27 08:39:28.826300 -- FileSlotService closeSlot -- 
2024-06-27 08:39:28.826300 -- /api/kess3/file-slots/7558a28b-caed-435b-9cd0-27c5076a68f3/close  -- 
2024-06-27 08:39:28.826400 -- AlientechLinkService processRequest -- 
2024-06-27 08:39:28.826400 -- isAuth -- 
2024-06-27 08:39:28.826600 -- AlientechLinkService isAuth_success -- 
2024-06-27 08:39:29.530700 -- AlientechLinkService processResponse -- 
2024-06-27 08:39:29.533600 --  --- $log->save() --  ---24 -- 
2024-06-27 08:39:29.537400 --  --- $log->save() --  ---25 -- 
2024-06-27 08:39:29.537700 -- slot [7558a28b-caed-435b-9cd0-27c5076a68f3] closeSlot null -- 
2024-06-27 08:39:29.537900 -- slot [df6b7185-99f5-4765-af90-f2a556f1f376] dateDiff {"y":0,"m":0,"d":0,"h":11,"i":18,"s":16,"f":0.47781,"invert":1,"days":0,"from_string":false} -- 
2024-06-27 08:39:29.538000 -- FileSlotService closeSlot -- 
2024-06-27 08:39:29.538100 -- /api/kess3/file-slots/df6b7185-99f5-4765-af90-f2a556f1f376/close  -- 
2024-06-27 08:39:29.538100 -- AlientechLinkService processRequest -- 
2024-06-27 08:39:29.538200 -- isAuth -- 
2024-06-27 08:39:29.538300 -- AlientechLinkService isAuth_success -- 
2024-06-27 08:39:30.280700 -- AlientechLinkService processResponse -- 
2024-06-27 08:39:30.283300 --  --- $log->save() --  ---26 -- 
2024-06-27 08:39:30.285300 --  --- $log->save() --  ---27 -- 
2024-06-27 08:39:30.285500 -- slot [df6b7185-99f5-4765-af90-f2a556f1f376] closeSlot null -- 
2024-06-27 08:39:30.285700 -- slot [f322d4e8-44c2-4a6c-be33-bda49ce4a704] dateDiff {"y":0,"m":0,"d":15,"h":14,"i":10,"s":9,"f":0.552314,"invert":1,"days":15,"from_string":false} -- 
2024-06-27 08:39:30.285800 -- FileSlotService closeSlot -- 
2024-06-27 08:39:30.286500 -- /api/kess3/file-slots/f322d4e8-44c2-4a6c-be33-bda49ce4a704/close  -- 
2024-06-27 08:39:30.286600 -- AlientechLinkService processRequest -- 
2024-06-27 08:39:30.287100 -- isAuth -- 
2024-06-27 08:39:30.287100 -- AlientechLinkService isAuth_success -- 
2024-06-27 08:39:30.534900 -- AlientechLinkService processResponse -- 
2024-06-27 08:39:30.537100 --  --- $log->save() --  ---28 -- 
2024-06-27 08:39:30.539300 --  --- $log->save() --  ---29 -- 
2024-06-27 08:39:30.539500 -- slot [f322d4e8-44c2-4a6c-be33-bda49ce4a704] closeSlot null -- 
2024-06-27 08:39:30.539800 -- slot [ff122272-aee1-4055-9b2d-03f16bebc654] dateDiff {"y":0,"m":1,"d":8,"h":11,"i":27,"s":53,"f":0.703047,"invert":1,"days":39,"from_string":false} -- 
2024-06-27 08:39:30.540500 -- FileSlotService closeSlot -- 
2024-06-27 08:39:30.540900 -- /api/kess3/file-slots/ff122272-aee1-4055-9b2d-03f16bebc654/close  -- 
2024-06-27 08:39:30.541400 -- AlientechLinkService processRequest -- 
2024-06-27 08:39:30.541800 -- isAuth -- 
2024-06-27 08:39:30.542200 -- AlientechLinkService isAuth_success -- 
2024-06-27 08:39:30.957900 -- AlientechLinkService processResponse -- 
2024-06-27 08:39:30.961400 --  --- $log->save() --  ---30 -- 
2024-06-27 08:39:30.963400 --  --- $log->save() --  ---31 -- 
2024-06-27 08:39:30.963500 -- slot [ff122272-aee1-4055-9b2d-03f16bebc654] closeSlot null -- 
2024-06-27 08:39:30.963700 -- closeAllSlots finish -- 
2024-06-27 08:39:30.964200 -- AsyncOperationService startOperationDecode -- 
2024-06-27 08:39:30.964300 -- AlientechLinkService processRequest -- 
2024-06-27 08:39:30.964800 -- isAuth -- 
2024-06-27 08:39:30.965100 -- AlientechLinkService isAuth_success -- 
2024-06-27 08:39:31.350800 -- AlientechLinkService processResponse -- 
2024-06-27 08:39:31.352900 --  --- $log->save() --  ---32 -- 
2024-06-27 08:39:31.354500 --  --- $log->save() --  ---33 -- 
2024-06-27 08:39:31.354800 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 08:39:31.354900 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 08:39:31.366500 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 08:39:34.659600 -- AlientechLinkService __construct -- 
2024-06-27 08:39:34.659800 -- AlientechLinkService processClient -- 
2024-06-27 08:39:34.659900 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 08:39:34.660000 -- initAccessToken -- 
2024-06-27 08:39:34.660400 -- iR8V9-KAuaE -- 
2024-06-27 08:39:34.660600 -- AlientechProjectService __construct -- 
2024-06-27 08:39:34.660700 -- FileSlotService __construct -- 
2024-06-27 08:39:34.660800 -- FileSlotService __construct -- 
2024-06-27 08:39:34.660800 -- AsyncOperationService __construct -- 
2024-06-27 08:39:34.660900 -- ApiController actionKess3Decoded -- 
2024-06-27 08:39:34.671400 --  --- $log->save() --  ---34 -- 
2024-06-27 08:39:34.671700 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 08:39:34.671700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 08:39:34.676500 -- AsyncOperationService processCompletedOperation -- 
2024-06-27 08:39:34.676600 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-27 08:39:34.679600 -- AsyncOperationService finishCompletedOperation -- 
2024-06-27 08:39:34.679800 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-06-27 08:39:34.679800 -- AlientechProjectService processSuccessOperationDecode -- 
2024-06-27 08:39:34.683100 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-27 08:39:36.814500 -- AlientechLinkService __construct -- 
2024-06-27 08:39:36.814600 -- AlientechLinkService processClient -- 
2024-06-27 08:39:36.814900 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 08:39:36.815500 -- initAccessToken -- 
2024-06-27 08:39:36.818200 -- iR8V9-KAuaE -- 
2024-06-27 08:39:36.818900 -- AlientechProjectService __construct -- 
2024-06-27 08:39:36.820000 -- FileSlotService __construct -- 
2024-06-27 08:39:36.820200 -- FileSlotService __construct -- 
2024-06-27 08:39:36.820200 -- AsyncOperationService __construct -- 
2024-06-27 08:39:36.820400 -- AsyncOperationService downloadDecodedFiles -- 
2024-06-27 08:39:36.821000 -- FileService downloadFile -- 
2024-06-27 08:39:36.821000 -- AlientechLinkService processRequest -- 
2024-06-27 08:39:36.821100 -- isAuth -- 
2024-06-27 08:39:36.821100 -- AlientechLinkService isAuth_success -- 
2024-06-27 08:39:37.629700 -- AlientechLinkService processResponse -- 
2024-06-27 08:39:37.660600 --  --- $log->save() --  ---35 -- 
2024-06-27 08:39:37.828900 --  --- $log->save() --  ---36 -- 
2024-06-27 08:39:37.857600 -- FileService saveDecodedFile -- 
2024-06-27 08:39:37.889600 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-27 09:00:32.892300 -- AlientechLinkService __construct -- 
2024-06-27 09:00:32.892600 -- AlientechLinkService processClient -- 
2024-06-27 09:00:32.892600 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 09:00:32.893000 -- initAccessToken -- 
2024-06-27 09:00:32.896600 -- iR8V9-KAuaE -- 
2024-06-27 09:00:32.898400 -- AlientechProjectService __construct -- 
2024-06-27 09:00:32.901300 -- FileSlotService __construct -- 
2024-06-27 09:00:32.902300 -- FileSlotService __construct -- 
2024-06-27 09:00:32.902500 -- AsyncOperationService __construct -- 
2024-06-27 09:00:32.904600 -- FileSlotService __construct -- 
2024-06-27 09:00:32.904700 -- Kess3Service __construct -- 
2024-06-27 09:00:32.905100 -- Kess3Service startEncoding -- 
2024-06-27 09:00:32.905300 -- AsyncOperationService startOperationEncode -- 
2024-06-27 09:00:32.939200 -- FileService uploadFiles -- 
2024-06-27 09:00:32.940300 -- AlientechLinkService processRequest -- 
2024-06-27 09:00:32.940400 -- isAuth -- 
2024-06-27 09:00:32.940500 -- AlientechLinkService isAuth_success -- 
2024-06-27 09:00:34.765700 -- AlientechLinkService processResponse -- 
2024-06-27 09:00:34.773100 --  --- $log->save() --  ---37 -- 
2024-06-27 09:00:34.774800 --  --- $log->save() --  ---38 -- 
2024-06-27 09:00:34.782600 -- AlientechLinkService processRequest -- 
2024-06-27 09:00:34.782700 -- isAuth -- 
2024-06-27 09:00:34.782800 -- AlientechLinkService isAuth_success -- 
2024-06-27 09:00:35.896400 -- AlientechLinkService processResponse -- 
2024-06-27 09:00:35.898400 --  --- $log->save() --  ---39 -- 
2024-06-27 09:00:35.901000 --  --- $log->save() --  ---40 -- 
2024-06-27 09:00:35.901200 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 09:00:35.901300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 09:00:35.902600 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 09:00:46.326600 -- AlientechLinkService __construct -- 
2024-06-27 09:00:46.327100 -- AlientechLinkService processClient -- 
2024-06-27 09:00:46.327400 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 09:00:46.328000 -- initAccessToken -- 
2024-06-27 09:00:46.330600 -- iR8V9-KAuaE -- 
2024-06-27 09:00:46.331400 -- AlientechProjectService __construct -- 
2024-06-27 09:00:46.332700 -- FileSlotService __construct -- 
2024-06-27 09:00:46.333000 -- FileSlotService __construct -- 
2024-06-27 09:00:46.333400 -- AsyncOperationService __construct -- 
2024-06-27 09:00:46.334300 -- FileSlotService __construct -- 
2024-06-27 09:00:46.334600 -- Kess3Service __construct -- 
2024-06-27 09:00:46.335000 -- Kess3Service startEncoding -- 
2024-06-27 09:00:46.335300 -- AsyncOperationService startOperationEncode -- 
2024-06-27 09:00:46.353900 -- FileService uploadFiles -- 
2024-06-27 09:00:46.354700 -- AlientechLinkService processRequest -- 
2024-06-27 09:00:46.355100 -- isAuth -- 
2024-06-27 09:00:46.355300 -- AlientechLinkService isAuth_success -- 
2024-06-27 09:00:47.101500 -- AlientechLinkService processResponse -- 
2024-06-27 09:00:47.113500 --  --- $log->save() --  ---41 -- 
2024-06-27 09:00:47.115300 --  --- $log->save() --  ---42 -- 
2024-06-27 09:00:47.125600 -- AlientechLinkService processRequest -- 
2024-06-27 09:00:47.125700 -- isAuth -- 
2024-06-27 09:00:47.125800 -- AlientechLinkService isAuth_success -- 
2024-06-27 09:00:48.087900 -- AlientechLinkService processResponse -- 
2024-06-27 09:00:48.090500 --  --- $log->save() --  ---43 -- 
2024-06-27 09:00:48.092900 --  --- $log->save() --  ---44 -- 
2024-06-27 09:00:48.093100 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 09:00:48.093300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 09:00:48.094500 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 09:00:54.892300 -- AlientechLinkService __construct -- 
2024-06-27 09:00:54.892700 -- AlientechLinkService processClient -- 
2024-06-27 09:00:54.892700 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 09:00:54.892800 -- initAccessToken -- 
2024-06-27 09:00:54.893400 -- iR8V9-KAuaE -- 
2024-06-27 09:00:54.893600 -- AlientechProjectService __construct -- 
2024-06-27 09:00:54.893800 -- FileSlotService __construct -- 
2024-06-27 09:00:54.893800 -- FileSlotService __construct -- 
2024-06-27 09:00:54.893900 -- AsyncOperationService __construct -- 
2024-06-27 09:00:54.894000 -- ApiController actionKess3Encoded -- 
2024-06-27 09:00:54.896100 --  --- $log->save() --  ---45 -- 
2024-06-27 09:00:54.896200 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 09:00:54.896300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 09:00:54.897200 -- AsyncOperationService processCompletedOperation -- 
2024-06-27 09:00:54.897300 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-27 09:00:54.899600 -- AsyncOperationService finishCompletedOperation -- 
2024-06-27 09:00:54.899700 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-06-27 09:00:54.899700 -- AlientechProjectService processSuccessOperationEncode -- 
2024-06-27 09:00:54.901700 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-27 09:00:57.867300 -- AlientechLinkService __construct -- 
2024-06-27 09:00:57.867500 -- AlientechLinkService processClient -- 
2024-06-27 09:00:57.867600 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 09:00:57.868800 -- initAccessToken -- 
2024-06-27 09:00:57.873000 -- iR8V9-KAuaE -- 
2024-06-27 09:00:57.874600 -- AlientechProjectService __construct -- 
2024-06-27 09:00:57.876400 -- FileSlotService __construct -- 
2024-06-27 09:00:57.877000 -- FileSlotService __construct -- 
2024-06-27 09:00:57.877100 -- AsyncOperationService __construct -- 
2024-06-27 09:00:57.877900 -- AsyncOperationService downloadEncodedFiles -- 
2024-06-27 09:00:57.878500 -- FileService downloadFile -- 
2024-06-27 09:00:57.878600 -- AlientechLinkService processRequest -- 
2024-06-27 09:00:57.878900 -- isAuth -- 
2024-06-27 09:00:57.879000 -- AlientechLinkService isAuth_success -- 
2024-06-27 09:00:59.533000 -- AlientechLinkService processResponse -- 
2024-06-27 09:00:59.549000 --  --- $log->save() --  ---46 -- 
2024-06-27 09:00:59.764600 --  --- $log->save() --  ---47 -- 
2024-06-27 09:00:59.798700 -- FileService saveEncodedFile -- 
2024-06-27 09:00:59.834000 -- FileService $initFile->file_history={"id":"7573","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-06-27 09:00:59.853700 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-06-27 09:39:38.780100 -- AlientechLinkService __construct -- 
2024-06-27 09:39:38.780300 -- AlientechLinkService processClient -- 
2024-06-27 09:39:38.780400 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 09:39:38.780900 -- initAccessToken -- 
2024-06-27 09:39:38.783800 -- iR8V9-KAuaE -- 
2024-06-27 09:39:38.784500 -- AlientechProjectService __construct -- 
2024-06-27 09:39:38.785400 -- FileSlotService __construct -- 
2024-06-27 09:39:38.785700 -- FileSlotService __construct -- 
2024-06-27 09:39:38.785700 -- AsyncOperationService __construct -- 
2024-06-27 09:39:38.786400 -- FileSlotService __construct -- 
2024-06-27 09:39:38.786500 -- Kess3Service __construct -- 
2024-06-27 09:39:38.786500 -- Kess3Service startDecoding -- 
2024-06-27 09:39:38.786600 -- AsyncOperationService startOperationDecode -- 
2024-06-27 09:39:38.786600 -- AlientechLinkService processRequest -- 
2024-06-27 09:39:38.786700 -- isAuth -- 
2024-06-27 09:39:38.786700 -- AlientechLinkService isAuth_success -- 
2024-06-27 09:39:39.330300 -- AlientechLinkService processResponse -- 
2024-06-27 09:39:39.350300 --  --- $log->save() --  ---48 -- 
2024-06-27 09:39:39.351900 --  --- $log->save() --  ---49 -- 
2024-06-27 09:39:39.352000 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 09:39:39.352000 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 09:39:39.357400 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 09:39:41.623500 -- AlientechLinkService __construct -- 
2024-06-27 09:39:41.623700 -- AlientechLinkService processClient -- 
2024-06-27 09:39:41.623800 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 09:39:41.623900 -- initAccessToken -- 
2024-06-27 09:39:41.626400 -- iR8V9-KAuaE -- 
2024-06-27 09:39:41.626700 -- AlientechProjectService __construct -- 
2024-06-27 09:39:41.626800 -- FileSlotService __construct -- 
2024-06-27 09:39:41.626900 -- FileSlotService __construct -- 
2024-06-27 09:39:41.626900 -- AsyncOperationService __construct -- 
2024-06-27 09:39:41.627100 -- ApiController actionKess3Decoded -- 
2024-06-27 09:39:41.637000 --  --- $log->save() --  ---50 -- 
2024-06-27 09:39:41.637200 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 09:39:41.637200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 09:39:41.640500 -- AsyncOperationService processCompletedOperation -- 
2024-06-27 09:39:41.640600 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-27 09:39:41.643300 -- AsyncOperationService finishCompletedOperation -- 
2024-06-27 09:39:41.643400 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-06-27 09:39:41.643400 -- AlientechProjectService processSuccessOperationDecode -- 
2024-06-27 09:39:41.646200 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-27 09:39:43.944700 -- AlientechLinkService __construct -- 
2024-06-27 09:39:43.944900 -- AlientechLinkService processClient -- 
2024-06-27 09:39:43.944900 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 09:39:43.945400 -- initAccessToken -- 
2024-06-27 09:39:43.948500 -- iR8V9-KAuaE -- 
2024-06-27 09:39:43.949600 -- AlientechProjectService __construct -- 
2024-06-27 09:39:43.951500 -- FileSlotService __construct -- 
2024-06-27 09:39:43.952200 -- FileSlotService __construct -- 
2024-06-27 09:39:43.952400 -- AsyncOperationService __construct -- 
2024-06-27 09:39:43.952900 -- AsyncOperationService downloadDecodedFiles -- 
2024-06-27 09:39:43.954200 -- FileService downloadFile -- 
2024-06-27 09:39:43.954400 -- AlientechLinkService processRequest -- 
2024-06-27 09:39:43.954400 -- isAuth -- 
2024-06-27 09:39:43.954500 -- AlientechLinkService isAuth_success -- 
2024-06-27 09:39:44.436400 -- AlientechLinkService processResponse -- 
2024-06-27 09:39:44.462200 --  --- $log->save() --  ---51 -- 
2024-06-27 09:39:44.469500 --  --- $log->save() --  ---52 -- 
2024-06-27 09:39:44.470200 -- FileService saveDecodedFile -- 
2024-06-27 09:39:44.485000 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-27 09:39:44.535900 -- FileService downloadFile -- 
2024-06-27 09:39:44.536100 -- AlientechLinkService processRequest -- 
2024-06-27 09:39:44.536200 -- isAuth -- 
2024-06-27 09:39:44.536300 -- AlientechLinkService isAuth_success -- 
2024-06-27 09:39:45.928200 -- AlientechLinkService processResponse -- 
2024-06-27 09:39:45.930200 --  --- $log->save() --  ---53 -- 
2024-06-27 09:39:46.261400 --  --- $log->save() --  ---54 -- 
2024-06-27 09:39:46.289400 -- FileService saveDecodedFile -- 
2024-06-27 09:39:46.317900 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-27 11:05:34.825800 -- AlientechLinkService __construct -- 
2024-06-27 11:05:34.826100 -- AlientechLinkService processClient -- 
2024-06-27 11:05:34.826100 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 11:05:34.827000 -- initAccessToken -- 
2024-06-27 11:05:34.831100 -- iR8V9-KAuaE -- 
2024-06-27 11:05:34.832600 -- AlientechProjectService __construct -- 
2024-06-27 11:05:34.835200 -- FileSlotService __construct -- 
2024-06-27 11:05:34.835800 -- FileSlotService __construct -- 
2024-06-27 11:05:34.836000 -- AsyncOperationService __construct -- 
2024-06-27 11:05:34.837800 -- FileSlotService __construct -- 
2024-06-27 11:05:34.837900 -- Kess3Service __construct -- 
2024-06-27 11:05:34.838000 -- Kess3Service startDecoding -- 
2024-06-27 11:05:34.838200 -- AsyncOperationService startOperationDecode -- 
2024-06-27 11:05:34.838300 -- AlientechLinkService processRequest -- 
2024-06-27 11:05:34.838400 -- isAuth -- 
2024-06-27 11:05:34.838500 -- AlientechLinkService isAuth_success -- 
2024-06-27 11:05:36.489500 -- AlientechLinkService processResponse -- 
2024-06-27 11:05:36.506400 --  --- $log->save() --  ---55 -- 
2024-06-27 11:05:36.508000 --  --- $log->save() --  ---56 -- 
2024-06-27 11:05:36.508200 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 11:05:36.508200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 11:05:36.513800 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 11:05:38.576900 -- AlientechLinkService __construct -- 
2024-06-27 11:05:38.577200 -- AlientechLinkService processClient -- 
2024-06-27 11:05:38.577300 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 11:05:38.577400 -- initAccessToken -- 
2024-06-27 11:05:38.579300 -- iR8V9-KAuaE -- 
2024-06-27 11:05:38.579800 -- AlientechProjectService __construct -- 
2024-06-27 11:05:38.580300 -- FileSlotService __construct -- 
2024-06-27 11:05:38.580500 -- FileSlotService __construct -- 
2024-06-27 11:05:38.580600 -- AsyncOperationService __construct -- 
2024-06-27 11:05:38.581100 -- ApiController actionKess3Decoded -- 
2024-06-27 11:05:38.588800 --  --- $log->save() --  ---57 -- 
2024-06-27 11:05:38.589000 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 11:05:38.589100 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 11:05:38.594400 -- AsyncOperationService processCompletedOperation -- 
2024-06-27 11:05:38.594500 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-27 11:05:38.597600 -- AsyncOperationService finishCompletedOperation -- 
2024-06-27 11:05:38.597700 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-06-27 11:05:38.597800 -- AlientechProjectService processSuccessOperationDecode -- 
2024-06-27 11:05:38.601800 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-27 11:05:40.586200 -- AlientechLinkService __construct -- 
2024-06-27 11:05:40.586400 -- AlientechLinkService processClient -- 
2024-06-27 11:05:40.586500 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 11:05:40.586900 -- initAccessToken -- 
2024-06-27 11:05:40.590900 -- iR8V9-KAuaE -- 
2024-06-27 11:05:40.592500 -- AlientechProjectService __construct -- 
2024-06-27 11:05:40.595100 -- FileSlotService __construct -- 
2024-06-27 11:05:40.595700 -- FileSlotService __construct -- 
2024-06-27 11:05:40.595800 -- AsyncOperationService __construct -- 
2024-06-27 11:05:40.596500 -- AsyncOperationService downloadDecodedFiles -- 
2024-06-27 11:05:40.598200 -- FileService downloadFile -- 
2024-06-27 11:05:40.598400 -- AlientechLinkService processRequest -- 
2024-06-27 11:05:40.598500 -- isAuth -- 
2024-06-27 11:05:40.598600 -- AlientechLinkService isAuth_success -- 
2024-06-27 11:05:41.444400 -- AlientechLinkService processResponse -- 
2024-06-27 11:05:41.474800 --  --- $log->save() --  ---58 -- 
2024-06-27 11:05:41.533600 --  --- $log->save() --  ---59 -- 
2024-06-27 11:05:41.542900 -- FileService saveDecodedFile -- 
2024-06-27 11:05:41.574800 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-27 11:19:46.423600 -- AlientechLinkService __construct -- 
2024-06-27 11:19:46.423900 -- AlientechLinkService processClient -- 
2024-06-27 11:19:46.423900 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 11:19:46.424600 -- initAccessToken -- 
2024-06-27 11:19:46.428000 -- iR8V9-KAuaE -- 
2024-06-27 11:19:46.429400 -- AlientechProjectService __construct -- 
2024-06-27 11:19:46.432000 -- FileSlotService __construct -- 
2024-06-27 11:19:46.432500 -- FileSlotService __construct -- 
2024-06-27 11:19:46.432600 -- AsyncOperationService __construct -- 
2024-06-27 11:19:46.434200 -- FileSlotService __construct -- 
2024-06-27 11:19:46.434400 -- Kess3Service __construct -- 
2024-06-27 11:19:46.434500 -- Kess3Service startEncoding -- 
2024-06-27 11:19:46.434500 -- AsyncOperationService startOperationEncode -- 
2024-06-27 11:19:46.471200 -- FileService uploadFiles -- 
2024-06-27 11:19:46.471700 -- AlientechLinkService processRequest -- 
2024-06-27 11:19:46.471700 -- isAuth -- 
2024-06-27 11:19:46.471800 -- AlientechLinkService isAuth_success -- 
2024-06-27 11:19:46.978000 -- AlientechLinkService processResponse -- 
2024-06-27 11:19:46.996700 --  --- $log->save() --  ---60 -- 
2024-06-27 11:19:46.998400 --  --- $log->save() --  ---61 -- 
2024-06-27 11:19:47.008700 -- AlientechLinkService processRequest -- 
2024-06-27 11:19:47.008900 -- isAuth -- 
2024-06-27 11:19:47.008900 -- AlientechLinkService isAuth_success -- 
2024-06-27 11:19:47.774200 -- AlientechLinkService processResponse -- 
2024-06-27 11:19:47.776100 --  --- $log->save() --  ---62 -- 
2024-06-27 11:19:47.778400 --  --- $log->save() --  ---63 -- 
2024-06-27 11:19:47.778600 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 11:19:47.778700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 11:19:47.779900 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 11:19:49.656900 -- AlientechLinkService __construct -- 
2024-06-27 11:19:49.657200 -- AlientechLinkService processClient -- 
2024-06-27 11:19:49.657300 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 11:19:49.657400 -- initAccessToken -- 
2024-06-27 11:19:49.659400 -- iR8V9-KAuaE -- 
2024-06-27 11:19:49.659900 -- AlientechProjectService __construct -- 
2024-06-27 11:19:49.660300 -- FileSlotService __construct -- 
2024-06-27 11:19:49.660500 -- FileSlotService __construct -- 
2024-06-27 11:19:49.660600 -- AsyncOperationService __construct -- 
2024-06-27 11:19:49.661000 -- ApiController actionKess3Encoded -- 
2024-06-27 11:19:49.664800 --  --- $log->save() --  ---64 -- 
2024-06-27 11:19:49.665000 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 11:19:49.665100 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 11:19:49.666800 -- AsyncOperationService processCompletedOperation -- 
2024-06-27 11:19:49.666900 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-27 11:19:49.669600 -- AsyncOperationService finishCompletedOperation -- 
2024-06-27 11:19:49.669700 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-06-27 11:19:49.669800 -- AlientechProjectService processSuccessOperationEncode -- 
2024-06-27 11:19:49.673200 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-27 11:19:52.002800 -- AlientechLinkService __construct -- 
2024-06-27 11:19:52.002900 -- AlientechLinkService processClient -- 
2024-06-27 11:19:52.003000 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 11:19:52.003300 -- initAccessToken -- 
2024-06-27 11:19:52.006400 -- iR8V9-KAuaE -- 
2024-06-27 11:19:52.007000 -- AlientechProjectService __construct -- 
2024-06-27 11:19:52.007900 -- FileSlotService __construct -- 
2024-06-27 11:19:52.008000 -- FileSlotService __construct -- 
2024-06-27 11:19:52.008100 -- AsyncOperationService __construct -- 
2024-06-27 11:19:52.008300 -- AsyncOperationService downloadEncodedFiles -- 
2024-06-27 11:19:52.008400 -- FileService downloadFile -- 
2024-06-27 11:19:52.008400 -- AlientechLinkService processRequest -- 
2024-06-27 11:19:52.008400 -- isAuth -- 
2024-06-27 11:19:52.008500 -- AlientechLinkService isAuth_success -- 
2024-06-27 11:19:52.430800 -- AlientechLinkService processResponse -- 
2024-06-27 11:19:52.458600 --  --- $log->save() --  ---65 -- 
2024-06-27 11:19:52.517800 --  --- $log->save() --  ---66 -- 
2024-06-27 11:19:52.530700 -- FileService saveEncodedFile -- 
2024-06-27 11:19:52.567600 -- FileService $initFile->file_history={"id":"7575","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-06-27 11:19:52.589800 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-06-27 11:31:52.282000 -- AlientechLinkService __construct -- 
2024-06-27 11:31:52.282300 -- AlientechLinkService processClient -- 
2024-06-27 11:31:52.282400 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 11:31:52.282900 -- initAccessToken -- 
2024-06-27 11:31:52.287000 -- iR8V9-KAuaE -- 
2024-06-27 11:31:52.288800 -- AlientechProjectService __construct -- 
2024-06-27 11:31:52.292500 -- FileSlotService __construct -- 
2024-06-27 11:31:52.293100 -- FileSlotService __construct -- 
2024-06-27 11:31:52.293200 -- AsyncOperationService __construct -- 
2024-06-27 11:31:52.295200 -- FileSlotService __construct -- 
2024-06-27 11:31:52.295400 -- Kess3Service __construct -- 
2024-06-27 11:31:52.295500 -- Kess3Service startEncoding -- 
2024-06-27 11:31:52.295600 -- AsyncOperationService startOperationEncode -- 
2024-06-27 11:31:52.328800 -- FileService uploadFiles -- 
2024-06-27 11:31:52.329900 -- AlientechLinkService processRequest -- 
2024-06-27 11:31:52.330100 -- isAuth -- 
2024-06-27 11:31:52.330200 -- AlientechLinkService isAuth_success -- 
2024-06-27 11:31:53.303900 -- AlientechLinkService processResponse -- 
2024-06-27 11:31:53.315700 --  --- $log->save() --  ---67 -- 
2024-06-27 11:31:53.317200 --  --- $log->save() --  ---68 -- 
2024-06-27 11:31:53.326100 -- AlientechLinkService processRequest -- 
2024-06-27 11:31:53.326300 -- isAuth -- 
2024-06-27 11:31:53.326300 -- AlientechLinkService isAuth_success -- 
2024-06-27 11:31:54.716500 -- AlientechLinkService processResponse -- 
2024-06-27 11:31:54.718900 --  --- $log->save() --  ---69 -- 
2024-06-27 11:31:54.721400 --  --- $log->save() --  ---70 -- 
2024-06-27 11:31:54.721500 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 11:31:54.721600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 11:31:54.722800 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 11:31:57.363400 -- AlientechLinkService __construct -- 
2024-06-27 11:31:57.363600 -- AlientechLinkService processClient -- 
2024-06-27 11:31:57.363600 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 11:31:57.363700 -- initAccessToken -- 
2024-06-27 11:31:57.365400 -- iR8V9-KAuaE -- 
2024-06-27 11:31:57.365800 -- AlientechProjectService __construct -- 
2024-06-27 11:31:57.366200 -- FileSlotService __construct -- 
2024-06-27 11:31:57.366400 -- FileSlotService __construct -- 
2024-06-27 11:31:57.366400 -- AsyncOperationService __construct -- 
2024-06-27 11:31:57.366800 -- ApiController actionKess3Encoded -- 
2024-06-27 11:31:57.370700 --  --- $log->save() --  ---71 -- 
2024-06-27 11:31:57.370900 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 11:31:57.370900 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 11:31:57.373000 -- AsyncOperationService processCompletedOperation -- 
2024-06-27 11:31:57.373100 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-27 11:31:57.375500 -- AsyncOperationService finishCompletedOperation -- 
2024-06-27 11:31:57.375700 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-06-27 11:31:57.375800 -- AlientechProjectService processSuccessOperationEncode -- 
2024-06-27 11:31:57.378800 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-27 11:31:57.904900 -- AlientechLinkService __construct -- 
2024-06-27 11:31:57.905300 -- AlientechLinkService processClient -- 
2024-06-27 11:31:57.905400 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 11:31:57.906000 -- initAccessToken -- 
2024-06-27 11:31:57.909200 -- iR8V9-KAuaE -- 
2024-06-27 11:31:57.911100 -- AlientechProjectService __construct -- 
2024-06-27 11:31:57.913900 -- FileSlotService __construct -- 
2024-06-27 11:31:57.914500 -- FileSlotService __construct -- 
2024-06-27 11:31:57.914800 -- AsyncOperationService __construct -- 
2024-06-27 11:31:57.915300 -- AsyncOperationService downloadEncodedFiles -- 
2024-06-27 11:31:57.915700 -- FileService downloadFile -- 
2024-06-27 11:31:57.915800 -- AlientechLinkService processRequest -- 
2024-06-27 11:31:57.915800 -- isAuth -- 
2024-06-27 11:31:57.915900 -- AlientechLinkService isAuth_success -- 
2024-06-27 11:31:58.921100 -- AlientechLinkService processResponse -- 
2024-06-27 11:31:58.947100 --  --- $log->save() --  ---72 -- 
2024-06-27 11:31:59.056200 --  --- $log->save() --  ---73 -- 
2024-06-27 11:31:59.071400 -- FileService saveEncodedFile -- 
2024-06-27 11:31:59.105000 -- FileService $initFile->file_history={"id":"7574","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-06-27 11:31:59.128700 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-06-27 11:31:59.173800 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-06-27 16:38:45.310900 -- AlientechLinkService __construct -- 
2024-06-27 16:38:45.311100 -- AlientechLinkService processClient -- 
2024-06-27 16:38:45.311100 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 16:38:45.311600 -- initAccessToken -- 
2024-06-27 16:38:45.316300 -- iR8V9-KAuaE -- 
2024-06-27 16:38:45.318400 -- AlientechProjectService __construct -- 
2024-06-27 16:38:45.321900 -- FileSlotService __construct -- 
2024-06-27 16:38:45.322600 -- FileSlotService __construct -- 
2024-06-27 16:38:45.322800 -- AsyncOperationService __construct -- 
2024-06-27 16:38:45.324500 -- FileSlotService __construct -- 
2024-06-27 16:38:45.324600 -- Kess3Service __construct -- 
2024-06-27 16:38:45.324700 -- Kess3Service startDecoding -- 
2024-06-27 16:38:45.324700 -- AsyncOperationService startOperationDecode -- 
2024-06-27 16:38:45.324800 -- AlientechLinkService processRequest -- 
2024-06-27 16:38:45.324800 -- isAuth -- 
2024-06-27 16:38:45.324900 -- AlientechLinkService isAuth_success -- 
2024-06-27 16:38:46.851700 -- AlientechLinkService processResponse -- 
2024-06-27 16:38:46.874600 --  --- $log->save() --  ---74 -- 
2024-06-27 16:38:46.874800 -- response_status=429 -- 
2024-06-27 16:38:46.875000 -- response=Http-Code: 429
Content-Type: text/plain; charset=utf-8
Retry-After: 0
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Thu, 27 Jun 2024 16:38:46 GMT
Connection: close

TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-06-27 16:38:46.875100 -- content=TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-06-27 16:38:46.875200 -- FileSlotService closeAllFileSlots -- 
2024-06-27 16:38:46.875300 -- FileSlotService getSlots -- 
2024-06-27 16:38:46.875300 -- AlientechLinkService processRequest -- 
2024-06-27 16:38:46.875400 -- isAuth -- 
2024-06-27 16:38:46.875500 -- AlientechLinkService isAuth_success -- 
2024-06-27 16:38:47.052700 -- AlientechLinkService processResponse -- 
2024-06-27 16:38:47.057600 --  --- $log->save() --  ---75 -- 
2024-06-27 16:38:47.067100 --  --- $log->save() --  ---76 -- 
2024-06-27 16:38:47.067800 -- slot [d7cfdbd8-9b55-4af5-a130-101e54e38824] dateDiff {"y":0,"m":0,"d":0,"h":5,"i":33,"s":10,"f":0.651054,"invert":1,"days":0,"from_string":false} -- 
2024-06-27 16:38:47.068000 -- FileSlotService closeSlot -- 
2024-06-27 16:38:47.068000 -- /api/kess3/file-slots/d7cfdbd8-9b55-4af5-a130-101e54e38824/close  -- 
2024-06-27 16:38:47.068100 -- AlientechLinkService processRequest -- 
2024-06-27 16:38:47.068300 -- isAuth -- 
2024-06-27 16:38:47.068500 -- AlientechLinkService isAuth_success -- 
2024-06-27 16:38:47.333300 -- AlientechLinkService processResponse -- 
2024-06-27 16:38:47.336200 --  --- $log->save() --  ---77 -- 
2024-06-27 16:38:47.338600 --  --- $log->save() --  ---78 -- 
2024-06-27 16:38:47.338800 -- slot [d7cfdbd8-9b55-4af5-a130-101e54e38824] closeSlot null -- 
2024-06-27 16:38:47.338900 -- slot [89b55edf-8457-432c-a8f1-82b932e0ec3a] dateDiff {"y":0,"m":0,"d":0,"h":6,"i":59,"s":8,"f":0.085556,"invert":1,"days":0,"from_string":false} -- 
2024-06-27 16:38:47.339000 -- FileSlotService closeSlot -- 
2024-06-27 16:38:47.339000 -- /api/kess3/file-slots/89b55edf-8457-432c-a8f1-82b932e0ec3a/close  -- 
2024-06-27 16:38:47.339100 -- AlientechLinkService processRequest -- 
2024-06-27 16:38:47.339100 -- isAuth -- 
2024-06-27 16:38:47.339200 -- AlientechLinkService isAuth_success -- 
2024-06-27 16:38:47.591500 -- AlientechLinkService processResponse -- 
2024-06-27 16:38:47.594200 --  --- $log->save() --  ---79 -- 
2024-06-27 16:38:47.596500 --  --- $log->save() --  ---80 -- 
2024-06-27 16:38:47.596800 -- slot [89b55edf-8457-432c-a8f1-82b932e0ec3a] closeSlot null -- 
2024-06-27 16:38:47.596900 -- slot [c795d255-3f89-48cd-9485-056dadc59d43] dateDiff {"y":0,"m":0,"d":0,"h":7,"i":59,"s":16,"f":0.320201,"invert":1,"days":0,"from_string":false} -- 
2024-06-27 16:38:47.597000 -- FileSlotService closeSlot -- 
2024-06-27 16:38:47.597000 -- /api/kess3/file-slots/c795d255-3f89-48cd-9485-056dadc59d43/close  -- 
2024-06-27 16:38:47.597100 -- AlientechLinkService processRequest -- 
2024-06-27 16:38:47.597200 -- isAuth -- 
2024-06-27 16:38:47.597200 -- AlientechLinkService isAuth_success -- 
2024-06-27 16:38:47.870500 -- AlientechLinkService processResponse -- 
2024-06-27 16:38:47.873600 --  --- $log->save() --  ---81 -- 
2024-06-27 16:38:47.875800 --  --- $log->save() --  ---82 -- 
2024-06-27 16:38:47.875900 -- slot [c795d255-3f89-48cd-9485-056dadc59d43] closeSlot null -- 
2024-06-27 16:38:47.876300 -- closeAllSlots finish -- 
2024-06-27 16:38:47.876500 -- AsyncOperationService startOperationDecode -- 
2024-06-27 16:38:47.876700 -- AlientechLinkService processRequest -- 
2024-06-27 16:38:47.876800 -- isAuth -- 
2024-06-27 16:38:47.877000 -- AlientechLinkService isAuth_success -- 
2024-06-27 16:38:48.203100 -- AlientechLinkService processResponse -- 
2024-06-27 16:38:48.205900 --  --- $log->save() --  ---83 -- 
2024-06-27 16:38:48.208600 --  --- $log->save() --  ---84 -- 
2024-06-27 16:38:48.208800 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 16:38:48.208900 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 16:38:48.222800 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 16:38:50.529500 -- AlientechLinkService __construct -- 
2024-06-27 16:38:50.529700 -- AlientechLinkService processClient -- 
2024-06-27 16:38:50.529700 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 16:38:50.529800 -- initAccessToken -- 
2024-06-27 16:38:50.530300 -- iR8V9-KAuaE -- 
2024-06-27 16:38:50.530400 -- AlientechProjectService __construct -- 
2024-06-27 16:38:50.530600 -- FileSlotService __construct -- 
2024-06-27 16:38:50.530600 -- FileSlotService __construct -- 
2024-06-27 16:38:50.530700 -- AsyncOperationService __construct -- 
2024-06-27 16:38:50.530900 -- ApiController actionKess3Decoded -- 
2024-06-27 16:38:50.537100 --  --- $log->save() --  ---85 -- 
2024-06-27 16:38:50.537200 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 16:38:50.537200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 16:38:50.541300 -- AsyncOperationService processCompletedOperation -- 
2024-06-27 16:38:50.541400 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-27 16:38:50.543900 -- AsyncOperationService finishCompletedOperation -- 
2024-06-27 16:38:50.544000 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-06-27 16:38:50.544100 -- AlientechProjectService processSuccessOperationDecode -- 
2024-06-27 16:38:50.546900 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-27 16:38:51.584300 -- AlientechLinkService __construct -- 
2024-06-27 16:38:51.584400 -- AlientechLinkService processClient -- 
2024-06-27 16:38:51.584400 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 16:38:51.584800 -- initAccessToken -- 
2024-06-27 16:38:51.587700 -- iR8V9-KAuaE -- 
2024-06-27 16:38:51.589800 -- AlientechProjectService __construct -- 
2024-06-27 16:38:51.592600 -- FileSlotService __construct -- 
2024-06-27 16:38:51.593600 -- FileSlotService __construct -- 
2024-06-27 16:38:51.594100 -- AsyncOperationService __construct -- 
2024-06-27 16:38:51.594400 -- AsyncOperationService downloadDecodedFiles -- 
2024-06-27 16:38:51.595000 -- FileService downloadFile -- 
2024-06-27 16:38:51.595100 -- AlientechLinkService processRequest -- 
2024-06-27 16:38:51.595100 -- isAuth -- 
2024-06-27 16:38:51.595200 -- AlientechLinkService isAuth_success -- 
2024-06-27 16:38:52.106400 -- AlientechLinkService processResponse -- 
2024-06-27 16:38:52.131900 --  --- $log->save() --  ---86 -- 
2024-06-27 16:38:52.140600 --  --- $log->save() --  ---87 -- 
2024-06-27 16:38:52.141500 -- FileService saveDecodedFile -- 
2024-06-27 16:38:52.164500 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-27 16:38:52.221200 -- FileService downloadFile -- 
2024-06-27 16:38:52.221300 -- AlientechLinkService processRequest -- 
2024-06-27 16:38:52.221300 -- isAuth -- 
2024-06-27 16:38:52.221400 -- AlientechLinkService isAuth_success -- 
2024-06-27 16:38:53.262400 -- AlientechLinkService processResponse -- 
2024-06-27 16:38:53.264700 --  --- $log->save() --  ---88 -- 
2024-06-27 16:38:53.429100 --  --- $log->save() --  ---89 -- 
2024-06-27 16:38:53.454100 -- FileService saveDecodedFile -- 
2024-06-27 16:38:53.470100 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-27 18:57:11.240600 -- AlientechLinkService __construct -- 
2024-06-27 18:57:11.240900 -- AlientechLinkService processClient -- 
2024-06-27 18:57:11.241000 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 18:57:11.241600 -- initAccessToken -- 
2024-06-27 18:57:11.245400 -- iR8V9-KAuaE -- 
2024-06-27 18:57:11.246300 -- AlientechProjectService __construct -- 
2024-06-27 18:57:11.247800 -- FileSlotService __construct -- 
2024-06-27 18:57:11.248200 -- FileSlotService __construct -- 
2024-06-27 18:57:11.248300 -- AsyncOperationService __construct -- 
2024-06-27 18:57:11.249200 -- FileSlotService __construct -- 
2024-06-27 18:57:11.249300 -- Kess3Service __construct -- 
2024-06-27 18:57:11.249300 -- Kess3Service startEncoding -- 
2024-06-27 18:57:11.249300 -- AsyncOperationService startOperationEncode -- 
2024-06-27 18:57:11.278700 -- FileService uploadFiles -- 
2024-06-27 18:57:11.279400 -- AlientechLinkService processRequest -- 
2024-06-27 18:57:11.279500 -- isAuth -- 
2024-06-27 18:57:11.279500 -- AlientechLinkService isAuth_success -- 
2024-06-27 18:57:12.898700 -- AlientechLinkService processResponse -- 
2024-06-27 18:57:12.910500 --  --- $log->save() --  ---90 -- 
2024-06-27 18:57:12.912700 --  --- $log->save() --  ---91 -- 
2024-06-27 18:57:12.923800 -- AlientechLinkService processRequest -- 
2024-06-27 18:57:12.924000 -- isAuth -- 
2024-06-27 18:57:12.924100 -- AlientechLinkService isAuth_success -- 
2024-06-27 18:57:14.237800 -- AlientechLinkService processResponse -- 
2024-06-27 18:57:14.240800 --  --- $log->save() --  ---92 -- 
2024-06-27 18:57:14.244900 --  --- $log->save() --  ---93 -- 
2024-06-27 18:57:14.245200 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 18:57:14.245300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 18:57:14.246700 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 18:57:16.563400 -- AlientechLinkService __construct -- 
2024-06-27 18:57:16.563800 -- AlientechLinkService processClient -- 
2024-06-27 18:57:16.563900 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 18:57:16.564100 -- initAccessToken -- 
2024-06-27 18:57:16.564700 -- iR8V9-KAuaE -- 
2024-06-27 18:57:16.565000 -- AlientechProjectService __construct -- 
2024-06-27 18:57:16.565200 -- FileSlotService __construct -- 
2024-06-27 18:57:16.565400 -- FileSlotService __construct -- 
2024-06-27 18:57:16.565400 -- AsyncOperationService __construct -- 
2024-06-27 18:57:16.565700 -- ApiController actionKess3Encoded -- 
2024-06-27 18:57:16.571400 --  --- $log->save() --  ---94 -- 
2024-06-27 18:57:16.571700 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 18:57:16.571800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 18:57:16.576000 -- AsyncOperationService processCompletedOperation -- 
2024-06-27 18:57:16.576200 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-27 18:57:16.578900 -- AsyncOperationService finishCompletedOperation -- 
2024-06-27 18:57:16.579000 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-06-27 18:57:16.579100 -- AlientechProjectService processSuccessOperationEncode -- 
2024-06-27 18:57:16.582400 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-27 18:57:18.180300 -- AlientechLinkService __construct -- 
2024-06-27 18:57:18.180500 -- AlientechLinkService processClient -- 
2024-06-27 18:57:18.180500 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 18:57:18.180900 -- initAccessToken -- 
2024-06-27 18:57:18.183400 -- iR8V9-KAuaE -- 
2024-06-27 18:57:18.184000 -- AlientechProjectService __construct -- 
2024-06-27 18:57:18.185100 -- FileSlotService __construct -- 
2024-06-27 18:57:18.185300 -- FileSlotService __construct -- 
2024-06-27 18:57:18.185300 -- AsyncOperationService __construct -- 
2024-06-27 18:57:18.185600 -- AsyncOperationService downloadEncodedFiles -- 
2024-06-27 18:57:18.185800 -- FileService downloadFile -- 
2024-06-27 18:57:18.185900 -- AlientechLinkService processRequest -- 
2024-06-27 18:57:18.185900 -- isAuth -- 
2024-06-27 18:57:18.186000 -- AlientechLinkService isAuth_success -- 
2024-06-27 18:57:18.789700 -- AlientechLinkService processResponse -- 
2024-06-27 18:57:18.816400 --  --- $log->save() --  ---95 -- 
2024-06-27 18:57:18.877500 --  --- $log->save() --  ---96 -- 
2024-06-27 18:57:18.889100 -- FileService saveEncodedFile -- 
2024-06-27 18:57:18.924000 -- FileService $initFile->file_history={"id":"7579","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-06-27 18:57:18.943300 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-06-27 18:57:18.971100 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-06-27 18:57:24.244100 -- AlientechLinkService __construct -- 
2024-06-27 18:57:24.244300 -- AlientechLinkService processClient -- 
2024-06-27 18:57:24.244400 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 18:57:24.244900 -- initAccessToken -- 
2024-06-27 18:57:24.247700 -- iR8V9-KAuaE -- 
2024-06-27 18:57:24.248700 -- AlientechProjectService __construct -- 
2024-06-27 18:57:24.249900 -- FileSlotService __construct -- 
2024-06-27 18:57:24.250200 -- FileSlotService __construct -- 
2024-06-27 18:57:24.250300 -- AsyncOperationService __construct -- 
2024-06-27 18:57:24.251000 -- FileSlotService __construct -- 
2024-06-27 18:57:24.251100 -- Kess3Service __construct -- 
2024-06-27 18:57:24.251100 -- Kess3Service startEncoding -- 
2024-06-27 18:57:24.251400 -- AsyncOperationService startOperationEncode -- 
2024-06-27 18:57:24.271700 -- FileService uploadFiles -- 
2024-06-27 18:57:24.272500 -- AlientechLinkService processRequest -- 
2024-06-27 18:57:24.272600 -- isAuth -- 
2024-06-27 18:57:24.273000 -- AlientechLinkService isAuth_success -- 
2024-06-27 18:57:24.948100 -- AlientechLinkService processResponse -- 
2024-06-27 18:57:24.960300 --  --- $log->save() --  ---97 -- 
2024-06-27 18:57:24.962200 --  --- $log->save() --  ---98 -- 
2024-06-27 18:57:24.970200 -- AlientechLinkService processRequest -- 
2024-06-27 18:57:24.970300 -- isAuth -- 
2024-06-27 18:57:24.970400 -- AlientechLinkService isAuth_success -- 
2024-06-27 18:57:26.199100 -- AlientechLinkService processResponse -- 
2024-06-27 18:57:26.201000 --  --- $log->save() --  ---99 -- 
2024-06-27 18:57:26.203500 --  --- $log->save() --  ---100 -- 
2024-06-27 18:57:26.203800 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 18:57:26.203900 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 18:57:26.205200 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 18:57:28.715700 -- AlientechLinkService __construct -- 
2024-06-27 18:57:28.715800 -- AlientechLinkService processClient -- 
2024-06-27 18:57:28.715800 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 18:57:28.715900 -- initAccessToken -- 
2024-06-27 18:57:28.716800 -- iR8V9-KAuaE -- 
2024-06-27 18:57:28.717000 -- AlientechProjectService __construct -- 
2024-06-27 18:57:28.717100 -- FileSlotService __construct -- 
2024-06-27 18:57:28.717100 -- FileSlotService __construct -- 
2024-06-27 18:57:28.717100 -- AsyncOperationService __construct -- 
2024-06-27 18:57:28.717200 -- ApiController actionKess3Encoded -- 
2024-06-27 18:57:28.719100 --  --- $log->save() --  ---101 -- 
2024-06-27 18:57:28.719200 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 18:57:28.719200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 18:57:28.720100 -- AsyncOperationService processCompletedOperation -- 
2024-06-27 18:57:28.720200 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-27 18:57:28.721600 -- AsyncOperationService finishCompletedOperation -- 
2024-06-27 18:57:28.721700 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-06-27 18:57:28.721700 -- AlientechProjectService processSuccessOperationEncode -- 
2024-06-27 18:57:28.723600 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-27 18:57:29.647100 -- AlientechLinkService __construct -- 
2024-06-27 18:57:29.647400 -- AlientechLinkService processClient -- 
2024-06-27 18:57:29.647400 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 18:57:29.647900 -- initAccessToken -- 
2024-06-27 18:57:29.651000 -- iR8V9-KAuaE -- 
2024-06-27 18:57:29.651600 -- AlientechProjectService __construct -- 
2024-06-27 18:57:29.652300 -- FileSlotService __construct -- 
2024-06-27 18:57:29.652500 -- FileSlotService __construct -- 
2024-06-27 18:57:29.652500 -- AsyncOperationService __construct -- 
2024-06-27 18:57:29.652700 -- AsyncOperationService downloadEncodedFiles -- 
2024-06-27 18:57:29.652800 -- FileService downloadFile -- 
2024-06-27 18:57:29.652800 -- AlientechLinkService processRequest -- 
2024-06-27 18:57:29.652900 -- isAuth -- 
2024-06-27 18:57:29.652900 -- AlientechLinkService isAuth_success -- 
2024-06-27 18:57:30.079500 -- AlientechLinkService processResponse -- 
2024-06-27 18:57:30.104000 --  --- $log->save() --  ---102 -- 
2024-06-27 18:57:30.176500 --  --- $log->save() --  ---103 -- 
2024-06-27 18:57:30.182600 -- FileService saveEncodedFile -- 
2024-06-27 18:57:30.212700 -- FileService $initFile->file_history={"id":"7579","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-06-27 18:57:30.232700 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-06-27 18:57:30.258300 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-06-27 18:57:59.768300 -- AlientechLinkService __construct -- 
2024-06-27 18:57:59.768500 -- AlientechLinkService processClient -- 
2024-06-27 18:57:59.768600 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 18:57:59.769000 -- initAccessToken -- 
2024-06-27 18:57:59.772200 -- iR8V9-KAuaE -- 
2024-06-27 18:57:59.773600 -- AlientechProjectService __construct -- 
2024-06-27 18:57:59.776000 -- FileSlotService __construct -- 
2024-06-27 18:57:59.776400 -- FileSlotService __construct -- 
2024-06-27 18:57:59.776500 -- AsyncOperationService __construct -- 
2024-06-27 18:57:59.777900 -- FileSlotService __construct -- 
2024-06-27 18:57:59.778000 -- Kess3Service __construct -- 
2024-06-27 18:57:59.778200 -- Kess3Service startEncoding -- 
2024-06-27 18:57:59.778300 -- AsyncOperationService startOperationEncode -- 
2024-06-27 18:57:59.816400 -- FileService uploadFiles -- 
2024-06-27 18:57:59.816900 -- AlientechLinkService processRequest -- 
2024-06-27 18:57:59.817000 -- isAuth -- 
2024-06-27 18:57:59.817100 -- AlientechLinkService isAuth_success -- 
2024-06-27 18:58:00.577700 -- AlientechLinkService processResponse -- 
2024-06-27 18:58:00.588800 --  --- $log->save() --  ---104 -- 
2024-06-27 18:58:00.590600 --  --- $log->save() --  ---105 -- 
2024-06-27 18:58:00.602600 -- AlientechLinkService processRequest -- 
2024-06-27 18:58:00.602700 -- isAuth -- 
2024-06-27 18:58:00.602800 -- AlientechLinkService isAuth_success -- 
2024-06-27 18:58:01.842100 -- AlientechLinkService processResponse -- 
2024-06-27 18:58:01.844100 --  --- $log->save() --  ---106 -- 
2024-06-27 18:58:01.849600 --  --- $log->save() --  ---107 -- 
2024-06-27 18:58:01.849700 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 18:58:01.849800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 18:58:01.850800 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-27 18:58:03.960200 -- AlientechLinkService __construct -- 
2024-06-27 18:58:03.960300 -- AlientechLinkService processClient -- 
2024-06-27 18:58:03.960400 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 18:58:03.960400 -- initAccessToken -- 
2024-06-27 18:58:03.962000 -- iR8V9-KAuaE -- 
2024-06-27 18:58:03.962400 -- AlientechProjectService __construct -- 
2024-06-27 18:58:03.962800 -- FileSlotService __construct -- 
2024-06-27 18:58:03.963000 -- FileSlotService __construct -- 
2024-06-27 18:58:03.963200 -- AsyncOperationService __construct -- 
2024-06-27 18:58:03.963600 -- ApiController actionKess3Encoded -- 
2024-06-27 18:58:03.968200 --  --- $log->save() --  ---108 -- 
2024-06-27 18:58:03.968400 -- AsyncOperationService createOperationDtoByData -- 
2024-06-27 18:58:03.968500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-27 18:58:03.970400 -- AsyncOperationService processCompletedOperation -- 
2024-06-27 18:58:03.970500 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-27 18:58:03.972900 -- AsyncOperationService finishCompletedOperation -- 
2024-06-27 18:58:03.973000 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-06-27 18:58:03.973000 -- AlientechProjectService processSuccessOperationEncode -- 
2024-06-27 18:58:03.975700 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-27 18:58:04.922800 -- AlientechLinkService __construct -- 
2024-06-27 18:58:04.923000 -- AlientechLinkService processClient -- 
2024-06-27 18:58:04.923000 -- AlientechLinkService is_null($this->client) -- 
2024-06-27 18:58:04.923500 -- initAccessToken -- 
2024-06-27 18:58:04.926700 -- iR8V9-KAuaE -- 
2024-06-27 18:58:04.928600 -- AlientechProjectService __construct -- 
2024-06-27 18:58:04.932400 -- FileSlotService __construct -- 
2024-06-27 18:58:04.933400 -- FileSlotService __construct -- 
2024-06-27 18:58:04.933500 -- AsyncOperationService __construct -- 
2024-06-27 18:58:04.934600 -- AsyncOperationService downloadEncodedFiles -- 
2024-06-27 18:58:04.935500 -- FileService downloadFile -- 
2024-06-27 18:58:04.935600 -- AlientechLinkService processRequest -- 
2024-06-27 18:58:04.935600 -- isAuth -- 
2024-06-27 18:58:04.936000 -- AlientechLinkService isAuth_success -- 
2024-06-27 18:58:05.762100 -- AlientechLinkService processResponse -- 
2024-06-27 18:58:05.803200 --  --- $log->save() --  ---109 -- 
2024-06-27 18:58:05.879400 --  --- $log->save() --  ---110 -- 
2024-06-27 18:58:05.886900 -- FileService saveEncodedFile -- 
2024-06-27 18:58:05.914500 -- FileService $initFile->file_history={"id":"7579","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-06-27 18:58:05.934400 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-06-27 18:58:05.976500 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-06-28 09:13:21.459200 -- AlientechLinkService __construct -- 
2024-06-28 09:13:21.459500 -- AlientechLinkService processClient -- 
2024-06-28 09:13:21.459500 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 09:13:21.459800 -- initAccessToken -- 
2024-06-28 09:13:21.462500 -- iR8V9-KAuaE -- 
2024-06-28 09:13:21.463100 -- AlientechProjectService __construct -- 
2024-06-28 09:13:21.463900 -- FileSlotService __construct -- 
2024-06-28 09:13:21.464000 -- FileSlotService __construct -- 
2024-06-28 09:13:21.464100 -- AsyncOperationService __construct -- 
2024-06-28 09:13:21.464600 -- FileSlotService __construct -- 
2024-06-28 09:13:21.464600 -- Kess3Service __construct -- 
2024-06-28 09:13:21.464600 -- Kess3Service startDecoding -- 
2024-06-28 09:13:21.464600 -- AsyncOperationService startOperationDecode -- 
2024-06-28 09:13:21.464700 -- AlientechLinkService processRequest -- 
2024-06-28 09:13:21.464700 -- isAuth -- 
2024-06-28 09:13:21.464700 -- AlientechLinkService isAuth_success -- 
2024-06-28 09:13:23.450500 -- AlientechLinkService processResponse -- 
2024-06-28 09:13:23.473700 --  --- $log->save() --  ---111 -- 
2024-06-28 09:13:23.476100 --  --- $log->save() --  ---112 -- 
2024-06-28 09:13:23.476300 -- AsyncOperationService createOperationDtoByData -- 
2024-06-28 09:13:23.476400 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-28 09:13:23.487800 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-28 09:13:25.877400 -- AlientechLinkService __construct -- 
2024-06-28 09:13:25.877600 -- AlientechLinkService processClient -- 
2024-06-28 09:13:25.877700 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 09:13:25.877800 -- initAccessToken -- 
2024-06-28 09:13:25.880000 -- iR8V9-KAuaE -- 
2024-06-28 09:13:25.880400 -- AlientechProjectService __construct -- 
2024-06-28 09:13:25.880700 -- FileSlotService __construct -- 
2024-06-28 09:13:25.880800 -- FileSlotService __construct -- 
2024-06-28 09:13:25.880900 -- AsyncOperationService __construct -- 
2024-06-28 09:13:25.881200 -- ApiController actionKess3Decoded -- 
2024-06-28 09:13:25.887900 --  --- $log->save() --  ---113 -- 
2024-06-28 09:13:25.888000 -- AsyncOperationService createOperationDtoByData -- 
2024-06-28 09:13:25.888100 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-28 09:13:25.893500 -- AsyncOperationService processCompletedOperation -- 
2024-06-28 09:13:25.893700 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-28 09:13:25.896400 -- AsyncOperationService finishCompletedOperation -- 
2024-06-28 09:13:25.896500 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-06-28 09:13:25.896500 -- AlientechProjectService processSuccessOperationDecode -- 
2024-06-28 09:13:25.899800 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-28 09:13:26.670900 -- AlientechLinkService __construct -- 
2024-06-28 09:13:26.671000 -- AlientechLinkService processClient -- 
2024-06-28 09:13:26.671000 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 09:13:26.671400 -- initAccessToken -- 
2024-06-28 09:13:26.674200 -- iR8V9-KAuaE -- 
2024-06-28 09:13:26.674700 -- AlientechProjectService __construct -- 
2024-06-28 09:13:26.675500 -- FileSlotService __construct -- 
2024-06-28 09:13:26.675600 -- FileSlotService __construct -- 
2024-06-28 09:13:26.675600 -- AsyncOperationService __construct -- 
2024-06-28 09:13:26.675800 -- AsyncOperationService downloadDecodedFiles -- 
2024-06-28 09:13:26.676200 -- FileService downloadFile -- 
2024-06-28 09:13:26.676200 -- AlientechLinkService processRequest -- 
2024-06-28 09:13:26.676300 -- isAuth -- 
2024-06-28 09:13:26.676300 -- AlientechLinkService isAuth_success -- 
2024-06-28 09:13:27.101700 -- AlientechLinkService processResponse -- 
2024-06-28 09:13:27.129300 --  --- $log->save() --  ---114 -- 
2024-06-28 09:13:27.148600 --  --- $log->save() --  ---115 -- 
2024-06-28 09:13:27.150100 -- FileService saveDecodedFile -- 
2024-06-28 09:13:27.167700 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-28 09:13:27.219600 -- FileService downloadFile -- 
2024-06-28 09:13:27.219700 -- AlientechLinkService processRequest -- 
2024-06-28 09:13:27.219800 -- isAuth -- 
2024-06-28 09:13:27.219800 -- AlientechLinkService isAuth_success -- 
2024-06-28 09:13:27.761900 -- AlientechLinkService processResponse -- 
2024-06-28 09:13:27.765600 --  --- $log->save() --  ---116 -- 
2024-06-28 09:13:27.990400 --  --- $log->save() --  ---117 -- 
2024-06-28 09:13:28.008300 -- FileService saveDecodedFile -- 
2024-06-28 09:13:28.033700 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-28 10:04:52.225200 -- AlientechLinkService __construct -- 
2024-06-28 10:04:52.225400 -- AlientechLinkService processClient -- 
2024-06-28 10:04:52.225500 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 10:04:52.225800 -- initAccessToken -- 
2024-06-28 10:04:52.228900 -- iR8V9-KAuaE -- 
2024-06-28 10:04:52.229400 -- AlientechProjectService __construct -- 
2024-06-28 10:04:52.230300 -- FileSlotService __construct -- 
2024-06-28 10:04:52.230500 -- FileSlotService __construct -- 
2024-06-28 10:04:52.230600 -- AsyncOperationService __construct -- 
2024-06-28 10:04:52.231100 -- FileSlotService __construct -- 
2024-06-28 10:04:52.231200 -- Kess3Service __construct -- 
2024-06-28 10:04:52.231200 -- Kess3Service startEncoding -- 
2024-06-28 10:04:52.231200 -- AsyncOperationService startOperationEncode -- 
2024-06-28 10:04:52.255000 -- FileService uploadFiles -- 
2024-06-28 10:04:52.255600 -- AlientechLinkService processRequest -- 
2024-06-28 10:04:52.255700 -- isAuth -- 
2024-06-28 10:04:52.255700 -- AlientechLinkService isAuth_success -- 
2024-06-28 10:04:54.351000 -- AlientechLinkService processResponse -- 
2024-06-28 10:04:54.365000 --  --- $log->save() --  ---118 -- 
2024-06-28 10:04:54.367000 --  --- $log->save() --  ---119 -- 
2024-06-28 10:04:54.376900 -- AlientechLinkService processRequest -- 
2024-06-28 10:04:54.377100 -- isAuth -- 
2024-06-28 10:04:54.377200 -- AlientechLinkService isAuth_success -- 
2024-06-28 10:04:55.691300 -- AlientechLinkService processResponse -- 
2024-06-28 10:04:55.694200 --  --- $log->save() --  ---120 -- 
2024-06-28 10:04:55.696400 --  --- $log->save() --  ---121 -- 
2024-06-28 10:04:55.696700 -- AsyncOperationService createOperationDtoByData -- 
2024-06-28 10:04:55.696800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-28 10:04:55.698300 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-28 10:04:58.796100 -- AlientechLinkService __construct -- 
2024-06-28 10:04:58.796600 -- AlientechLinkService processClient -- 
2024-06-28 10:04:58.796700 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 10:04:58.796800 -- initAccessToken -- 
2024-06-28 10:04:58.797500 -- iR8V9-KAuaE -- 
2024-06-28 10:04:58.797700 -- AlientechProjectService __construct -- 
2024-06-28 10:04:58.798000 -- FileSlotService __construct -- 
2024-06-28 10:04:58.798200 -- FileSlotService __construct -- 
2024-06-28 10:04:58.798200 -- AsyncOperationService __construct -- 
2024-06-28 10:04:58.798500 -- ApiController actionKess3Encoded -- 
2024-06-28 10:04:58.802000 --  --- $log->save() --  ---122 -- 
2024-06-28 10:04:58.802300 -- AsyncOperationService createOperationDtoByData -- 
2024-06-28 10:04:58.802400 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-28 10:04:58.804500 -- AsyncOperationService processCompletedOperation -- 
2024-06-28 10:04:58.804600 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-28 10:04:58.807700 -- AsyncOperationService finishCompletedOperation -- 
2024-06-28 10:04:58.807800 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-06-28 10:04:58.807900 -- AlientechProjectService processSuccessOperationEncode -- 
2024-06-28 10:04:58.810900 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-28 10:04:58.971700 -- AlientechLinkService __construct -- 
2024-06-28 10:04:58.971900 -- AlientechLinkService processClient -- 
2024-06-28 10:04:58.972000 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 10:04:58.972500 -- initAccessToken -- 
2024-06-28 10:04:58.974900 -- iR8V9-KAuaE -- 
2024-06-28 10:04:58.975600 -- AlientechProjectService __construct -- 
2024-06-28 10:04:58.976600 -- FileSlotService __construct -- 
2024-06-28 10:04:58.976900 -- FileSlotService __construct -- 
2024-06-28 10:04:58.976900 -- AsyncOperationService __construct -- 
2024-06-28 10:04:58.977200 -- AsyncOperationService downloadEncodedFiles -- 
2024-06-28 10:04:58.977500 -- FileService downloadFile -- 
2024-06-28 10:04:58.977500 -- AlientechLinkService processRequest -- 
2024-06-28 10:04:58.977600 -- isAuth -- 
2024-06-28 10:04:58.977600 -- AlientechLinkService isAuth_success -- 
2024-06-28 10:04:59.808500 -- AlientechLinkService processResponse -- 
2024-06-28 10:04:59.826200 --  --- $log->save() --  ---123 -- 
2024-06-28 10:04:59.928100 --  --- $log->save() --  ---124 -- 
2024-06-28 10:04:59.952300 -- FileService saveEncodedFile -- 
2024-06-28 10:04:59.985400 -- FileService $initFile->file_history={"id":"7581","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-06-28 10:05:00.005500 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-06-28 10:05:00.051800 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-06-28 10:27:31.280600 -- AlientechLinkService __construct -- 
2024-06-28 10:27:31.281200 -- AlientechLinkService processClient -- 
2024-06-28 10:27:31.281400 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 10:27:31.282000 -- initAccessToken -- 
2024-06-28 10:27:31.284600 -- iR8V9-KAuaE -- 
2024-06-28 10:27:31.285400 -- AlientechProjectService __construct -- 
2024-06-28 10:27:31.286400 -- FileSlotService __construct -- 
2024-06-28 10:27:31.286800 -- FileSlotService __construct -- 
2024-06-28 10:27:31.287100 -- AsyncOperationService __construct -- 
2024-06-28 10:27:31.287700 -- FileSlotService __construct -- 
2024-06-28 10:27:31.288000 -- Kess3Service __construct -- 
2024-06-28 10:27:31.288200 -- Kess3Service startDecoding -- 
2024-06-28 10:27:31.288400 -- AsyncOperationService startOperationDecode -- 
2024-06-28 10:27:31.288600 -- AlientechLinkService processRequest -- 
2024-06-28 10:27:31.288800 -- isAuth -- 
2024-06-28 10:27:31.289000 -- AlientechLinkService isAuth_success -- 
2024-06-28 10:27:33.324800 -- AlientechLinkService processResponse -- 
2024-06-28 10:27:33.347200 --  --- $log->save() --  ---125 -- 
2024-06-28 10:27:33.349200 --  --- $log->save() --  ---126 -- 
2024-06-28 10:27:33.349400 -- AsyncOperationService createOperationDtoByData -- 
2024-06-28 10:27:33.349500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-28 10:27:33.358100 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-28 10:27:35.440300 -- AlientechLinkService __construct -- 
2024-06-28 10:27:35.440500 -- AlientechLinkService processClient -- 
2024-06-28 10:27:35.440600 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 10:27:35.440700 -- initAccessToken -- 
2024-06-28 10:27:35.441400 -- iR8V9-KAuaE -- 
2024-06-28 10:27:35.441500 -- AlientechProjectService __construct -- 
2024-06-28 10:27:35.441700 -- FileSlotService __construct -- 
2024-06-28 10:27:35.441800 -- FileSlotService __construct -- 
2024-06-28 10:27:35.441800 -- AsyncOperationService __construct -- 
2024-06-28 10:27:35.442000 -- ApiController actionKess3Decoded -- 
2024-06-28 10:27:35.447500 --  --- $log->save() --  ---127 -- 
2024-06-28 10:27:35.447700 -- AsyncOperationService createOperationDtoByData -- 
2024-06-28 10:27:35.447800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-28 10:27:35.452900 -- AsyncOperationService processCompletedOperation -- 
2024-06-28 10:27:35.453100 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-28 10:27:35.456100 -- AsyncOperationService finishCompletedOperation -- 
2024-06-28 10:27:35.456200 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-06-28 10:27:35.456300 -- AlientechProjectService processSuccessOperationDecode -- 
2024-06-28 10:27:35.460500 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-28 10:27:35.618900 -- AlientechLinkService __construct -- 
2024-06-28 10:27:35.619000 -- AlientechLinkService processClient -- 
2024-06-28 10:27:35.619100 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 10:27:35.619400 -- initAccessToken -- 
2024-06-28 10:27:35.621900 -- iR8V9-KAuaE -- 
2024-06-28 10:27:35.622700 -- AlientechProjectService __construct -- 
2024-06-28 10:27:35.624000 -- FileSlotService __construct -- 
2024-06-28 10:27:35.624200 -- FileSlotService __construct -- 
2024-06-28 10:27:35.624300 -- AsyncOperationService __construct -- 
2024-06-28 10:27:35.624600 -- AsyncOperationService downloadDecodedFiles -- 
2024-06-28 10:27:35.625200 -- FileService downloadFile -- 
2024-06-28 10:27:35.625300 -- AlientechLinkService processRequest -- 
2024-06-28 10:27:35.625300 -- isAuth -- 
2024-06-28 10:27:35.625400 -- AlientechLinkService isAuth_success -- 
2024-06-28 10:27:36.357600 -- AlientechLinkService processResponse -- 
2024-06-28 10:27:36.379200 --  --- $log->save() --  ---128 -- 
2024-06-28 10:27:36.547000 --  --- $log->save() --  ---129 -- 
2024-06-28 10:27:36.563200 -- FileService saveDecodedFile -- 
2024-06-28 10:27:36.588500 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-28 10:59:37.752000 -- AlientechLinkService __construct -- 
2024-06-28 10:59:37.752300 -- AlientechLinkService processClient -- 
2024-06-28 10:59:37.752300 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 10:59:37.752700 -- initAccessToken -- 
2024-06-28 10:59:37.755300 -- iR8V9-KAuaE -- 
2024-06-28 10:59:37.756600 -- AlientechProjectService __construct -- 
2024-06-28 10:59:37.759400 -- FileSlotService __construct -- 
2024-06-28 10:59:37.759900 -- FileSlotService __construct -- 
2024-06-28 10:59:37.760000 -- AsyncOperationService __construct -- 
2024-06-28 10:59:37.761500 -- FileSlotService __construct -- 
2024-06-28 10:59:37.761700 -- Kess3Service __construct -- 
2024-06-28 10:59:37.761800 -- Kess3Service startEncoding -- 
2024-06-28 10:59:37.761900 -- AsyncOperationService startOperationEncode -- 
2024-06-28 10:59:37.787600 -- FileService uploadFiles -- 
2024-06-28 10:59:37.788000 -- AlientechLinkService processRequest -- 
2024-06-28 10:59:37.788000 -- isAuth -- 
2024-06-28 10:59:37.788100 -- AlientechLinkService isAuth_success -- 
2024-06-28 10:59:39.596800 -- AlientechLinkService processResponse -- 
2024-06-28 10:59:39.610100 --  --- $log->save() --  ---130 -- 
2024-06-28 10:59:39.612000 --  --- $log->save() --  ---131 -- 
2024-06-28 10:59:39.625800 -- AlientechLinkService processRequest -- 
2024-06-28 10:59:39.626100 -- isAuth -- 
2024-06-28 10:59:39.626200 -- AlientechLinkService isAuth_success -- 
2024-06-28 10:59:40.403300 -- AlientechLinkService processResponse -- 
2024-06-28 10:59:40.405500 --  --- $log->save() --  ---132 -- 
2024-06-28 10:59:40.407400 --  --- $log->save() --  ---133 -- 
2024-06-28 10:59:40.407600 -- AsyncOperationService createOperationDtoByData -- 
2024-06-28 10:59:40.407600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-28 10:59:40.408700 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-28 10:59:42.461800 -- AlientechLinkService __construct -- 
2024-06-28 10:59:42.462000 -- AlientechLinkService processClient -- 
2024-06-28 10:59:42.462100 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 10:59:42.462200 -- initAccessToken -- 
2024-06-28 10:59:42.462700 -- iR8V9-KAuaE -- 
2024-06-28 10:59:42.462900 -- AlientechProjectService __construct -- 
2024-06-28 10:59:42.463100 -- FileSlotService __construct -- 
2024-06-28 10:59:42.463200 -- FileSlotService __construct -- 
2024-06-28 10:59:42.463300 -- AsyncOperationService __construct -- 
2024-06-28 10:59:42.463500 -- ApiController actionKess3Encoded -- 
2024-06-28 10:59:42.466300 --  --- $log->save() --  ---134 -- 
2024-06-28 10:59:42.466400 -- AsyncOperationService createOperationDtoByData -- 
2024-06-28 10:59:42.466500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-28 10:59:42.467600 -- AsyncOperationService processCompletedOperation -- 
2024-06-28 10:59:42.467700 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-28 10:59:42.469500 -- AsyncOperationService finishCompletedOperation -- 
2024-06-28 10:59:42.469600 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-06-28 10:59:42.469700 -- AlientechProjectService processSuccessOperationEncode -- 
2024-06-28 10:59:42.471500 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-28 10:59:43.617900 -- AlientechLinkService __construct -- 
2024-06-28 10:59:43.618100 -- AlientechLinkService processClient -- 
2024-06-28 10:59:43.618100 -- AlientechLinkService is_null($this->client) -- 
2024-06-28 10:59:43.618500 -- initAccessToken -- 
2024-06-28 10:59:43.621300 -- iR8V9-KAuaE -- 
2024-06-28 10:59:43.621900 -- AlientechProjectService __construct -- 
2024-06-28 10:59:43.622900 -- FileSlotService __construct -- 
2024-06-28 10:59:43.623100 -- FileSlotService __construct -- 
2024-06-28 10:59:43.623100 -- AsyncOperationService __construct -- 
2024-06-28 10:59:43.623300 -- AsyncOperationService downloadEncodedFiles -- 
2024-06-28 10:59:43.623400 -- FileService downloadFile -- 
2024-06-28 10:59:43.623400 -- AlientechLinkService processRequest -- 
2024-06-28 10:59:43.623400 -- isAuth -- 
2024-06-28 10:59:43.623500 -- AlientechLinkService isAuth_success -- 
2024-06-28 10:59:44.964200 -- AlientechLinkService processResponse -- 
2024-06-28 10:59:44.984200 --  --- $log->save() --  ---135 -- 
2024-06-28 10:59:45.178200 --  --- $log->save() --  ---136 -- 
2024-06-28 10:59:45.199300 -- FileService saveEncodedFile -- 
2024-06-28 10:59:45.228400 -- FileService $initFile->file_history={"id":"7582","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-06-28 10:59:45.241200 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-06-29 06:29:17.083900 -- AlientechLinkService __construct -- 
2024-06-29 06:29:17.084200 -- AlientechLinkService processClient -- 
2024-06-29 06:29:17.084200 -- AlientechLinkService is_null($this->client) -- 
2024-06-29 06:29:17.084800 -- initAccessToken -- 
2024-06-29 06:29:17.089600 -- iR8V9-KAuaE -- 
2024-06-29 06:29:17.091500 -- AlientechProjectService __construct -- 
2024-06-29 06:29:17.094200 -- FileSlotService __construct -- 
2024-06-29 06:29:17.094500 -- FileSlotService __construct -- 
2024-06-29 06:29:17.094500 -- AsyncOperationService __construct -- 
2024-06-29 06:29:17.095200 -- FileSlotService __construct -- 
2024-06-29 06:29:17.095300 -- Kess3Service __construct -- 
2024-06-29 06:29:17.095300 -- Kess3Service startDecoding -- 
2024-06-29 06:29:17.095400 -- AsyncOperationService startOperationDecode -- 
2024-06-29 06:29:17.095400 -- AlientechLinkService processRequest -- 
2024-06-29 06:29:17.095400 -- isAuth -- 
2024-06-29 06:29:17.095500 -- AlientechLinkService isAuth_success -- 
2024-06-29 06:29:18.652800 -- AlientechLinkService processResponse -- 
2024-06-29 06:29:18.674800 --  --- $log->save() --  ---137 -- 
2024-06-29 06:29:18.675000 -- response_status=429 -- 
2024-06-29 06:29:18.675100 -- response=Http-Code: 429
Content-Type: text/plain; charset=utf-8
Retry-After: 0
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Sat, 29 Jun 2024 06:29:17 GMT
Connection: close

TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-06-29 06:29:18.675200 -- content=TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-06-29 06:29:18.675300 -- FileSlotService closeAllFileSlots -- 
2024-06-29 06:29:18.675300 -- FileSlotService getSlots -- 
2024-06-29 06:29:18.675400 -- AlientechLinkService processRequest -- 
2024-06-29 06:29:18.675400 -- isAuth -- 
2024-06-29 06:29:18.675500 -- AlientechLinkService isAuth_success -- 
2024-06-29 06:29:19.215800 -- AlientechLinkService processResponse -- 
2024-06-29 06:29:19.218200 --  --- $log->save() --  ---138 -- 
2024-06-29 06:29:19.224000 --  --- $log->save() --  ---139 -- 
2024-06-29 06:29:19.224600 -- slot [3795b09d-24b7-45b4-80b2-84ddc23cdaa8] dateDiff {"y":0,"m":0,"d":0,"h":20,"i":1,"s":46,"f":0.054508,"invert":1,"days":0,"from_string":false} -- 
2024-06-29 06:29:19.224700 -- FileSlotService closeSlot -- 
2024-06-29 06:29:19.224800 -- /api/kess3/file-slots/3795b09d-24b7-45b4-80b2-84ddc23cdaa8/close  -- 
2024-06-29 06:29:19.224800 -- AlientechLinkService processRequest -- 
2024-06-29 06:29:19.224900 -- isAuth -- 
2024-06-29 06:29:19.224900 -- AlientechLinkService isAuth_success -- 
2024-06-29 06:29:19.610300 -- AlientechLinkService processResponse -- 
2024-06-29 06:29:19.612500 --  --- $log->save() --  ---140 -- 
2024-06-29 06:29:19.613800 --  --- $log->save() --  ---141 -- 
2024-06-29 06:29:19.613900 -- slot [3795b09d-24b7-45b4-80b2-84ddc23cdaa8] closeSlot null -- 
2024-06-29 06:29:19.614000 -- slot [add1d66f-8537-48ea-9c87-ee79266116a4] dateDiff {"y":0,"m":0,"d":0,"h":21,"i":15,"s":56,"f":0.217296,"invert":1,"days":0,"from_string":false} -- 
2024-06-29 06:29:19.614000 -- FileSlotService closeSlot -- 
2024-06-29 06:29:19.614100 -- /api/kess3/file-slots/add1d66f-8537-48ea-9c87-ee79266116a4/close  -- 
2024-06-29 06:29:19.614100 -- AlientechLinkService processRequest -- 
2024-06-29 06:29:19.614100 -- isAuth -- 
2024-06-29 06:29:19.614100 -- AlientechLinkService isAuth_success -- 
2024-06-29 06:29:19.891800 -- AlientechLinkService processResponse -- 
2024-06-29 06:29:19.893700 --  --- $log->save() --  ---142 -- 
2024-06-29 06:29:19.895500 --  --- $log->save() --  ---143 -- 
2024-06-29 06:29:19.895700 -- slot [add1d66f-8537-48ea-9c87-ee79266116a4] closeSlot null -- 
2024-06-29 06:29:19.895800 -- slot [d0fc89b0-fdf9-4924-8606-61b413162fc2] dateDiff {"y":0,"m":0,"d":1,"h":13,"i":50,"s":31,"f":0.769119,"invert":1,"days":1,"from_string":false} -- 
2024-06-29 06:29:19.895900 -- FileSlotService closeSlot -- 
2024-06-29 06:29:19.896000 -- /api/kess3/file-slots/d0fc89b0-fdf9-4924-8606-61b413162fc2/close  -- 
2024-06-29 06:29:19.896100 -- AlientechLinkService processRequest -- 
2024-06-29 06:29:19.896100 -- isAuth -- 
2024-06-29 06:29:19.896200 -- AlientechLinkService isAuth_success -- 
2024-06-29 06:29:20.163500 -- AlientechLinkService processResponse -- 
2024-06-29 06:29:20.165500 --  --- $log->save() --  ---144 -- 
2024-06-29 06:29:20.167900 --  --- $log->save() --  ---145 -- 
2024-06-29 06:29:20.168100 -- slot [d0fc89b0-fdf9-4924-8606-61b413162fc2] closeSlot null -- 
2024-06-29 06:29:20.168200 -- closeAllSlots finish -- 
2024-06-29 06:29:20.168300 -- AsyncOperationService startOperationDecode -- 
2024-06-29 06:29:20.168300 -- AlientechLinkService processRequest -- 
2024-06-29 06:29:20.168300 -- isAuth -- 
2024-06-29 06:29:20.168400 -- AlientechLinkService isAuth_success -- 
2024-06-29 06:29:20.517600 -- AlientechLinkService processResponse -- 
2024-06-29 06:29:20.520600 --  --- $log->save() --  ---146 -- 
2024-06-29 06:29:20.527300 --  --- $log->save() --  ---147 -- 
2024-06-29 06:29:20.527600 -- AsyncOperationService createOperationDtoByData -- 
2024-06-29 06:29:20.527700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-29 06:29:20.541100 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-29 06:29:22.624800 -- AlientechLinkService __construct -- 
2024-06-29 06:29:22.625100 -- AlientechLinkService processClient -- 
2024-06-29 06:29:22.625200 -- AlientechLinkService is_null($this->client) -- 
2024-06-29 06:29:22.625300 -- initAccessToken -- 
2024-06-29 06:29:22.625600 -- iR8V9-KAuaE -- 
2024-06-29 06:29:22.625800 -- AlientechProjectService __construct -- 
2024-06-29 06:29:22.625900 -- FileSlotService __construct -- 
2024-06-29 06:29:22.626000 -- FileSlotService __construct -- 
2024-06-29 06:29:22.626100 -- AsyncOperationService __construct -- 
2024-06-29 06:29:22.626200 -- ApiController actionKess3Decoded -- 
2024-06-29 06:29:22.629400 --  --- $log->save() --  ---148 -- 
2024-06-29 06:29:22.629500 -- AsyncOperationService createOperationDtoByData -- 
2024-06-29 06:29:22.629500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-29 06:29:22.631700 -- AsyncOperationService processCompletedOperation -- 
2024-06-29 06:29:22.631800 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-29 06:29:22.633700 -- AsyncOperationService finishCompletedOperation -- 
2024-06-29 06:29:22.633800 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-06-29 06:29:22.633900 -- AlientechProjectService processSuccessOperationDecode -- 
2024-06-29 06:29:22.636500 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-29 06:29:23.198600 -- AlientechLinkService __construct -- 
2024-06-29 06:29:23.198800 -- AlientechLinkService processClient -- 
2024-06-29 06:29:23.198800 -- AlientechLinkService is_null($this->client) -- 
2024-06-29 06:29:23.199200 -- initAccessToken -- 
2024-06-29 06:29:23.202200 -- iR8V9-KAuaE -- 
2024-06-29 06:29:23.203500 -- AlientechProjectService __construct -- 
2024-06-29 06:29:23.205900 -- FileSlotService __construct -- 
2024-06-29 06:29:23.206500 -- FileSlotService __construct -- 
2024-06-29 06:29:23.206600 -- AsyncOperationService __construct -- 
2024-06-29 06:29:23.207100 -- AsyncOperationService downloadDecodedFiles -- 
2024-06-29 06:29:23.208200 -- FileService downloadFile -- 
2024-06-29 06:29:23.208400 -- AlientechLinkService processRequest -- 
2024-06-29 06:29:23.208500 -- isAuth -- 
2024-06-29 06:29:23.208500 -- AlientechLinkService isAuth_success -- 
2024-06-29 06:29:23.617400 -- AlientechLinkService processResponse -- 
2024-06-29 06:29:23.643400 --  --- $log->save() --  ---149 -- 
2024-06-29 06:29:23.653800 --  --- $log->save() --  ---150 -- 
2024-06-29 06:29:23.654700 -- FileService saveDecodedFile -- 
2024-06-29 06:29:23.671900 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-29 06:29:23.731900 -- FileService downloadFile -- 
2024-06-29 06:29:23.732100 -- AlientechLinkService processRequest -- 
2024-06-29 06:29:23.732100 -- isAuth -- 
2024-06-29 06:29:23.732200 -- AlientechLinkService isAuth_success -- 
2024-06-29 06:29:25.158600 -- AlientechLinkService processResponse -- 
2024-06-29 06:29:25.166300 --  --- $log->save() --  ---151 -- 
2024-06-29 06:29:25.495300 --  --- $log->save() --  ---152 -- 
2024-06-29 06:29:25.526600 -- FileService saveDecodedFile -- 
2024-06-29 06:29:25.553000 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-06-29 07:34:12.476800 -- AlientechLinkService __construct -- 
2024-06-29 07:34:12.477200 -- AlientechLinkService processClient -- 
2024-06-29 07:34:12.477300 -- AlientechLinkService is_null($this->client) -- 
2024-06-29 07:34:12.477700 -- initAccessToken -- 
2024-06-29 07:34:12.480100 -- iR8V9-KAuaE -- 
2024-06-29 07:34:12.480700 -- AlientechProjectService __construct -- 
2024-06-29 07:34:12.481700 -- FileSlotService __construct -- 
2024-06-29 07:34:12.481900 -- FileSlotService __construct -- 
2024-06-29 07:34:12.482100 -- AsyncOperationService __construct -- 
2024-06-29 07:34:12.482600 -- FileSlotService __construct -- 
2024-06-29 07:34:12.482700 -- Kess3Service __construct -- 
2024-06-29 07:34:12.482800 -- Kess3Service startEncoding -- 
2024-06-29 07:34:12.482800 -- AsyncOperationService startOperationEncode -- 
2024-06-29 07:34:12.499400 -- FileService uploadFiles -- 
2024-06-29 07:34:12.499800 -- AlientechLinkService processRequest -- 
2024-06-29 07:34:12.499900 -- isAuth -- 
2024-06-29 07:34:12.499900 -- AlientechLinkService isAuth_success -- 
2024-06-29 07:34:14.780400 -- AlientechLinkService processResponse -- 
2024-06-29 07:34:14.788100 --  --- $log->save() --  ---153 -- 
2024-06-29 07:34:14.790300 --  --- $log->save() --  ---154 -- 
2024-06-29 07:34:14.800600 -- AlientechLinkService processRequest -- 
2024-06-29 07:34:14.801200 -- isAuth -- 
2024-06-29 07:34:14.801600 -- AlientechLinkService isAuth_success -- 
2024-06-29 07:34:16.133900 -- AlientechLinkService processResponse -- 
2024-06-29 07:34:16.136600 --  --- $log->save() --  ---155 -- 
2024-06-29 07:34:16.139500 --  --- $log->save() --  ---156 -- 
2024-06-29 07:34:16.139700 -- AsyncOperationService createOperationDtoByData -- 
2024-06-29 07:34:16.139900 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-29 07:34:16.141100 -- AsyncOperationRepository createAsyncOperation -- 
2024-06-29 07:34:18.563600 -- AlientechLinkService __construct -- 
2024-06-29 07:34:18.564000 -- AlientechLinkService processClient -- 
2024-06-29 07:34:18.564100 -- AlientechLinkService is_null($this->client) -- 
2024-06-29 07:34:18.564300 -- initAccessToken -- 
2024-06-29 07:34:18.564900 -- iR8V9-KAuaE -- 
2024-06-29 07:34:18.565200 -- AlientechProjectService __construct -- 
2024-06-29 07:34:18.565400 -- FileSlotService __construct -- 
2024-06-29 07:34:18.565500 -- FileSlotService __construct -- 
2024-06-29 07:34:18.565600 -- AsyncOperationService __construct -- 
2024-06-29 07:34:18.565900 -- ApiController actionKess3Encoded -- 
2024-06-29 07:34:18.571600 --  --- $log->save() --  ---157 -- 
2024-06-29 07:34:18.571800 -- AsyncOperationService createOperationDtoByData -- 
2024-06-29 07:34:18.571800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-06-29 07:34:18.575400 -- AsyncOperationService processCompletedOperation -- 
2024-06-29 07:34:18.575500 -- AsyncOperationRepository updateAsyncOperation -- 
2024-06-29 07:34:18.578200 -- AsyncOperationService finishCompletedOperation -- 
2024-06-29 07:34:18.578300 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-06-29 07:34:18.578300 -- AlientechProjectService processSuccessOperationEncode -- 
2024-06-29 07:34:18.581300 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-06-29 07:34:21.575400 -- AlientechLinkService __construct -- 
2024-06-29 07:34:21.575700 -- AlientechLinkService processClient -- 
2024-06-29 07:34:21.575800 -- AlientechLinkService is_null($this->client) -- 
2024-06-29 07:34:21.576300 -- initAccessToken -- 
2024-06-29 07:34:21.579600 -- iR8V9-KAuaE -- 
2024-06-29 07:34:21.581600 -- AlientechProjectService __construct -- 
2024-06-29 07:34:21.584500 -- FileSlotService __construct -- 
2024-06-29 07:34:21.585400 -- FileSlotService __construct -- 
2024-06-29 07:34:21.585500 -- AsyncOperationService __construct -- 
2024-06-29 07:34:21.586900 -- AsyncOperationService downloadEncodedFiles -- 
2024-06-29 07:34:21.587400 -- FileService downloadFile -- 
2024-06-29 07:34:21.587600 -- AlientechLinkService processRequest -- 
2024-06-29 07:34:21.587700 -- isAuth -- 
2024-06-29 07:34:21.587700 -- AlientechLinkService isAuth_success -- 
2024-06-29 07:34:22.090100 -- AlientechLinkService processResponse -- 
2024-06-29 07:34:22.110500 --  --- $log->save() --  ---158 -- 
2024-06-29 07:34:22.210600 --  --- $log->save() --  ---159 -- 
2024-06-29 07:34:22.223900 -- FileService saveEncodedFile -- 
2024-06-29 07:34:22.251900 -- FileService $initFile->file_history={"id":"7585","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-06-29 07:34:22.272800 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-01 07:32:41.121900 -- AlientechLinkService __construct -- 
2024-07-01 07:32:41.122200 -- AlientechLinkService processClient -- 
2024-07-01 07:32:41.122200 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 07:32:41.122500 -- initAccessToken -- 
2024-07-01 07:32:41.126100 -- iR8V9-KAuaE -- 
2024-07-01 07:32:41.128000 -- AlientechProjectService __construct -- 
2024-07-01 07:32:41.131000 -- FileSlotService __construct -- 
2024-07-01 07:32:41.131500 -- FileSlotService __construct -- 
2024-07-01 07:32:41.131700 -- AsyncOperationService __construct -- 
2024-07-01 07:32:41.133400 -- FileSlotService __construct -- 
2024-07-01 07:32:41.133500 -- Kess3Service __construct -- 
2024-07-01 07:32:41.133600 -- Kess3Service startDecoding -- 
2024-07-01 07:32:41.133700 -- AsyncOperationService startOperationDecode -- 
2024-07-01 07:32:41.133800 -- AlientechLinkService processRequest -- 
2024-07-01 07:32:41.133900 -- isAuth -- 
2024-07-01 07:32:41.134100 -- AlientechLinkService isAuth_success -- 
2024-07-01 07:32:43.229000 -- AlientechLinkService processResponse -- 
2024-07-01 07:32:43.252400 --  --- $log->save() --  ---160 -- 
2024-07-01 07:32:43.254900 --  --- $log->save() --  ---161 -- 
2024-07-01 07:32:43.255200 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 07:32:43.255400 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 07:32:43.268200 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-01 07:32:45.512100 -- AlientechLinkService __construct -- 
2024-07-01 07:32:45.512300 -- AlientechLinkService processClient -- 
2024-07-01 07:32:45.512300 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 07:32:45.512400 -- initAccessToken -- 
2024-07-01 07:32:45.513400 -- iR8V9-KAuaE -- 
2024-07-01 07:32:45.513800 -- AlientechProjectService __construct -- 
2024-07-01 07:32:45.514200 -- FileSlotService __construct -- 
2024-07-01 07:32:45.514400 -- FileSlotService __construct -- 
2024-07-01 07:32:45.514500 -- AsyncOperationService __construct -- 
2024-07-01 07:32:45.514900 -- ApiController actionKess3Decoded -- 
2024-07-01 07:32:45.526700 --  --- $log->save() --  ---162 -- 
2024-07-01 07:32:45.526800 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 07:32:45.526800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 07:32:45.531300 -- AsyncOperationService processCompletedOperation -- 
2024-07-01 07:32:45.531400 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-01 07:32:45.534300 -- AsyncOperationService finishCompletedOperation -- 
2024-07-01 07:32:45.534500 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-01 07:32:45.534500 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-01 07:32:45.537900 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-01 07:32:46.130700 -- AlientechLinkService __construct -- 
2024-07-01 07:32:46.130900 -- AlientechLinkService processClient -- 
2024-07-01 07:32:46.130900 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 07:32:46.131300 -- initAccessToken -- 
2024-07-01 07:32:46.134600 -- iR8V9-KAuaE -- 
2024-07-01 07:32:46.135400 -- AlientechProjectService __construct -- 
2024-07-01 07:32:46.136500 -- FileSlotService __construct -- 
2024-07-01 07:32:46.136800 -- FileSlotService __construct -- 
2024-07-01 07:32:46.136800 -- AsyncOperationService __construct -- 
2024-07-01 07:32:46.137300 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-01 07:32:46.138000 -- FileService downloadFile -- 
2024-07-01 07:32:46.138100 -- AlientechLinkService processRequest -- 
2024-07-01 07:32:46.138100 -- isAuth -- 
2024-07-01 07:32:46.138200 -- AlientechLinkService isAuth_success -- 
2024-07-01 07:32:47.757800 -- AlientechLinkService processResponse -- 
2024-07-01 07:32:47.786800 --  --- $log->save() --  ---163 -- 
2024-07-01 07:32:48.008100 --  --- $log->save() --  ---164 -- 
2024-07-01 07:32:48.021200 -- FileService saveDecodedFile -- 
2024-07-01 07:32:48.054400 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-01 09:54:49.836000 -- AlientechLinkService __construct -- 
2024-07-01 09:54:49.836200 -- AlientechLinkService processClient -- 
2024-07-01 09:54:49.836200 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 09:54:49.836600 -- initAccessToken -- 
2024-07-01 09:54:49.839900 -- iR8V9-KAuaE -- 
2024-07-01 09:54:49.841300 -- AlientechProjectService __construct -- 
2024-07-01 09:54:49.843600 -- FileSlotService __construct -- 
2024-07-01 09:54:49.844100 -- FileSlotService __construct -- 
2024-07-01 09:54:49.844200 -- AsyncOperationService __construct -- 
2024-07-01 09:54:49.845500 -- FileSlotService __construct -- 
2024-07-01 09:54:49.845600 -- Kess3Service __construct -- 
2024-07-01 09:54:49.845700 -- Kess3Service startEncoding -- 
2024-07-01 09:54:49.845800 -- AsyncOperationService startOperationEncode -- 
2024-07-01 09:54:49.871200 -- FileService uploadFiles -- 
2024-07-01 09:54:49.871500 -- AlientechLinkService processRequest -- 
2024-07-01 09:54:49.871500 -- isAuth -- 
2024-07-01 09:54:49.871600 -- AlientechLinkService isAuth_success -- 
2024-07-01 09:54:51.679900 -- AlientechLinkService processResponse -- 
2024-07-01 09:54:51.690900 --  --- $log->save() --  ---165 -- 
2024-07-01 09:54:51.692900 --  --- $log->save() --  ---166 -- 
2024-07-01 09:54:51.702900 -- AlientechLinkService processRequest -- 
2024-07-01 09:54:51.703000 -- isAuth -- 
2024-07-01 09:54:51.703100 -- AlientechLinkService isAuth_success -- 
2024-07-01 09:54:52.542800 -- AlientechLinkService processResponse -- 
2024-07-01 09:54:52.545700 --  --- $log->save() --  ---167 -- 
2024-07-01 09:54:52.548000 --  --- $log->save() --  ---168 -- 
2024-07-01 09:54:52.548200 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 09:54:52.548300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 09:54:52.549700 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-01 09:54:54.754400 -- AlientechLinkService __construct -- 
2024-07-01 09:54:54.754600 -- AlientechLinkService processClient -- 
2024-07-01 09:54:54.754700 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 09:54:54.754800 -- initAccessToken -- 
2024-07-01 09:54:54.756500 -- iR8V9-KAuaE -- 
2024-07-01 09:54:54.756800 -- AlientechProjectService __construct -- 
2024-07-01 09:54:54.757000 -- FileSlotService __construct -- 
2024-07-01 09:54:54.757100 -- FileSlotService __construct -- 
2024-07-01 09:54:54.757100 -- AsyncOperationService __construct -- 
2024-07-01 09:54:54.757400 -- ApiController actionKess3Encoded -- 
2024-07-01 09:54:54.762900 --  --- $log->save() --  ---169 -- 
2024-07-01 09:54:54.763100 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 09:54:54.763200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 09:54:54.766800 -- AsyncOperationService processCompletedOperation -- 
2024-07-01 09:54:54.767000 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-01 09:54:54.769500 -- AsyncOperationService finishCompletedOperation -- 
2024-07-01 09:54:54.769700 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-01 09:54:54.769700 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-01 09:54:54.772400 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-01 09:54:57.092700 -- AlientechLinkService __construct -- 
2024-07-01 09:54:57.092800 -- AlientechLinkService processClient -- 
2024-07-01 09:54:57.092900 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 09:54:57.093600 -- initAccessToken -- 
2024-07-01 09:54:57.096500 -- iR8V9-KAuaE -- 
2024-07-01 09:54:57.097200 -- AlientechProjectService __construct -- 
2024-07-01 09:54:57.098200 -- FileSlotService __construct -- 
2024-07-01 09:54:57.098400 -- FileSlotService __construct -- 
2024-07-01 09:54:57.098600 -- AsyncOperationService __construct -- 
2024-07-01 09:54:57.098900 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-01 09:54:57.099200 -- FileService downloadFile -- 
2024-07-01 09:54:57.099300 -- AlientechLinkService processRequest -- 
2024-07-01 09:54:57.099300 -- isAuth -- 
2024-07-01 09:54:57.099300 -- AlientechLinkService isAuth_success -- 
2024-07-01 09:54:58.092000 -- AlientechLinkService processResponse -- 
2024-07-01 09:54:58.119500 --  --- $log->save() --  ---170 -- 
2024-07-01 09:54:58.381400 --  --- $log->save() --  ---171 -- 
2024-07-01 09:54:58.423700 -- FileService saveEncodedFile -- 
2024-07-01 09:54:58.465300 -- FileService $initFile->file_history={"id":"7588","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-01 09:54:58.485800 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-01 10:28:42.727500 -- AlientechLinkService __construct -- 
2024-07-01 10:28:42.727700 -- AlientechLinkService processClient -- 
2024-07-01 10:28:42.727700 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 10:28:42.728100 -- initAccessToken -- 
2024-07-01 10:28:42.730600 -- iR8V9-KAuaE -- 
2024-07-01 10:28:42.731200 -- AlientechProjectService __construct -- 
2024-07-01 10:28:42.732000 -- FileSlotService __construct -- 
2024-07-01 10:28:42.732200 -- FileSlotService __construct -- 
2024-07-01 10:28:42.732200 -- AsyncOperationService __construct -- 
2024-07-01 10:28:42.732700 -- FileSlotService __construct -- 
2024-07-01 10:28:42.732800 -- Kess3Service __construct -- 
2024-07-01 10:28:42.732800 -- Kess3Service startDecoding -- 
2024-07-01 10:28:42.732800 -- AsyncOperationService startOperationDecode -- 
2024-07-01 10:28:42.732800 -- AlientechLinkService processRequest -- 
2024-07-01 10:28:42.732900 -- isAuth -- 
2024-07-01 10:28:42.732900 -- AlientechLinkService isAuth_success -- 
2024-07-01 10:28:43.547400 -- AlientechLinkService processResponse -- 
2024-07-01 10:28:43.569300 --  --- $log->save() --  ---172 -- 
2024-07-01 10:28:43.571200 --  --- $log->save() --  ---173 -- 
2024-07-01 10:28:43.571400 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 10:28:43.571400 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 10:28:43.579400 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-01 10:28:47.403300 -- AlientechLinkService __construct -- 
2024-07-01 10:28:47.403400 -- AlientechLinkService processClient -- 
2024-07-01 10:28:47.403400 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 10:28:47.403500 -- initAccessToken -- 
2024-07-01 10:28:47.403800 -- iR8V9-KAuaE -- 
2024-07-01 10:28:47.404000 -- AlientechProjectService __construct -- 
2024-07-01 10:28:47.404100 -- FileSlotService __construct -- 
2024-07-01 10:28:47.404200 -- FileSlotService __construct -- 
2024-07-01 10:28:47.404200 -- AsyncOperationService __construct -- 
2024-07-01 10:28:47.404300 -- ApiController actionKess3Decoded -- 
2024-07-01 10:28:47.406300 --  --- $log->save() --  ---174 -- 
2024-07-01 10:28:47.406400 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 10:28:47.406500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 10:28:47.407300 -- AsyncOperationService processCompletedOperation -- 
2024-07-01 10:28:47.407300 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-01 10:28:47.408900 -- AsyncOperationService finishCompletedOperation -- 
2024-07-01 10:28:47.408900 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-01 10:28:47.408900 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-01 10:28:47.411100 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-01 10:28:48.002500 -- AlientechLinkService __construct -- 
2024-07-01 10:28:48.002800 -- AlientechLinkService processClient -- 
2024-07-01 10:28:48.002800 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 10:28:48.003200 -- initAccessToken -- 
2024-07-01 10:28:48.005700 -- iR8V9-KAuaE -- 
2024-07-01 10:28:48.006300 -- AlientechProjectService __construct -- 
2024-07-01 10:28:48.007100 -- FileSlotService __construct -- 
2024-07-01 10:28:48.007500 -- FileSlotService __construct -- 
2024-07-01 10:28:48.007600 -- AsyncOperationService __construct -- 
2024-07-01 10:28:48.007800 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-01 10:28:48.008400 -- FileService downloadFile -- 
2024-07-01 10:28:48.008500 -- AlientechLinkService processRequest -- 
2024-07-01 10:28:48.008500 -- isAuth -- 
2024-07-01 10:28:48.008600 -- AlientechLinkService isAuth_success -- 
2024-07-01 10:28:48.385900 -- AlientechLinkService processResponse -- 
2024-07-01 10:28:48.417200 --  --- $log->save() --  ---175 -- 
2024-07-01 10:28:48.425200 --  --- $log->save() --  ---176 -- 
2024-07-01 10:28:48.426100 -- FileService saveDecodedFile -- 
2024-07-01 10:28:48.442100 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-01 10:28:48.486600 -- FileService downloadFile -- 
2024-07-01 10:28:48.486700 -- AlientechLinkService processRequest -- 
2024-07-01 10:28:48.486700 -- isAuth -- 
2024-07-01 10:28:48.486800 -- AlientechLinkService isAuth_success -- 
2024-07-01 10:28:49.548100 -- AlientechLinkService processResponse -- 
2024-07-01 10:28:49.549900 --  --- $log->save() --  ---177 -- 
2024-07-01 10:28:49.762200 --  --- $log->save() --  ---178 -- 
2024-07-01 10:28:49.788500 -- FileService saveDecodedFile -- 
2024-07-01 10:28:49.838200 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-01 12:16:20.253600 -- AlientechLinkService __construct -- 
2024-07-01 12:16:20.253800 -- AlientechLinkService processClient -- 
2024-07-01 12:16:20.253800 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 12:16:20.254200 -- initAccessToken -- 
2024-07-01 12:16:20.256700 -- iR8V9-KAuaE -- 
2024-07-01 12:16:20.257600 -- AlientechProjectService __construct -- 
2024-07-01 12:16:20.259200 -- FileSlotService __construct -- 
2024-07-01 12:16:20.259400 -- FileSlotService __construct -- 
2024-07-01 12:16:20.259400 -- AsyncOperationService __construct -- 
2024-07-01 12:16:20.260400 -- FileSlotService __construct -- 
2024-07-01 12:16:20.260500 -- Kess3Service __construct -- 
2024-07-01 12:16:20.260500 -- Kess3Service startDecoding -- 
2024-07-01 12:16:20.260600 -- AsyncOperationService startOperationDecode -- 
2024-07-01 12:16:20.260600 -- AlientechLinkService processRequest -- 
2024-07-01 12:16:20.260700 -- isAuth -- 
2024-07-01 12:16:20.260800 -- AlientechLinkService isAuth_success -- 
2024-07-01 12:16:21.493800 -- AlientechLinkService processResponse -- 
2024-07-01 12:16:21.512400 --  --- $log->save() --  ---179 -- 
2024-07-01 12:16:21.512600 -- response_status=429 -- 
2024-07-01 12:16:21.512700 -- response=Http-Code: 429
Content-Type: text/plain; charset=utf-8
Retry-After: 0
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Mon, 01 Jul 2024 12:16:21 GMT
Connection: close

TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-07-01 12:16:21.512800 -- content=TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-07-01 12:16:21.512800 -- FileSlotService closeAllFileSlots -- 
2024-07-01 12:16:21.512900 -- FileSlotService getSlots -- 
2024-07-01 12:16:21.512900 -- AlientechLinkService processRequest -- 
2024-07-01 12:16:21.513000 -- isAuth -- 
2024-07-01 12:16:21.513000 -- AlientechLinkService isAuth_success -- 
2024-07-01 12:16:21.895300 -- AlientechLinkService processResponse -- 
2024-07-01 12:16:21.897200 --  --- $log->save() --  ---180 -- 
2024-07-01 12:16:21.901600 --  --- $log->save() --  ---181 -- 
2024-07-01 12:16:21.902300 -- slot [c2e4433f-f2f8-492b-bfbb-96acf773b9cf] dateDiff {"y":0,"m":0,"d":0,"h":1,"i":47,"s":38,"f":0.402243,"invert":1,"days":0,"from_string":false} -- 
2024-07-01 12:16:21.902500 -- FileSlotService closeSlot -- 
2024-07-01 12:16:21.902600 -- /api/kess3/file-slots/c2e4433f-f2f8-492b-bfbb-96acf773b9cf/close  -- 
2024-07-01 12:16:21.902700 -- AlientechLinkService processRequest -- 
2024-07-01 12:16:21.902700 -- isAuth -- 
2024-07-01 12:16:21.903000 -- AlientechLinkService isAuth_success -- 
2024-07-01 12:16:23.390600 -- AlientechLinkService processResponse -- 
2024-07-01 12:16:23.393500 --  --- $log->save() --  ---182 -- 
2024-07-01 12:16:23.395900 --  --- $log->save() --  ---183 -- 
2024-07-01 12:16:23.396100 -- slot [c2e4433f-f2f8-492b-bfbb-96acf773b9cf] closeSlot null -- 
2024-07-01 12:16:23.396300 -- slot [91a01f65-6d44-461e-a72a-f940c64a56bf] dateDiff {"y":0,"m":0,"d":0,"h":4,"i":43,"s":40,"f":0.229561,"invert":1,"days":0,"from_string":false} -- 
2024-07-01 12:16:23.396300 -- FileSlotService closeSlot -- 
2024-07-01 12:16:23.396400 -- /api/kess3/file-slots/91a01f65-6d44-461e-a72a-f940c64a56bf/close  -- 
2024-07-01 12:16:23.396500 -- AlientechLinkService processRequest -- 
2024-07-01 12:16:23.396500 -- isAuth -- 
2024-07-01 12:16:23.396600 -- AlientechLinkService isAuth_success -- 
2024-07-01 12:16:23.752200 -- AlientechLinkService processResponse -- 
2024-07-01 12:16:23.755000 --  --- $log->save() --  ---184 -- 
2024-07-01 12:16:23.758100 --  --- $log->save() --  ---185 -- 
2024-07-01 12:16:23.758200 -- slot [91a01f65-6d44-461e-a72a-f940c64a56bf] closeSlot null -- 
2024-07-01 12:16:23.758700 -- slot [4f05bb24-90a9-4899-8219-1f52c50aaf8f] dateDiff {"y":0,"m":0,"d":2,"h":5,"i":47,"s":3,"f":0.295308,"invert":1,"days":2,"from_string":false} -- 
2024-07-01 12:16:23.758900 -- FileSlotService closeSlot -- 
2024-07-01 12:16:23.759100 -- /api/kess3/file-slots/4f05bb24-90a9-4899-8219-1f52c50aaf8f/close  -- 
2024-07-01 12:16:23.759300 -- AlientechLinkService processRequest -- 
2024-07-01 12:16:23.759500 -- isAuth -- 
2024-07-01 12:16:23.759800 -- AlientechLinkService isAuth_success -- 
2024-07-01 12:16:24.055000 -- AlientechLinkService processResponse -- 
2024-07-01 12:16:24.056600 --  --- $log->save() --  ---186 -- 
2024-07-01 12:16:24.058100 --  --- $log->save() --  ---187 -- 
2024-07-01 12:16:24.058200 -- slot [4f05bb24-90a9-4899-8219-1f52c50aaf8f] closeSlot null -- 
2024-07-01 12:16:24.058300 -- closeAllSlots finish -- 
2024-07-01 12:16:24.058400 -- AsyncOperationService startOperationDecode -- 
2024-07-01 12:16:24.058500 -- AlientechLinkService processRequest -- 
2024-07-01 12:16:24.058600 -- isAuth -- 
2024-07-01 12:16:24.058700 -- AlientechLinkService isAuth_success -- 
2024-07-01 12:16:24.560800 -- AlientechLinkService processResponse -- 
2024-07-01 12:16:24.563300 --  --- $log->save() --  ---188 -- 
2024-07-01 12:16:24.565800 --  --- $log->save() --  ---189 -- 
2024-07-01 12:16:24.566100 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 12:16:24.566200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 12:16:24.578000 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-01 12:16:29.450700 -- AlientechLinkService __construct -- 
2024-07-01 12:16:29.451000 -- AlientechLinkService processClient -- 
2024-07-01 12:16:29.451100 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 12:16:29.451300 -- initAccessToken -- 
2024-07-01 12:16:29.453100 -- iR8V9-KAuaE -- 
2024-07-01 12:16:29.453400 -- AlientechProjectService __construct -- 
2024-07-01 12:16:29.453700 -- FileSlotService __construct -- 
2024-07-01 12:16:29.453700 -- FileSlotService __construct -- 
2024-07-01 12:16:29.453800 -- AsyncOperationService __construct -- 
2024-07-01 12:16:29.454000 -- ApiController actionKess3Decoded -- 
2024-07-01 12:16:29.458900 --  --- $log->save() --  ---190 -- 
2024-07-01 12:16:29.459000 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 12:16:29.459000 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 12:16:29.462500 -- AsyncOperationService processCompletedOperation -- 
2024-07-01 12:16:29.462600 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-01 12:16:29.464800 -- AsyncOperationService finishCompletedOperation -- 
2024-07-01 12:16:29.464900 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-01 12:16:29.465000 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-01 12:16:29.468800 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-01 12:16:30.033300 -- AlientechLinkService __construct -- 
2024-07-01 12:16:30.033800 -- AlientechLinkService processClient -- 
2024-07-01 12:16:30.034100 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 12:16:30.034700 -- initAccessToken -- 
2024-07-01 12:16:30.037100 -- iR8V9-KAuaE -- 
2024-07-01 12:16:30.037800 -- AlientechProjectService __construct -- 
2024-07-01 12:16:30.038900 -- FileSlotService __construct -- 
2024-07-01 12:16:30.039200 -- FileSlotService __construct -- 
2024-07-01 12:16:30.039400 -- AsyncOperationService __construct -- 
2024-07-01 12:16:30.039900 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-01 12:16:30.040600 -- FileService downloadFile -- 
2024-07-01 12:16:30.040800 -- AlientechLinkService processRequest -- 
2024-07-01 12:16:30.041000 -- isAuth -- 
2024-07-01 12:16:30.041300 -- AlientechLinkService isAuth_success -- 
2024-07-01 12:16:31.107500 -- AlientechLinkService processResponse -- 
2024-07-01 12:16:31.123400 --  --- $log->save() --  ---191 -- 
2024-07-01 12:16:31.488100 --  --- $log->save() --  ---192 -- 
2024-07-01 12:16:31.516800 -- FileService saveDecodedFile -- 
2024-07-01 12:16:31.565500 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-01 12:44:13.290500 -- AlientechLinkService __construct -- 
2024-07-01 12:44:13.290700 -- AlientechLinkService processClient -- 
2024-07-01 12:44:13.290800 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 12:44:13.291300 -- initAccessToken -- 
2024-07-01 12:44:13.294600 -- iR8V9-KAuaE -- 
2024-07-01 12:44:13.296300 -- AlientechProjectService __construct -- 
2024-07-01 12:44:13.299000 -- FileSlotService __construct -- 
2024-07-01 12:44:13.299400 -- FileSlotService __construct -- 
2024-07-01 12:44:13.299500 -- AsyncOperationService __construct -- 
2024-07-01 12:44:13.301100 -- FileSlotService __construct -- 
2024-07-01 12:44:13.301200 -- Kess3Service __construct -- 
2024-07-01 12:44:13.301300 -- Kess3Service startEncoding -- 
2024-07-01 12:44:13.301400 -- AsyncOperationService startOperationEncode -- 
2024-07-01 12:44:13.330700 -- FileService uploadFiles -- 
2024-07-01 12:44:13.331900 -- AlientechLinkService processRequest -- 
2024-07-01 12:44:13.332000 -- isAuth -- 
2024-07-01 12:44:13.332200 -- AlientechLinkService isAuth_success -- 
2024-07-01 12:44:13.974600 -- AlientechLinkService processResponse -- 
2024-07-01 12:44:13.985700 --  --- $log->save() --  ---193 -- 
2024-07-01 12:44:13.985900 -- response_status=400 -- 
2024-07-01 12:44:13.986000 -- response=Http-Code: 400
Content-Type: text/plain; charset=utf-8
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Mon, 01 Jul 2024 12:44:13 GMT
Connection: close

KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-01 12:44:13.986100 -- content=KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-01 12:44:13.991000 -- AsyncOperationService startOperationEncode no_filled_data -- 
2024-07-01 12:44:13.991100 -- no_AsyncOperationDto -- 
2024-07-01 12:45:41.239200 -- AlientechLinkService __construct -- 
2024-07-01 12:45:41.239800 -- AlientechLinkService processClient -- 
2024-07-01 12:45:41.240100 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 12:45:41.240900 -- initAccessToken -- 
2024-07-01 12:45:41.243900 -- iR8V9-KAuaE -- 
2024-07-01 12:45:41.244700 -- AlientechProjectService __construct -- 
2024-07-01 12:45:41.245800 -- FileSlotService __construct -- 
2024-07-01 12:45:41.246200 -- FileSlotService __construct -- 
2024-07-01 12:45:41.246500 -- AsyncOperationService __construct -- 
2024-07-01 12:45:41.247200 -- FileSlotService __construct -- 
2024-07-01 12:45:41.247500 -- Kess3Service __construct -- 
2024-07-01 12:45:41.247800 -- Kess3Service startEncoding -- 
2024-07-01 12:45:41.248000 -- AsyncOperationService startOperationEncode -- 
2024-07-01 12:45:41.265600 -- FileService uploadFiles -- 
2024-07-01 12:45:41.266300 -- AlientechLinkService processRequest -- 
2024-07-01 12:45:41.266700 -- isAuth -- 
2024-07-01 12:45:41.266900 -- AlientechLinkService isAuth_success -- 
2024-07-01 12:45:43.026900 -- AlientechLinkService processResponse -- 
2024-07-01 12:45:43.038400 --  --- $log->save() --  ---194 -- 
2024-07-01 12:45:43.038600 -- response_status=400 -- 
2024-07-01 12:45:43.038700 -- response=Http-Code: 400
Content-Type: text/plain; charset=utf-8
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Mon, 01 Jul 2024 12:45:43 GMT
Connection: close

KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-01 12:45:43.038800 -- content=KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-01 12:45:43.045800 -- AsyncOperationService startOperationEncode no_filled_data -- 
2024-07-01 12:45:43.046000 -- no_AsyncOperationDto -- 
2024-07-01 13:03:18.532500 -- AlientechLinkService __construct -- 
2024-07-01 13:03:18.532700 -- AlientechLinkService processClient -- 
2024-07-01 13:03:18.532800 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 13:03:18.533200 -- initAccessToken -- 
2024-07-01 13:03:18.536400 -- iR8V9-KAuaE -- 
2024-07-01 13:03:18.536900 -- AlientechProjectService __construct -- 
2024-07-01 13:03:18.537800 -- FileSlotService __construct -- 
2024-07-01 13:03:18.537900 -- FileSlotService __construct -- 
2024-07-01 13:03:18.538000 -- AsyncOperationService __construct -- 
2024-07-01 13:03:18.538500 -- FileSlotService __construct -- 
2024-07-01 13:03:18.538600 -- Kess3Service __construct -- 
2024-07-01 13:03:18.538600 -- Kess3Service startDecoding -- 
2024-07-01 13:03:18.538700 -- AsyncOperationService startOperationDecode -- 
2024-07-01 13:03:18.538700 -- AlientechLinkService processRequest -- 
2024-07-01 13:03:18.538700 -- isAuth -- 
2024-07-01 13:03:18.538700 -- AlientechLinkService isAuth_success -- 
2024-07-01 13:03:19.465000 -- AlientechLinkService processResponse -- 
2024-07-01 13:03:19.486600 --  --- $log->save() --  ---195 -- 
2024-07-01 13:03:19.488400 --  --- $log->save() --  ---196 -- 
2024-07-01 13:03:19.488500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 13:03:19.488500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 13:03:19.495000 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-01 13:03:21.824200 -- AlientechLinkService __construct -- 
2024-07-01 13:03:21.824600 -- AlientechLinkService processClient -- 
2024-07-01 13:03:21.824600 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 13:03:21.824800 -- initAccessToken -- 
2024-07-01 13:03:21.826900 -- iR8V9-KAuaE -- 
2024-07-01 13:03:21.827300 -- AlientechProjectService __construct -- 
2024-07-01 13:03:21.827700 -- FileSlotService __construct -- 
2024-07-01 13:03:21.827900 -- FileSlotService __construct -- 
2024-07-01 13:03:21.828000 -- AsyncOperationService __construct -- 
2024-07-01 13:03:21.828300 -- ApiController actionKess3Decoded -- 
2024-07-01 13:03:21.832100 --  --- $log->save() --  ---197 -- 
2024-07-01 13:03:21.832300 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 13:03:21.832300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 13:03:21.834200 -- AsyncOperationService processCompletedOperation -- 
2024-07-01 13:03:21.834300 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-01 13:03:21.836800 -- AsyncOperationService finishCompletedOperation -- 
2024-07-01 13:03:21.837000 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-01 13:03:21.837000 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-01 13:03:21.839400 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-01 13:03:24.481700 -- AlientechLinkService __construct -- 
2024-07-01 13:03:24.481900 -- AlientechLinkService processClient -- 
2024-07-01 13:03:24.481900 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 13:03:24.482600 -- initAccessToken -- 
2024-07-01 13:03:24.486200 -- iR8V9-KAuaE -- 
2024-07-01 13:03:24.488400 -- AlientechProjectService __construct -- 
2024-07-01 13:03:24.491400 -- FileSlotService __construct -- 
2024-07-01 13:03:24.491900 -- FileSlotService __construct -- 
2024-07-01 13:03:24.492100 -- AsyncOperationService __construct -- 
2024-07-01 13:03:24.492600 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-01 13:03:24.494200 -- FileService downloadFile -- 
2024-07-01 13:03:24.494300 -- AlientechLinkService processRequest -- 
2024-07-01 13:03:24.494400 -- isAuth -- 
2024-07-01 13:03:24.494500 -- AlientechLinkService isAuth_success -- 
2024-07-01 13:03:24.889200 -- AlientechLinkService processResponse -- 
2024-07-01 13:03:24.910300 --  --- $log->save() --  ---198 -- 
2024-07-01 13:03:24.924900 --  --- $log->save() --  ---199 -- 
2024-07-01 13:03:24.926000 -- FileService saveDecodedFile -- 
2024-07-01 13:03:24.943400 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-01 13:03:25.007700 -- FileService downloadFile -- 
2024-07-01 13:03:25.007800 -- AlientechLinkService processRequest -- 
2024-07-01 13:03:25.007900 -- isAuth -- 
2024-07-01 13:03:25.007900 -- AlientechLinkService isAuth_success -- 
2024-07-01 13:03:26.020500 -- AlientechLinkService processResponse -- 
2024-07-01 13:03:26.022300 --  --- $log->save() --  ---200 -- 
2024-07-01 13:03:26.196100 --  --- $log->save() --  ---201 -- 
2024-07-01 13:03:26.217000 -- FileService saveDecodedFile -- 
2024-07-01 13:03:26.240200 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-01 13:08:39.969600 -- AlientechLinkService __construct -- 
2024-07-01 13:08:39.969900 -- AlientechLinkService processClient -- 
2024-07-01 13:08:39.969900 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 13:08:39.970400 -- initAccessToken -- 
2024-07-01 13:08:39.973400 -- iR8V9-KAuaE -- 
2024-07-01 13:08:39.974800 -- AlientechProjectService __construct -- 
2024-07-01 13:08:39.976900 -- FileSlotService __construct -- 
2024-07-01 13:08:39.977300 -- FileSlotService __construct -- 
2024-07-01 13:08:39.977400 -- AsyncOperationService __construct -- 
2024-07-01 13:08:39.978700 -- FileSlotService __construct -- 
2024-07-01 13:08:39.978900 -- Kess3Service __construct -- 
2024-07-01 13:08:39.978900 -- Kess3Service startEncoding -- 
2024-07-01 13:08:39.979000 -- AsyncOperationService startOperationEncode -- 
2024-07-01 13:08:40.007900 -- FileService uploadFiles -- 
2024-07-01 13:08:40.008200 -- AlientechLinkService processRequest -- 
2024-07-01 13:08:40.008300 -- isAuth -- 
2024-07-01 13:08:40.008300 -- AlientechLinkService isAuth_success -- 
2024-07-01 13:08:40.704400 -- AlientechLinkService processResponse -- 
2024-07-01 13:08:40.714300 --  --- $log->save() --  ---202 -- 
2024-07-01 13:08:40.715900 --  --- $log->save() --  ---203 -- 
2024-07-01 13:08:40.726800 -- AlientechLinkService processRequest -- 
2024-07-01 13:08:40.727000 -- isAuth -- 
2024-07-01 13:08:40.727100 -- AlientechLinkService isAuth_success -- 
2024-07-01 13:08:42.072600 -- AlientechLinkService processResponse -- 
2024-07-01 13:08:42.074800 --  --- $log->save() --  ---204 -- 
2024-07-01 13:08:42.076400 --  --- $log->save() --  ---205 -- 
2024-07-01 13:08:42.076500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 13:08:42.076500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 13:08:42.077100 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-01 13:08:44.421000 -- AlientechLinkService __construct -- 
2024-07-01 13:08:44.421200 -- AlientechLinkService processClient -- 
2024-07-01 13:08:44.421300 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 13:08:44.421400 -- initAccessToken -- 
2024-07-01 13:08:44.421900 -- iR8V9-KAuaE -- 
2024-07-01 13:08:44.422200 -- AlientechProjectService __construct -- 
2024-07-01 13:08:44.422300 -- FileSlotService __construct -- 
2024-07-01 13:08:44.422400 -- FileSlotService __construct -- 
2024-07-01 13:08:44.422500 -- AsyncOperationService __construct -- 
2024-07-01 13:08:44.422700 -- ApiController actionKess3Encoded -- 
2024-07-01 13:08:44.424600 --  --- $log->save() --  ---206 -- 
2024-07-01 13:08:44.424700 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 13:08:44.424800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 13:08:44.425700 -- AsyncOperationService processCompletedOperation -- 
2024-07-01 13:08:44.425700 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-01 13:08:44.427500 -- AsyncOperationService finishCompletedOperation -- 
2024-07-01 13:08:44.427600 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-01 13:08:44.427700 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-01 13:08:44.429200 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-01 13:08:46.940900 -- AlientechLinkService __construct -- 
2024-07-01 13:08:46.941100 -- AlientechLinkService processClient -- 
2024-07-01 13:08:46.941100 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 13:08:46.941500 -- initAccessToken -- 
2024-07-01 13:08:46.944500 -- iR8V9-KAuaE -- 
2024-07-01 13:08:46.947300 -- AlientechProjectService __construct -- 
2024-07-01 13:08:46.951000 -- FileSlotService __construct -- 
2024-07-01 13:08:46.952200 -- FileSlotService __construct -- 
2024-07-01 13:08:46.952400 -- AsyncOperationService __construct -- 
2024-07-01 13:08:46.954000 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-01 13:08:46.954900 -- FileService downloadFile -- 
2024-07-01 13:08:46.955000 -- AlientechLinkService processRequest -- 
2024-07-01 13:08:46.955600 -- isAuth -- 
2024-07-01 13:08:46.955900 -- AlientechLinkService isAuth_success -- 
2024-07-01 13:08:47.449400 -- AlientechLinkService processResponse -- 
2024-07-01 13:08:47.475500 --  --- $log->save() --  ---207 -- 
2024-07-01 13:08:47.549100 --  --- $log->save() --  ---208 -- 
2024-07-01 13:08:47.556800 -- FileService saveEncodedFile -- 
2024-07-01 13:08:47.577900 -- FileService $initFile->file_history={"id":"7592","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-01 13:08:47.589200 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-01 14:24:32.497400 -- AlientechLinkService __construct -- 
2024-07-01 14:24:32.497700 -- AlientechLinkService processClient -- 
2024-07-01 14:24:32.497700 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 14:24:32.498100 -- initAccessToken -- 
2024-07-01 14:24:32.501300 -- iR8V9-KAuaE -- 
2024-07-01 14:24:32.502100 -- AlientechProjectService __construct -- 
2024-07-01 14:24:32.503300 -- FileSlotService __construct -- 
2024-07-01 14:24:32.503600 -- FileSlotService __construct -- 
2024-07-01 14:24:32.503700 -- AsyncOperationService __construct -- 
2024-07-01 14:24:32.504400 -- FileSlotService __construct -- 
2024-07-01 14:24:32.504500 -- Kess3Service __construct -- 
2024-07-01 14:24:32.504500 -- Kess3Service startEncoding -- 
2024-07-01 14:24:32.504600 -- AsyncOperationService startOperationEncode -- 
2024-07-01 14:24:32.530900 -- FileService uploadFiles -- 
2024-07-01 14:24:32.531400 -- AlientechLinkService processRequest -- 
2024-07-01 14:24:32.531400 -- isAuth -- 
2024-07-01 14:24:32.531500 -- AlientechLinkService isAuth_success -- 
2024-07-01 14:24:34.430600 -- AlientechLinkService processResponse -- 
2024-07-01 14:24:34.439100 --  --- $log->save() --  ---209 -- 
2024-07-01 14:24:34.440900 --  --- $log->save() --  ---210 -- 
2024-07-01 14:24:34.447100 -- AlientechLinkService processRequest -- 
2024-07-01 14:24:34.447200 -- isAuth -- 
2024-07-01 14:24:34.447300 -- AlientechLinkService isAuth_success -- 
2024-07-01 14:24:35.214300 -- AlientechLinkService processResponse -- 
2024-07-01 14:24:35.217400 --  --- $log->save() --  ---211 -- 
2024-07-01 14:24:35.219400 --  --- $log->save() --  ---212 -- 
2024-07-01 14:24:35.219600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 14:24:35.219800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 14:24:35.221300 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-01 14:25:30.703300 -- AlientechLinkService __construct -- 
2024-07-01 14:25:30.703500 -- AlientechLinkService processClient -- 
2024-07-01 14:25:30.703500 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 14:25:30.703600 -- initAccessToken -- 
2024-07-01 14:25:30.704100 -- iR8V9-KAuaE -- 
2024-07-01 14:25:30.704200 -- AlientechProjectService __construct -- 
2024-07-01 14:25:30.704300 -- FileSlotService __construct -- 
2024-07-01 14:25:30.704300 -- FileSlotService __construct -- 
2024-07-01 14:25:30.704400 -- AsyncOperationService __construct -- 
2024-07-01 14:25:30.704500 -- ApiController actionKess3Encoded -- 
2024-07-01 14:25:30.708300 --  --- $log->save() --  ---213 -- 
2024-07-01 14:25:30.708400 -- AsyncOperationService createOperationDtoByData -- 
2024-07-01 14:25:30.708400 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-01 14:25:30.711300 -- AsyncOperationService processCompletedOperation -- 
2024-07-01 14:25:30.711300 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-01 14:25:30.713400 -- AsyncOperationService finishCompletedOperation -- 
2024-07-01 14:25:30.713600 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-01 14:25:30.713600 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-01 14:25:30.716700 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-01 14:25:32.493000 -- AlientechLinkService __construct -- 
2024-07-01 14:25:32.493300 -- AlientechLinkService processClient -- 
2024-07-01 14:25:32.493300 -- AlientechLinkService is_null($this->client) -- 
2024-07-01 14:25:32.493700 -- initAccessToken -- 
2024-07-01 14:25:32.496600 -- iR8V9-KAuaE -- 
2024-07-01 14:25:32.497900 -- AlientechProjectService __construct -- 
2024-07-01 14:25:32.500000 -- FileSlotService __construct -- 
2024-07-01 14:25:32.500400 -- FileSlotService __construct -- 
2024-07-01 14:25:32.500500 -- AsyncOperationService __construct -- 
2024-07-01 14:25:32.500900 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-01 14:25:32.501300 -- FileService downloadFile -- 
2024-07-01 14:25:32.501400 -- AlientechLinkService processRequest -- 
2024-07-01 14:25:32.501400 -- isAuth -- 
2024-07-01 14:25:32.501500 -- AlientechLinkService isAuth_success -- 
2024-07-01 14:25:34.224500 -- AlientechLinkService processResponse -- 
2024-07-01 14:25:34.249500 --  --- $log->save() --  ---214 -- 
2024-07-01 14:25:34.643800 --  --- $log->save() --  ---215 -- 
2024-07-01 14:25:34.690500 -- FileService saveEncodedFile -- 
2024-07-01 14:25:34.745900 -- FileService $initFile->file_history={"id":"7591","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-01 14:25:34.763500 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-02 07:42:57.517700 -- AlientechLinkService __construct -- 
2024-07-02 07:42:57.517900 -- AlientechLinkService processClient -- 
2024-07-02 07:42:57.517900 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 07:42:57.518300 -- initAccessToken -- 
2024-07-02 07:42:57.521300 -- iR8V9-KAuaE -- 
2024-07-02 07:42:57.521900 -- AlientechProjectService __construct -- 
2024-07-02 07:42:57.523500 -- FileSlotService __construct -- 
2024-07-02 07:42:57.524000 -- FileSlotService __construct -- 
2024-07-02 07:42:57.524100 -- AsyncOperationService __construct -- 
2024-07-02 07:42:57.524800 -- FileSlotService __construct -- 
2024-07-02 07:42:57.524900 -- Kess3Service __construct -- 
2024-07-02 07:42:57.524900 -- Kess3Service startDecoding -- 
2024-07-02 07:42:57.524900 -- AsyncOperationService startOperationDecode -- 
2024-07-02 07:42:57.525000 -- AlientechLinkService processRequest -- 
2024-07-02 07:42:57.525000 -- isAuth -- 
2024-07-02 07:42:57.525000 -- AlientechLinkService isAuth_success -- 
2024-07-02 07:42:58.150600 -- AlientechLinkService processResponse -- 
2024-07-02 07:42:58.170300 --  --- $log->save() --  ---216 -- 
2024-07-02 07:42:58.171400 --  --- $log->save() --  ---217 -- 
2024-07-02 07:42:58.171500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 07:42:58.171600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 07:42:58.181400 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-02 07:43:00.646400 -- AlientechLinkService __construct -- 
2024-07-02 07:43:00.646600 -- AlientechLinkService processClient -- 
2024-07-02 07:43:00.646700 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 07:43:00.646800 -- initAccessToken -- 
2024-07-02 07:43:00.648500 -- iR8V9-KAuaE -- 
2024-07-02 07:43:00.648700 -- AlientechProjectService __construct -- 
2024-07-02 07:43:00.648900 -- FileSlotService __construct -- 
2024-07-02 07:43:00.648900 -- FileSlotService __construct -- 
2024-07-02 07:43:00.649000 -- AsyncOperationService __construct -- 
2024-07-02 07:43:00.649100 -- ApiController actionKess3Decoded -- 
2024-07-02 07:43:00.654000 --  --- $log->save() --  ---218 -- 
2024-07-02 07:43:00.654200 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 07:43:00.654200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 07:43:00.657100 -- AsyncOperationService processCompletedOperation -- 
2024-07-02 07:43:00.657200 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-02 07:43:00.659600 -- AsyncOperationService finishCompletedOperation -- 
2024-07-02 07:43:00.659700 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-02 07:43:00.659800 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-02 07:43:00.662600 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-02 07:43:02.819600 -- AlientechLinkService __construct -- 
2024-07-02 07:43:02.819800 -- AlientechLinkService processClient -- 
2024-07-02 07:43:02.819900 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 07:43:02.820400 -- initAccessToken -- 
2024-07-02 07:43:02.823300 -- iR8V9-KAuaE -- 
2024-07-02 07:43:02.824600 -- AlientechProjectService __construct -- 
2024-07-02 07:43:02.826800 -- FileSlotService __construct -- 
2024-07-02 07:43:02.827600 -- FileSlotService __construct -- 
2024-07-02 07:43:02.827800 -- AsyncOperationService __construct -- 
2024-07-02 07:43:02.829100 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-02 07:43:02.830500 -- FileService downloadFile -- 
2024-07-02 07:43:02.830600 -- AlientechLinkService processRequest -- 
2024-07-02 07:43:02.831100 -- isAuth -- 
2024-07-02 07:43:02.831600 -- AlientechLinkService isAuth_success -- 
2024-07-02 07:43:03.387800 -- AlientechLinkService processResponse -- 
2024-07-02 07:43:03.410900 --  --- $log->save() --  ---219 -- 
2024-07-02 07:43:03.437700 --  --- $log->save() --  ---220 -- 
2024-07-02 07:43:03.439000 -- FileService saveDecodedFile -- 
2024-07-02 07:43:03.453600 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-02 07:43:03.514200 -- FileService downloadFile -- 
2024-07-02 07:43:03.514400 -- AlientechLinkService processRequest -- 
2024-07-02 07:43:03.514500 -- isAuth -- 
2024-07-02 07:43:03.514600 -- AlientechLinkService isAuth_success -- 
2024-07-02 07:43:04.573700 -- AlientechLinkService processResponse -- 
2024-07-02 07:43:04.576600 --  --- $log->save() --  ---221 -- 
2024-07-02 07:43:04.922600 --  --- $log->save() --  ---222 -- 
2024-07-02 07:43:04.959300 -- FileService saveDecodedFile -- 
2024-07-02 07:43:04.983200 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-02 08:31:39.939200 -- AlientechLinkService __construct -- 
2024-07-02 08:31:39.939500 -- AlientechLinkService processClient -- 
2024-07-02 08:31:39.939500 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 08:31:39.939900 -- initAccessToken -- 
2024-07-02 08:31:39.942800 -- iR8V9-KAuaE -- 
2024-07-02 08:31:39.944900 -- AlientechProjectService __construct -- 
2024-07-02 08:31:39.948300 -- FileSlotService __construct -- 
2024-07-02 08:31:39.948800 -- FileSlotService __construct -- 
2024-07-02 08:31:39.949000 -- AsyncOperationService __construct -- 
2024-07-02 08:31:39.951000 -- FileSlotService __construct -- 
2024-07-02 08:31:39.951100 -- Kess3Service __construct -- 
2024-07-02 08:31:39.951200 -- Kess3Service startEncoding -- 
2024-07-02 08:31:39.951300 -- AsyncOperationService startOperationEncode -- 
2024-07-02 08:31:39.979200 -- FileService uploadFiles -- 
2024-07-02 08:31:39.979600 -- AlientechLinkService processRequest -- 
2024-07-02 08:31:39.979700 -- isAuth -- 
2024-07-02 08:31:39.979700 -- AlientechLinkService isAuth_success -- 
2024-07-02 08:31:41.176500 -- AlientechLinkService processResponse -- 
2024-07-02 08:31:41.188100 --  --- $log->save() --  ---223 -- 
2024-07-02 08:31:41.190100 --  --- $log->save() --  ---224 -- 
2024-07-02 08:31:41.198200 -- AlientechLinkService processRequest -- 
2024-07-02 08:31:41.198300 -- isAuth -- 
2024-07-02 08:31:41.198400 -- AlientechLinkService isAuth_success -- 
2024-07-02 08:31:42.520900 -- AlientechLinkService processResponse -- 
2024-07-02 08:31:42.523800 --  --- $log->save() --  ---225 -- 
2024-07-02 08:31:42.526700 --  --- $log->save() --  ---226 -- 
2024-07-02 08:31:42.526900 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 08:31:42.527300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 08:31:42.528500 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-02 08:31:45.749900 -- AlientechLinkService __construct -- 
2024-07-02 08:31:45.750300 -- AlientechLinkService processClient -- 
2024-07-02 08:31:45.750400 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 08:31:45.750600 -- initAccessToken -- 
2024-07-02 08:31:45.752600 -- iR8V9-KAuaE -- 
2024-07-02 08:31:45.753000 -- AlientechProjectService __construct -- 
2024-07-02 08:31:45.753400 -- FileSlotService __construct -- 
2024-07-02 08:31:45.753600 -- FileSlotService __construct -- 
2024-07-02 08:31:45.753700 -- AsyncOperationService __construct -- 
2024-07-02 08:31:45.754000 -- ApiController actionKess3Encoded -- 
2024-07-02 08:31:45.757600 --  --- $log->save() --  ---227 -- 
2024-07-02 08:31:45.757700 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 08:31:45.757800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 08:31:45.759500 -- AsyncOperationService processCompletedOperation -- 
2024-07-02 08:31:45.759600 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-02 08:31:45.762200 -- AsyncOperationService finishCompletedOperation -- 
2024-07-02 08:31:45.762300 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-02 08:31:45.762400 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-02 08:31:45.766500 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-02 08:31:45.899500 -- AlientechLinkService __construct -- 
2024-07-02 08:31:45.899700 -- AlientechLinkService processClient -- 
2024-07-02 08:31:45.899700 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 08:31:45.900000 -- initAccessToken -- 
2024-07-02 08:31:45.902700 -- iR8V9-KAuaE -- 
2024-07-02 08:31:45.903300 -- AlientechProjectService __construct -- 
2024-07-02 08:31:45.904100 -- FileSlotService __construct -- 
2024-07-02 08:31:45.904300 -- FileSlotService __construct -- 
2024-07-02 08:31:45.904300 -- AsyncOperationService __construct -- 
2024-07-02 08:31:45.904500 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-02 08:31:45.904600 -- FileService downloadFile -- 
2024-07-02 08:31:45.904600 -- AlientechLinkService processRequest -- 
2024-07-02 08:31:45.904600 -- isAuth -- 
2024-07-02 08:31:45.904700 -- AlientechLinkService isAuth_success -- 
2024-07-02 08:31:46.596500 -- AlientechLinkService processResponse -- 
2024-07-02 08:31:46.619500 --  --- $log->save() --  ---228 -- 
2024-07-02 08:31:46.775700 --  --- $log->save() --  ---229 -- 
2024-07-02 08:31:46.810700 -- FileService saveEncodedFile -- 
2024-07-02 08:31:46.843400 -- FileService $initFile->file_history={"id":"7594","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-02 08:31:46.853700 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-02 09:27:06.176000 -- AlientechLinkService __construct -- 
2024-07-02 09:27:06.176400 -- AlientechLinkService processClient -- 
2024-07-02 09:27:06.176500 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 09:27:06.177000 -- initAccessToken -- 
2024-07-02 09:27:06.180800 -- iR8V9-KAuaE -- 
2024-07-02 09:27:06.181700 -- AlientechProjectService __construct -- 
2024-07-02 09:27:06.182900 -- FileSlotService __construct -- 
2024-07-02 09:27:06.183100 -- FileSlotService __construct -- 
2024-07-02 09:27:06.183200 -- AsyncOperationService __construct -- 
2024-07-02 09:27:06.183700 -- FileSlotService __construct -- 
2024-07-02 09:27:06.183800 -- Kess3Service __construct -- 
2024-07-02 09:27:06.183900 -- Kess3Service startEncoding -- 
2024-07-02 09:27:06.183900 -- AsyncOperationService startOperationEncode -- 
2024-07-02 09:27:06.203300 -- FileService uploadFiles -- 
2024-07-02 09:27:06.203700 -- AlientechLinkService processRequest -- 
2024-07-02 09:27:06.203800 -- isAuth -- 
2024-07-02 09:27:06.203800 -- AlientechLinkService isAuth_success -- 
2024-07-02 09:27:08.569900 -- AlientechLinkService processResponse -- 
2024-07-02 09:27:08.579600 --  --- $log->save() --  ---230 -- 
2024-07-02 09:27:08.581500 --  --- $log->save() --  ---231 -- 
2024-07-02 09:27:08.592300 -- AlientechLinkService processRequest -- 
2024-07-02 09:27:08.592400 -- isAuth -- 
2024-07-02 09:27:08.592500 -- AlientechLinkService isAuth_success -- 
2024-07-02 09:27:09.445000 -- AlientechLinkService processResponse -- 
2024-07-02 09:27:09.447700 --  --- $log->save() --  ---232 -- 
2024-07-02 09:27:09.449900 --  --- $log->save() --  ---233 -- 
2024-07-02 09:27:09.450100 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 09:27:09.450300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 09:27:09.451600 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-02 09:28:06.703100 -- AlientechLinkService __construct -- 
2024-07-02 09:28:06.703500 -- AlientechLinkService processClient -- 
2024-07-02 09:28:06.703600 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 09:28:06.703800 -- initAccessToken -- 
2024-07-02 09:28:06.704700 -- iR8V9-KAuaE -- 
2024-07-02 09:28:06.704900 -- AlientechProjectService __construct -- 
2024-07-02 09:28:06.705200 -- FileSlotService __construct -- 
2024-07-02 09:28:06.705300 -- FileSlotService __construct -- 
2024-07-02 09:28:06.705400 -- AsyncOperationService __construct -- 
2024-07-02 09:28:06.705700 -- ApiController actionKess3Encoded -- 
2024-07-02 09:28:06.712900 --  --- $log->save() --  ---234 -- 
2024-07-02 09:28:06.713100 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 09:28:06.713200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 09:28:06.717500 -- AsyncOperationService processCompletedOperation -- 
2024-07-02 09:28:06.717600 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-02 09:28:06.720200 -- AsyncOperationService finishCompletedOperation -- 
2024-07-02 09:28:06.720300 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-02 09:28:06.720400 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-02 09:28:06.723600 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-02 09:28:07.375800 -- AlientechLinkService __construct -- 
2024-07-02 09:28:07.376000 -- AlientechLinkService processClient -- 
2024-07-02 09:28:07.376000 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 09:28:07.376400 -- initAccessToken -- 
2024-07-02 09:28:07.379100 -- iR8V9-KAuaE -- 
2024-07-02 09:28:07.379600 -- AlientechProjectService __construct -- 
2024-07-02 09:28:07.380400 -- FileSlotService __construct -- 
2024-07-02 09:28:07.380500 -- FileSlotService __construct -- 
2024-07-02 09:28:07.380600 -- AsyncOperationService __construct -- 
2024-07-02 09:28:07.380700 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-02 09:28:07.380900 -- FileService downloadFile -- 
2024-07-02 09:28:07.380900 -- AlientechLinkService processRequest -- 
2024-07-02 09:28:07.380900 -- isAuth -- 
2024-07-02 09:28:07.381000 -- AlientechLinkService isAuth_success -- 
2024-07-02 09:28:08.370900 -- AlientechLinkService processResponse -- 
2024-07-02 09:28:08.398500 --  --- $log->save() --  ---235 -- 
2024-07-02 09:28:08.746300 --  --- $log->save() --  ---236 -- 
2024-07-02 09:28:08.781900 -- FileService saveEncodedFile -- 
2024-07-02 09:28:08.819300 -- FileService $initFile->file_history={"id":"7591","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-02 09:28:08.828900 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-02 10:40:38.192700 -- AlientechLinkService __construct -- 
2024-07-02 10:40:38.192900 -- AlientechLinkService processClient -- 
2024-07-02 10:40:38.192900 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 10:40:38.193400 -- initAccessToken -- 
2024-07-02 10:40:38.197600 -- iR8V9-KAuaE -- 
2024-07-02 10:40:38.198300 -- AlientechProjectService __construct -- 
2024-07-02 10:40:38.199300 -- FileSlotService __construct -- 
2024-07-02 10:40:38.199400 -- FileSlotService __construct -- 
2024-07-02 10:40:38.199500 -- AsyncOperationService __construct -- 
2024-07-02 10:40:38.200100 -- FileSlotService __construct -- 
2024-07-02 10:40:38.200100 -- Kess3Service __construct -- 
2024-07-02 10:40:38.200100 -- Kess3Service startEncoding -- 
2024-07-02 10:40:38.200200 -- AsyncOperationService startOperationEncode -- 
2024-07-02 10:40:38.228100 -- FileService uploadFiles -- 
2024-07-02 10:40:38.228600 -- AlientechLinkService processRequest -- 
2024-07-02 10:40:38.228700 -- isAuth -- 
2024-07-02 10:40:38.228800 -- AlientechLinkService isAuth_success -- 
2024-07-02 10:40:39.408500 -- AlientechLinkService processResponse -- 
2024-07-02 10:40:39.420900 --  --- $log->save() --  ---237 -- 
2024-07-02 10:40:39.425300 --  --- $log->save() --  ---238 -- 
2024-07-02 10:40:39.433900 -- AlientechLinkService processRequest -- 
2024-07-02 10:40:39.434100 -- isAuth -- 
2024-07-02 10:40:39.434300 -- AlientechLinkService isAuth_success -- 
2024-07-02 10:40:40.312600 -- AlientechLinkService processResponse -- 
2024-07-02 10:40:40.315700 --  --- $log->save() --  ---239 -- 
2024-07-02 10:40:40.317600 --  --- $log->save() --  ---240 -- 
2024-07-02 10:40:40.317800 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 10:40:40.318000 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 10:40:40.319500 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-02 10:41:21.285100 -- AlientechLinkService __construct -- 
2024-07-02 10:41:21.285200 -- AlientechLinkService processClient -- 
2024-07-02 10:41:21.285200 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 10:41:21.285300 -- initAccessToken -- 
2024-07-02 10:41:21.285700 -- iR8V9-KAuaE -- 
2024-07-02 10:41:21.285800 -- AlientechProjectService __construct -- 
2024-07-02 10:41:21.286000 -- FileSlotService __construct -- 
2024-07-02 10:41:21.286000 -- FileSlotService __construct -- 
2024-07-02 10:41:21.286000 -- AsyncOperationService __construct -- 
2024-07-02 10:41:21.286200 -- ApiController actionKess3Encoded -- 
2024-07-02 10:41:21.292800 --  --- $log->save() --  ---241 -- 
2024-07-02 10:41:21.293000 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 10:41:21.293000 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 10:41:21.298900 -- AsyncOperationService processCompletedOperation -- 
2024-07-02 10:41:21.299000 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-02 10:41:21.302600 -- AsyncOperationService finishCompletedOperation -- 
2024-07-02 10:41:21.302700 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-02 10:41:21.302800 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-02 10:41:21.305700 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-02 10:41:23.736600 -- AlientechLinkService __construct -- 
2024-07-02 10:41:23.736700 -- AlientechLinkService processClient -- 
2024-07-02 10:41:23.736800 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 10:41:23.737200 -- initAccessToken -- 
2024-07-02 10:41:23.740300 -- iR8V9-KAuaE -- 
2024-07-02 10:41:23.741900 -- AlientechProjectService __construct -- 
2024-07-02 10:41:23.744300 -- FileSlotService __construct -- 
2024-07-02 10:41:23.744900 -- FileSlotService __construct -- 
2024-07-02 10:41:23.745100 -- AsyncOperationService __construct -- 
2024-07-02 10:41:23.745600 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-02 10:41:23.746100 -- FileService downloadFile -- 
2024-07-02 10:41:23.746200 -- AlientechLinkService processRequest -- 
2024-07-02 10:41:23.746300 -- isAuth -- 
2024-07-02 10:41:23.746400 -- AlientechLinkService isAuth_success -- 
2024-07-02 10:41:25.929200 -- AlientechLinkService processResponse -- 
2024-07-02 10:41:25.951500 --  --- $log->save() --  ---242 -- 
2024-07-02 10:41:26.307100 --  --- $log->save() --  ---243 -- 
2024-07-02 10:41:26.351300 -- FileService saveEncodedFile -- 
2024-07-02 10:41:26.400500 -- FileService $initFile->file_history={"id":"7591","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-02 10:41:26.421200 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-02 11:49:23.007700 -- AlientechLinkService __construct -- 
2024-07-02 11:49:23.007900 -- AlientechLinkService processClient -- 
2024-07-02 11:49:23.008000 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 11:49:23.008400 -- initAccessToken -- 
2024-07-02 11:49:23.011600 -- iR8V9-KAuaE -- 
2024-07-02 11:49:23.012200 -- AlientechProjectService __construct -- 
2024-07-02 11:49:23.013000 -- FileSlotService __construct -- 
2024-07-02 11:49:23.013200 -- FileSlotService __construct -- 
2024-07-02 11:49:23.013300 -- AsyncOperationService __construct -- 
2024-07-02 11:49:23.013900 -- FileSlotService __construct -- 
2024-07-02 11:49:23.013900 -- Kess3Service __construct -- 
2024-07-02 11:49:23.014000 -- Kess3Service startDecoding -- 
2024-07-02 11:49:23.014000 -- AsyncOperationService startOperationDecode -- 
2024-07-02 11:49:23.014000 -- AlientechLinkService processRequest -- 
2024-07-02 11:49:23.014100 -- isAuth -- 
2024-07-02 11:49:23.014100 -- AlientechLinkService isAuth_success -- 
2024-07-02 11:49:24.936000 -- AlientechLinkService processResponse -- 
2024-07-02 11:49:24.956900 --  --- $log->save() --  ---244 -- 
2024-07-02 11:49:24.957100 -- response_status=429 -- 
2024-07-02 11:49:24.957300 -- response=Http-Code: 429
Content-Type: text/plain; charset=utf-8
Retry-After: 0
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Tue, 02 Jul 2024 11:49:24 GMT
Connection: close

TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-07-02 11:49:24.957300 -- content=TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-07-02 11:49:24.957400 -- FileSlotService closeAllFileSlots -- 
2024-07-02 11:49:24.957400 -- FileSlotService getSlots -- 
2024-07-02 11:49:24.957500 -- AlientechLinkService processRequest -- 
2024-07-02 11:49:24.957500 -- isAuth -- 
2024-07-02 11:49:24.957600 -- AlientechLinkService isAuth_success -- 
2024-07-02 11:49:25.126200 -- AlientechLinkService processResponse -- 
2024-07-02 11:49:25.128600 --  --- $log->save() --  ---245 -- 
2024-07-02 11:49:25.134400 --  --- $log->save() --  ---246 -- 
2024-07-02 11:49:25.135200 -- slot [51ef9f94-53df-4188-8618-59ffb212d39b] dateDiff {"y":0,"m":0,"d":0,"h":4,"i":6,"s":27,"f":0.028447,"invert":1,"days":0,"from_string":false} -- 
2024-07-02 11:49:25.135300 -- FileSlotService closeSlot -- 
2024-07-02 11:49:25.135400 -- /api/kess3/file-slots/51ef9f94-53df-4188-8618-59ffb212d39b/close  -- 
2024-07-02 11:49:25.135600 -- AlientechLinkService processRequest -- 
2024-07-02 11:49:25.135800 -- isAuth -- 
2024-07-02 11:49:25.135900 -- AlientechLinkService isAuth_success -- 
2024-07-02 11:49:25.408100 -- AlientechLinkService processResponse -- 
2024-07-02 11:49:25.410600 --  --- $log->save() --  ---247 -- 
2024-07-02 11:49:25.413000 --  --- $log->save() --  ---248 -- 
2024-07-02 11:49:25.413500 -- slot [51ef9f94-53df-4188-8618-59ffb212d39b] closeSlot null -- 
2024-07-02 11:49:25.414400 -- slot [b86dc3b6-369b-46a2-b7f4-d9fcaae0bb7c] dateDiff {"y":0,"m":0,"d":0,"h":22,"i":46,"s":6,"f":0.024377,"invert":1,"days":0,"from_string":false} -- 
2024-07-02 11:49:25.414700 -- FileSlotService closeSlot -- 
2024-07-02 11:49:25.414900 -- /api/kess3/file-slots/b86dc3b6-369b-46a2-b7f4-d9fcaae0bb7c/close  -- 
2024-07-02 11:49:25.415000 -- AlientechLinkService processRequest -- 
2024-07-02 11:49:25.415200 -- isAuth -- 
2024-07-02 11:49:25.415300 -- AlientechLinkService isAuth_success -- 
2024-07-02 11:49:25.672400 -- AlientechLinkService processResponse -- 
2024-07-02 11:49:25.675000 --  --- $log->save() --  ---249 -- 
2024-07-02 11:49:25.677200 --  --- $log->save() --  ---250 -- 
2024-07-02 11:49:25.677300 -- slot [b86dc3b6-369b-46a2-b7f4-d9fcaae0bb7c] closeSlot null -- 
2024-07-02 11:49:25.677400 -- slot [70f9eb1e-ad47-4b9a-a6aa-6b761064a272] dateDiff {"y":0,"m":0,"d":0,"h":23,"i":33,"s":1,"f":0.177407,"invert":1,"days":0,"from_string":false} -- 
2024-07-02 11:49:25.677500 -- FileSlotService closeSlot -- 
2024-07-02 11:49:25.677600 -- /api/kess3/file-slots/70f9eb1e-ad47-4b9a-a6aa-6b761064a272/close  -- 
2024-07-02 11:49:25.677600 -- AlientechLinkService processRequest -- 
2024-07-02 11:49:25.677800 -- isAuth -- 
2024-07-02 11:49:25.678000 -- AlientechLinkService isAuth_success -- 
2024-07-02 11:49:26.120300 -- AlientechLinkService processResponse -- 
2024-07-02 11:49:26.123000 --  --- $log->save() --  ---251 -- 
2024-07-02 11:49:26.125300 --  --- $log->save() --  ---252 -- 
2024-07-02 11:49:26.125500 -- slot [70f9eb1e-ad47-4b9a-a6aa-6b761064a272] closeSlot null -- 
2024-07-02 11:49:26.125600 -- closeAllSlots finish -- 
2024-07-02 11:49:26.125700 -- AsyncOperationService startOperationDecode -- 
2024-07-02 11:49:26.125800 -- AlientechLinkService processRequest -- 
2024-07-02 11:49:26.125800 -- isAuth -- 
2024-07-02 11:49:26.126100 -- AlientechLinkService isAuth_success -- 
2024-07-02 11:49:26.405500 -- AlientechLinkService processResponse -- 
2024-07-02 11:49:26.407700 --  --- $log->save() --  ---253 -- 
2024-07-02 11:49:26.410400 --  --- $log->save() --  ---254 -- 
2024-07-02 11:49:26.410600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 11:49:26.410600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 11:49:26.417300 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-02 11:49:28.494600 -- AlientechLinkService __construct -- 
2024-07-02 11:49:28.494900 -- AlientechLinkService processClient -- 
2024-07-02 11:49:28.495000 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 11:49:28.495200 -- initAccessToken -- 
2024-07-02 11:49:28.496000 -- iR8V9-KAuaE -- 
2024-07-02 11:49:28.496300 -- AlientechProjectService __construct -- 
2024-07-02 11:49:28.496500 -- FileSlotService __construct -- 
2024-07-02 11:49:28.496600 -- FileSlotService __construct -- 
2024-07-02 11:49:28.496600 -- AsyncOperationService __construct -- 
2024-07-02 11:49:28.496800 -- ApiController actionKess3Decoded -- 
2024-07-02 11:49:28.502800 --  --- $log->save() --  ---255 -- 
2024-07-02 11:49:28.503000 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 11:49:28.503100 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 11:49:28.508300 -- AsyncOperationService processCompletedOperation -- 
2024-07-02 11:49:28.508400 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-02 11:49:28.511600 -- AsyncOperationService finishCompletedOperation -- 
2024-07-02 11:49:28.511900 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-02 11:49:28.512000 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-02 11:49:28.515900 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-02 11:49:30.789800 -- AlientechLinkService __construct -- 
2024-07-02 11:49:30.790000 -- AlientechLinkService processClient -- 
2024-07-02 11:49:30.790100 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 11:49:30.790700 -- initAccessToken -- 
2024-07-02 11:49:30.793800 -- iR8V9-KAuaE -- 
2024-07-02 11:49:30.795400 -- AlientechProjectService __construct -- 
2024-07-02 11:49:30.797900 -- FileSlotService __construct -- 
2024-07-02 11:49:30.798500 -- FileSlotService __construct -- 
2024-07-02 11:49:30.798700 -- AsyncOperationService __construct -- 
2024-07-02 11:49:30.799300 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-02 11:49:30.801000 -- FileService downloadFile -- 
2024-07-02 11:49:30.801200 -- AlientechLinkService processRequest -- 
2024-07-02 11:49:30.801300 -- isAuth -- 
2024-07-02 11:49:30.801400 -- AlientechLinkService isAuth_success -- 
2024-07-02 11:49:31.144400 -- AlientechLinkService processResponse -- 
2024-07-02 11:49:31.167800 --  --- $log->save() --  ---256 -- 
2024-07-02 11:49:31.170300 --  --- $log->save() --  ---257 -- 
2024-07-02 11:49:31.170900 -- FileService saveDecodedFile -- 
2024-07-02 11:49:31.193600 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-02 11:49:31.256900 -- FileService downloadFile -- 
2024-07-02 11:49:31.257000 -- AlientechLinkService processRequest -- 
2024-07-02 11:49:31.257100 -- isAuth -- 
2024-07-02 11:49:31.257200 -- AlientechLinkService isAuth_success -- 
2024-07-02 11:49:31.710800 -- AlientechLinkService processResponse -- 
2024-07-02 11:49:31.714400 --  --- $log->save() --  ---258 -- 
2024-07-02 11:49:31.768300 --  --- $log->save() --  ---259 -- 
2024-07-02 11:49:31.771900 -- FileService saveDecodedFile -- 
2024-07-02 11:49:31.777700 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-02 12:46:11.613900 -- AlientechLinkService __construct -- 
2024-07-02 12:46:11.614100 -- AlientechLinkService processClient -- 
2024-07-02 12:46:11.614200 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 12:46:11.614600 -- initAccessToken -- 
2024-07-02 12:46:11.617600 -- iR8V9-KAuaE -- 
2024-07-02 12:46:11.618200 -- AlientechProjectService __construct -- 
2024-07-02 12:46:11.619100 -- FileSlotService __construct -- 
2024-07-02 12:46:11.619200 -- FileSlotService __construct -- 
2024-07-02 12:46:11.619300 -- AsyncOperationService __construct -- 
2024-07-02 12:46:11.619800 -- FileSlotService __construct -- 
2024-07-02 12:46:11.619900 -- Kess3Service __construct -- 
2024-07-02 12:46:11.619900 -- Kess3Service startEncoding -- 
2024-07-02 12:46:11.619900 -- AsyncOperationService startOperationEncode -- 
2024-07-02 12:46:11.640100 -- FileService uploadFiles -- 
2024-07-02 12:46:11.640500 -- AlientechLinkService processRequest -- 
2024-07-02 12:46:11.640500 -- isAuth -- 
2024-07-02 12:46:11.640500 -- AlientechLinkService isAuth_success -- 
2024-07-02 12:46:13.679700 -- AlientechLinkService processResponse -- 
2024-07-02 12:46:13.689400 --  --- $log->save() --  ---260 -- 
2024-07-02 12:46:13.689500 -- response_status=400 -- 
2024-07-02 12:46:13.689600 -- response=Http-Code: 400
Content-Type: text/plain; charset=utf-8
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Tue, 02 Jul 2024 12:46:12 GMT
Connection: close

KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-02 12:46:13.689700 -- content=KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-02 12:46:13.696600 -- AsyncOperationService startOperationEncode no_filled_data -- 
2024-07-02 12:46:13.696900 -- no_AsyncOperationDto -- 
2024-07-02 13:05:12.174800 -- AlientechLinkService __construct -- 
2024-07-02 13:05:12.175000 -- AlientechLinkService processClient -- 
2024-07-02 13:05:12.175100 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 13:05:12.175500 -- initAccessToken -- 
2024-07-02 13:05:12.178700 -- iR8V9-KAuaE -- 
2024-07-02 13:05:12.180200 -- AlientechProjectService __construct -- 
2024-07-02 13:05:12.181800 -- FileSlotService __construct -- 
2024-07-02 13:05:12.182100 -- FileSlotService __construct -- 
2024-07-02 13:05:12.182200 -- AsyncOperationService __construct -- 
2024-07-02 13:05:12.183200 -- FileSlotService __construct -- 
2024-07-02 13:05:12.183300 -- Kess3Service __construct -- 
2024-07-02 13:05:12.183300 -- Kess3Service startEncoding -- 
2024-07-02 13:05:12.183400 -- AsyncOperationService startOperationEncode -- 
2024-07-02 13:05:12.205900 -- FileService uploadFiles -- 
2024-07-02 13:05:12.206300 -- AlientechLinkService processRequest -- 
2024-07-02 13:05:12.206300 -- isAuth -- 
2024-07-02 13:05:12.206300 -- AlientechLinkService isAuth_success -- 
2024-07-02 13:05:13.203400 -- AlientechLinkService processResponse -- 
2024-07-02 13:05:13.216200 --  --- $log->save() --  ---261 -- 
2024-07-02 13:05:13.218800 --  --- $log->save() --  ---262 -- 
2024-07-02 13:05:13.229300 -- AlientechLinkService processRequest -- 
2024-07-02 13:05:13.229400 -- isAuth -- 
2024-07-02 13:05:13.229500 -- AlientechLinkService isAuth_success -- 
2024-07-02 13:05:15.742900 -- AlientechLinkService processResponse -- 
2024-07-02 13:05:15.745100 --  --- $log->save() --  ---263 -- 
2024-07-02 13:05:15.746600 --  --- $log->save() --  ---264 -- 
2024-07-02 13:05:15.746800 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 13:05:15.747300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 13:05:15.748500 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-02 13:05:17.832100 -- AlientechLinkService __construct -- 
2024-07-02 13:05:17.832300 -- AlientechLinkService processClient -- 
2024-07-02 13:05:17.832300 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 13:05:17.832400 -- initAccessToken -- 
2024-07-02 13:05:17.832900 -- iR8V9-KAuaE -- 
2024-07-02 13:05:17.833100 -- AlientechProjectService __construct -- 
2024-07-02 13:05:17.833200 -- FileSlotService __construct -- 
2024-07-02 13:05:17.833300 -- FileSlotService __construct -- 
2024-07-02 13:05:17.833300 -- AsyncOperationService __construct -- 
2024-07-02 13:05:17.833500 -- ApiController actionKess3Encoded -- 
2024-07-02 13:05:17.838500 --  --- $log->save() --  ---265 -- 
2024-07-02 13:05:17.838600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-02 13:05:17.838700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-02 13:05:17.842800 -- AsyncOperationService processCompletedOperation -- 
2024-07-02 13:05:17.842900 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-02 13:05:17.845900 -- AsyncOperationService finishCompletedOperation -- 
2024-07-02 13:05:17.846000 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-02 13:05:17.846100 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-02 13:05:17.848900 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-02 13:05:18.006600 -- AlientechLinkService __construct -- 
2024-07-02 13:05:18.006800 -- AlientechLinkService processClient -- 
2024-07-02 13:05:18.006800 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 13:05:18.007500 -- initAccessToken -- 
2024-07-02 13:05:18.010200 -- iR8V9-KAuaE -- 
2024-07-02 13:05:18.010800 -- AlientechProjectService __construct -- 
2024-07-02 13:05:18.011700 -- FileSlotService __construct -- 
2024-07-02 13:05:18.011900 -- FileSlotService __construct -- 
2024-07-02 13:05:18.012000 -- AsyncOperationService __construct -- 
2024-07-02 13:05:18.012200 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-02 13:05:18.012400 -- FileService downloadFile -- 
2024-07-02 13:05:18.012500 -- AlientechLinkService processRequest -- 
2024-07-02 13:05:18.012600 -- isAuth -- 
2024-07-02 13:05:18.012600 -- AlientechLinkService isAuth_success -- 
2024-07-02 13:05:18.424200 -- AlientechLinkService processResponse -- 
2024-07-02 13:05:18.445500 --  --- $log->save() --  ---266 -- 
2024-07-02 13:05:18.473900 --  --- $log->save() --  ---267 -- 
2024-07-02 13:05:18.477200 -- FileService saveEncodedFile -- 
2024-07-02 13:05:18.503300 -- FileService $initFile->file_history={"id":"7597","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-02 13:05:18.521400 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-02 13:10:47.400800 -- AlientechLinkService __construct -- 
2024-07-02 13:10:47.401000 -- AlientechLinkService processClient -- 
2024-07-02 13:10:47.401000 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 13:10:47.401500 -- initAccessToken -- 
2024-07-02 13:10:47.404600 -- iR8V9-KAuaE -- 
2024-07-02 13:10:47.405400 -- AlientechProjectService __construct -- 
2024-07-02 13:10:47.406700 -- FileSlotService __construct -- 
2024-07-02 13:10:47.406900 -- FileSlotService __construct -- 
2024-07-02 13:10:47.407000 -- AsyncOperationService __construct -- 
2024-07-02 13:10:47.407700 -- FileSlotService __construct -- 
2024-07-02 13:10:47.407800 -- Kess3Service __construct -- 
2024-07-02 13:10:47.407900 -- Kess3Service startEncoding -- 
2024-07-02 13:10:47.407900 -- AsyncOperationService startOperationEncode -- 
2024-07-02 13:10:47.432500 -- FileService uploadFiles -- 
2024-07-02 13:10:47.432900 -- AlientechLinkService processRequest -- 
2024-07-02 13:10:47.433000 -- isAuth -- 
2024-07-02 13:10:47.433000 -- AlientechLinkService isAuth_success -- 
2024-07-02 13:10:49.294000 -- AlientechLinkService processResponse -- 
2024-07-02 13:10:49.309000 --  --- $log->save() --  ---268 -- 
2024-07-02 13:10:49.309200 -- response_status=400 -- 
2024-07-02 13:10:49.309400 -- response=Http-Code: 400
Content-Type: text/plain; charset=utf-8
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Tue, 02 Jul 2024 13:10:49 GMT
Connection: close

KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-02 13:10:49.309400 -- content=KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-02 13:10:49.317600 -- AsyncOperationService startOperationEncode no_filled_data -- 
2024-07-02 13:10:49.317700 -- no_AsyncOperationDto -- 
2024-07-02 13:17:53.919300 -- AlientechLinkService __construct -- 
2024-07-02 13:17:53.920000 -- AlientechLinkService processClient -- 
2024-07-02 13:17:53.920100 -- AlientechLinkService is_null($this->client) -- 
2024-07-02 13:17:53.920900 -- initAccessToken -- 
2024-07-02 13:17:53.925200 -- iR8V9-KAuaE -- 
2024-07-02 13:17:53.926500 -- AlientechProjectService __construct -- 
2024-07-02 13:17:53.928500 -- FileSlotService __construct -- 
2024-07-02 13:17:53.928900 -- FileSlotService __construct -- 
2024-07-02 13:17:53.929000 -- AsyncOperationService __construct -- 
2024-07-02 13:17:53.930200 -- FileSlotService __construct -- 
2024-07-02 13:17:53.930300 -- Kess3Service __construct -- 
2024-07-02 13:17:53.930400 -- Kess3Service startEncoding -- 
2024-07-02 13:17:53.930400 -- AsyncOperationService startOperationEncode -- 
2024-07-02 13:17:53.959200 -- FileService uploadFiles -- 
2024-07-02 13:17:53.959700 -- AlientechLinkService processRequest -- 
2024-07-02 13:17:53.959900 -- isAuth -- 
2024-07-02 13:17:53.959900 -- AlientechLinkService isAuth_success -- 
2024-07-02 13:17:56.043700 -- AlientechLinkService processResponse -- 
2024-07-02 13:17:56.055500 --  --- $log->save() --  ---269 -- 
2024-07-02 13:17:56.055700 -- response_status=400 -- 
2024-07-02 13:17:56.055800 -- response=Http-Code: 400
Content-Type: text/plain; charset=utf-8
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Tue, 02 Jul 2024 13:17:55 GMT
Connection: close

KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-02 13:17:56.056000 -- content=KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-02 13:17:56.062300 -- AsyncOperationService startOperationEncode no_filled_data -- 
2024-07-02 13:17:56.062500 -- no_AsyncOperationDto -- 
2024-07-03 08:59:32.259200 -- AlientechLinkService __construct -- 
2024-07-03 08:59:32.259400 -- AlientechLinkService processClient -- 
2024-07-03 08:59:32.259500 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 08:59:32.259900 -- initAccessToken -- 
2024-07-03 08:59:32.263600 -- iR8V9-KAuaE -- 
2024-07-03 08:59:32.264300 -- AlientechProjectService __construct -- 
2024-07-03 08:59:32.265300 -- FileSlotService __construct -- 
2024-07-03 08:59:32.265500 -- FileSlotService __construct -- 
2024-07-03 08:59:32.265600 -- AsyncOperationService __construct -- 
2024-07-03 08:59:32.266300 -- FileSlotService __construct -- 
2024-07-03 08:59:32.266400 -- Kess3Service __construct -- 
2024-07-03 08:59:32.266400 -- Kess3Service startEncoding -- 
2024-07-03 08:59:32.266500 -- AsyncOperationService startOperationEncode -- 
2024-07-03 08:59:32.284300 -- FileService uploadFiles -- 
2024-07-03 08:59:32.284700 -- AlientechLinkService processRequest -- 
2024-07-03 08:59:32.284700 -- isAuth -- 
2024-07-03 08:59:32.284800 -- AlientechLinkService isAuth_success -- 
2024-07-03 08:59:32.899700 -- AlientechLinkService processResponse -- 
2024-07-03 08:59:32.916400 --  --- $log->save() --  ---270 -- 
2024-07-03 08:59:32.918500 --  --- $log->save() --  ---271 -- 
2024-07-03 08:59:32.933000 -- AlientechLinkService processRequest -- 
2024-07-03 08:59:32.933200 -- isAuth -- 
2024-07-03 08:59:32.933200 -- AlientechLinkService isAuth_success -- 
2024-07-03 08:59:34.066900 -- AlientechLinkService processResponse -- 
2024-07-03 08:59:34.069500 --  --- $log->save() --  ---272 -- 
2024-07-03 08:59:34.071400 --  --- $log->save() --  ---273 -- 
2024-07-03 08:59:34.071600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-03 08:59:34.071700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-03 08:59:34.073100 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-03 08:59:35.837600 -- AlientechLinkService __construct -- 
2024-07-03 08:59:35.838100 -- AlientechLinkService processClient -- 
2024-07-03 08:59:35.838200 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 08:59:35.838500 -- initAccessToken -- 
2024-07-03 08:59:35.839300 -- iR8V9-KAuaE -- 
2024-07-03 08:59:35.839600 -- AlientechProjectService __construct -- 
2024-07-03 08:59:35.840000 -- FileSlotService __construct -- 
2024-07-03 08:59:35.840100 -- FileSlotService __construct -- 
2024-07-03 08:59:35.840200 -- AsyncOperationService __construct -- 
2024-07-03 08:59:35.840600 -- ApiController actionKess3Encoded -- 
2024-07-03 08:59:35.846300 --  --- $log->save() --  ---274 -- 
2024-07-03 08:59:35.846500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-03 08:59:35.846600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-03 08:59:35.850200 -- AsyncOperationService processCompletedOperation -- 
2024-07-03 08:59:35.850400 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-03 08:59:35.852500 -- AsyncOperationService finishCompletedOperation -- 
2024-07-03 08:59:35.852600 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-03 08:59:35.852600 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-03 08:59:35.854600 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-03 08:59:39.055300 -- AlientechLinkService __construct -- 
2024-07-03 08:59:39.055500 -- AlientechLinkService processClient -- 
2024-07-03 08:59:39.055500 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 08:59:39.056300 -- initAccessToken -- 
2024-07-03 08:59:39.059200 -- iR8V9-KAuaE -- 
2024-07-03 08:59:39.060800 -- AlientechProjectService __construct -- 
2024-07-03 08:59:39.062900 -- FileSlotService __construct -- 
2024-07-03 08:59:39.063400 -- FileSlotService __construct -- 
2024-07-03 08:59:39.063500 -- AsyncOperationService __construct -- 
2024-07-03 08:59:39.064000 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-03 08:59:39.064300 -- FileService downloadFile -- 
2024-07-03 08:59:39.064500 -- AlientechLinkService processRequest -- 
2024-07-03 08:59:39.064500 -- isAuth -- 
2024-07-03 08:59:39.064600 -- AlientechLinkService isAuth_success -- 
2024-07-03 08:59:39.375500 -- AlientechLinkService processResponse -- 
2024-07-03 08:59:39.393800 --  --- $log->save() --  ---275 -- 
2024-07-03 08:59:39.420800 --  --- $log->save() --  ---276 -- 
2024-07-03 08:59:39.423600 -- FileService saveEncodedFile -- 
2024-07-03 08:59:39.442800 -- FileService $initFile->file_history={"id":"7597","value":"1","file_ver":"2","comment":null,"option":"can_download"} -- 
2024-07-03 08:59:39.455100 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-03 09:00:49.063000 -- AlientechLinkService __construct -- 
2024-07-03 09:00:49.063200 -- AlientechLinkService processClient -- 
2024-07-03 09:00:49.063300 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 09:00:49.063600 -- initAccessToken -- 
2024-07-03 09:00:49.066200 -- iR8V9-KAuaE -- 
2024-07-03 09:00:49.067000 -- AlientechProjectService __construct -- 
2024-07-03 09:00:49.068800 -- FileSlotService __construct -- 
2024-07-03 09:00:49.069100 -- FileSlotService __construct -- 
2024-07-03 09:00:49.069200 -- AsyncOperationService __construct -- 
2024-07-03 09:00:49.070400 -- FileSlotService __construct -- 
2024-07-03 09:00:49.070500 -- Kess3Service __construct -- 
2024-07-03 09:00:49.070600 -- Kess3Service startEncoding -- 
2024-07-03 09:00:49.070700 -- AsyncOperationService startOperationEncode -- 
2024-07-03 09:00:49.098300 -- FileService uploadFiles -- 
2024-07-03 09:00:49.099100 -- AlientechLinkService processRequest -- 
2024-07-03 09:00:49.099200 -- isAuth -- 
2024-07-03 09:00:49.099300 -- AlientechLinkService isAuth_success -- 
2024-07-03 09:00:49.921500 -- AlientechLinkService processResponse -- 
2024-07-03 09:00:49.933500 --  --- $log->save() --  ---277 -- 
2024-07-03 09:00:49.933700 -- response_status=400 -- 
2024-07-03 09:00:49.933800 -- response=Http-Code: 400
Content-Type: text/plain; charset=utf-8
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Wed, 03 Jul 2024 09:00:49 GMT
Connection: close

KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-03 09:00:49.933900 -- content=KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-03 09:00:49.940900 -- AsyncOperationService startOperationEncode no_filled_data -- 
2024-07-03 09:00:49.941200 -- no_AsyncOperationDto -- 
2024-07-03 10:45:40.135500 -- AlientechLinkService __construct -- 
2024-07-03 10:45:40.136100 -- AlientechLinkService processClient -- 
2024-07-03 10:45:40.136500 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 10:45:40.137400 -- initAccessToken -- 
2024-07-03 10:45:40.141100 -- iR8V9-KAuaE -- 
2024-07-03 10:45:40.142400 -- AlientechProjectService __construct -- 
2024-07-03 10:45:40.144400 -- FileSlotService __construct -- 
2024-07-03 10:45:40.144900 -- FileSlotService __construct -- 
2024-07-03 10:45:40.145300 -- AsyncOperationService __construct -- 
2024-07-03 10:45:40.146400 -- FileSlotService __construct -- 
2024-07-03 10:45:40.146700 -- Kess3Service __construct -- 
2024-07-03 10:45:40.147000 -- Kess3Service startDecoding -- 
2024-07-03 10:45:40.147300 -- AsyncOperationService startOperationDecode -- 
2024-07-03 10:45:40.147600 -- AlientechLinkService processRequest -- 
2024-07-03 10:45:40.147900 -- isAuth -- 
2024-07-03 10:45:40.148100 -- AlientechLinkService isAuth_success -- 
2024-07-03 10:45:41.739900 -- AlientechLinkService processResponse -- 
2024-07-03 10:45:41.759700 --  --- $log->save() --  ---278 -- 
2024-07-03 10:45:41.761500 --  --- $log->save() --  ---279 -- 
2024-07-03 10:45:41.761600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-03 10:45:41.761600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-03 10:45:41.768100 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-03 10:45:43.311900 -- AlientechLinkService __construct -- 
2024-07-03 10:45:43.312400 -- AlientechLinkService processClient -- 
2024-07-03 10:45:43.312400 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 10:45:43.312600 -- initAccessToken -- 
2024-07-03 10:45:43.313300 -- iR8V9-KAuaE -- 
2024-07-03 10:45:43.313500 -- AlientechProjectService __construct -- 
2024-07-03 10:45:43.313800 -- FileSlotService __construct -- 
2024-07-03 10:45:43.313900 -- FileSlotService __construct -- 
2024-07-03 10:45:43.314000 -- AsyncOperationService __construct -- 
2024-07-03 10:45:43.314300 -- ApiController actionKess3Decoded -- 
2024-07-03 10:45:43.320500 --  --- $log->save() --  ---280 -- 
2024-07-03 10:45:43.320700 -- AsyncOperationService createOperationDtoByData -- 
2024-07-03 10:45:43.320700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-03 10:45:43.326000 -- AsyncOperationService processCompletedOperation -- 
2024-07-03 10:45:43.326200 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-03 10:45:43.330000 -- AsyncOperationService finishCompletedOperation -- 
2024-07-03 10:45:43.330200 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-03 10:45:43.330300 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-03 10:45:43.333800 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-03 10:45:45.427900 -- AlientechLinkService __construct -- 
2024-07-03 10:45:45.428100 -- AlientechLinkService processClient -- 
2024-07-03 10:45:45.428400 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 10:45:45.429100 -- initAccessToken -- 
2024-07-03 10:45:45.432700 -- iR8V9-KAuaE -- 
2024-07-03 10:45:45.433700 -- AlientechProjectService __construct -- 
2024-07-03 10:45:45.435100 -- FileSlotService __construct -- 
2024-07-03 10:45:45.435400 -- FileSlotService __construct -- 
2024-07-03 10:45:45.435400 -- AsyncOperationService __construct -- 
2024-07-03 10:45:45.436100 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-03 10:45:45.436900 -- FileService downloadFile -- 
2024-07-03 10:45:45.437000 -- AlientechLinkService processRequest -- 
2024-07-03 10:45:45.437100 -- isAuth -- 
2024-07-03 10:45:45.437100 -- AlientechLinkService isAuth_success -- 
2024-07-03 10:45:45.661900 -- AlientechLinkService processResponse -- 
2024-07-03 10:45:45.680100 --  --- $log->save() --  ---281 -- 
2024-07-03 10:45:45.681900 --  --- $log->save() --  ---282 -- 
2024-07-03 10:45:45.682300 -- FileService saveDecodedFile -- 
2024-07-03 10:45:45.696300 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-03 10:45:45.749000 -- FileService downloadFile -- 
2024-07-03 10:45:45.749300 -- AlientechLinkService processRequest -- 
2024-07-03 10:45:45.749400 -- isAuth -- 
2024-07-03 10:45:45.749500 -- AlientechLinkService isAuth_success -- 
2024-07-03 10:45:47.145000 -- AlientechLinkService processResponse -- 
2024-07-03 10:45:47.146800 --  --- $log->save() --  ---283 -- 
2024-07-03 10:45:47.309400 --  --- $log->save() --  ---284 -- 
2024-07-03 10:45:47.322300 -- FileService saveDecodedFile -- 
2024-07-03 10:45:47.339900 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-03 10:45:47.365800 -- FileService downloadFile -- 
2024-07-03 10:45:47.366100 -- AlientechLinkService processRequest -- 
2024-07-03 10:45:47.366200 -- isAuth -- 
2024-07-03 10:45:47.366300 -- AlientechLinkService isAuth_success -- 
2024-07-03 10:45:48.940800 -- AlientechLinkService processResponse -- 
2024-07-03 10:45:48.943100 --  --- $log->save() --  ---285 -- 
2024-07-03 10:45:49.000500 --  --- $log->save() --  ---286 -- 
2024-07-03 10:45:49.007400 -- FileService saveDecodedFile -- 
2024-07-03 10:45:49.016700 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-03 14:56:30.276100 -- AlientechLinkService __construct -- 
2024-07-03 14:56:30.276400 -- AlientechLinkService processClient -- 
2024-07-03 14:56:30.276500 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 14:56:30.276900 -- initAccessToken -- 
2024-07-03 14:56:30.280400 -- iR8V9-KAuaE -- 
2024-07-03 14:56:30.281100 -- AlientechProjectService __construct -- 
2024-07-03 14:56:30.282200 -- FileSlotService __construct -- 
2024-07-03 14:56:30.282400 -- FileSlotService __construct -- 
2024-07-03 14:56:30.282500 -- AsyncOperationService __construct -- 
2024-07-03 14:56:30.283100 -- FileSlotService __construct -- 
2024-07-03 14:56:30.283200 -- Kess3Service __construct -- 
2024-07-03 14:56:30.283200 -- Kess3Service startEncoding -- 
2024-07-03 14:56:30.283300 -- AsyncOperationService startOperationEncode -- 
2024-07-03 14:56:30.304400 -- FileService uploadFiles -- 
2024-07-03 14:56:30.304900 -- AlientechLinkService processRequest -- 
2024-07-03 14:56:30.305100 -- isAuth -- 
2024-07-03 14:56:30.305100 -- AlientechLinkService isAuth_success -- 
2024-07-03 14:56:30.869700 -- AlientechLinkService processResponse -- 
2024-07-03 14:56:30.879400 --  --- $log->save() --  ---287 -- 
2024-07-03 14:56:30.879700 -- response_status=400 -- 
2024-07-03 14:56:30.879900 -- response=Http-Code: 400
Content-Type: text/plain; charset=utf-8
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Wed, 03 Jul 2024 14:56:29 GMT
Connection: close

KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-03 14:56:30.880700 -- content=KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-03 14:56:30.887500 -- AsyncOperationService startOperationEncode no_filled_data -- 
2024-07-03 14:56:30.887600 -- no_AsyncOperationDto -- 
2024-07-03 15:07:15.407500 -- AlientechLinkService __construct -- 
2024-07-03 15:07:15.407800 -- AlientechLinkService processClient -- 
2024-07-03 15:07:15.407800 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 15:07:15.408200 -- initAccessToken -- 
2024-07-03 15:07:15.411800 -- iR8V9-KAuaE -- 
2024-07-03 15:07:15.412600 -- AlientechProjectService __construct -- 
2024-07-03 15:07:15.413700 -- FileSlotService __construct -- 
2024-07-03 15:07:15.414000 -- FileSlotService __construct -- 
2024-07-03 15:07:15.414100 -- AsyncOperationService __construct -- 
2024-07-03 15:07:15.414900 -- FileSlotService __construct -- 
2024-07-03 15:07:15.415000 -- Kess3Service __construct -- 
2024-07-03 15:07:15.415000 -- Kess3Service startEncoding -- 
2024-07-03 15:07:15.415000 -- AsyncOperationService startOperationEncode -- 
2024-07-03 15:07:15.438700 -- FileService uploadFiles -- 
2024-07-03 15:07:15.439100 -- AlientechLinkService processRequest -- 
2024-07-03 15:07:15.439200 -- isAuth -- 
2024-07-03 15:07:15.439300 -- AlientechLinkService isAuth_success -- 
2024-07-03 15:07:17.525700 -- AlientechLinkService processResponse -- 
2024-07-03 15:07:17.536000 --  --- $log->save() --  ---288 -- 
2024-07-03 15:07:17.536200 -- response_status=400 -- 
2024-07-03 15:07:17.536400 -- response=Http-Code: 400
Content-Type: text/plain; charset=utf-8
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Wed, 03 Jul 2024 15:07:17 GMT
Connection: close

KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-03 15:07:17.536500 -- content=KESS3_FILE_SLOT_IS_CLOSED -- 
2024-07-03 15:07:17.540700 -- AsyncOperationService startOperationEncode no_filled_data -- 
2024-07-03 15:07:17.540800 -- no_AsyncOperationDto -- 
2024-07-03 16:31:56.506500 -- AlientechLinkService __construct -- 
2024-07-03 16:31:56.508700 -- AlientechLinkService processClient -- 
2024-07-03 16:31:56.508800 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 16:31:56.510300 -- AlientechLinkService initAccessToken -- 
2024-07-03 16:31:56.516600 -- iR8V9-KAuaE -- 
2024-07-03 16:31:56.519500 -- AlientechProjectService __construct -- 
2024-07-03 16:31:56.523700 -- FileSlotService __construct -- 
2024-07-03 16:31:56.524300 -- FileSlotService __construct -- 
2024-07-03 16:31:56.524500 -- AsyncOperationService __construct -- 
2024-07-03 16:31:56.527200 -- FileSlotService __construct -- 
2024-07-03 16:31:56.527300 -- Kess3Service __construct -- 
2024-07-03 16:31:56.527400 -- Kess3Service startEncoding -- 
2024-07-03 16:31:56.527500 -- AsyncOperationService startOperationEncode -- 
2024-07-03 16:31:56.556000 -- FileService uploadFiles -- 
2024-07-03 16:31:56.556800 -- FileSlotService reOpenSlot -- 
2024-07-03 16:31:56.556800 -- FileSlotService hasOpenFileSlots -- 
2024-07-03 16:31:56.556900 -- FileSlotService getSlots -- 
2024-07-03 16:31:56.556900 -- AlientechLinkService processRequest -- 
2024-07-03 16:31:56.556900 -- isAuth -- 
2024-07-03 16:31:56.556900 -- AlientechLinkService isAuth_success -- 
2024-07-03 16:31:57.377900 -- AlientechLinkService processResponse -- 
2024-07-03 16:31:57.392700 --  --- $log->save() --  ---289 -- 
2024-07-03 16:31:57.398500 --  --- $log->save() --  ---290 -- 
2024-07-03 16:31:57.398900 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-03 16:31:57.399000 -- /api/kess3/file-slots/70f9eb1e-ad47-4b9a-a6aa-6b761064a272/reopen -- 
2024-07-03 16:31:57.399000 -- AlientechLinkService processRequest -- 
2024-07-03 16:31:57.399100 -- isAuth -- 
2024-07-03 16:31:57.399100 -- AlientechLinkService isAuth_success -- 
2024-07-03 16:31:57.706800 -- AlientechLinkService processResponse -- 
2024-07-03 16:31:57.709600 --  --- $log->save() --  ---291 -- 
2024-07-03 16:31:57.711800 --  --- $log->save() --  ---292 -- 
2024-07-03 16:31:57.712000 -- AlientechLinkService processRequest -- 
2024-07-03 16:31:57.712100 -- isAuth -- 
2024-07-03 16:31:57.712100 -- AlientechLinkService isAuth_success -- 
2024-07-03 16:31:58.653100 -- AlientechLinkService processResponse -- 
2024-07-03 16:31:58.655100 --  --- $log->save() --  ---293 -- 
2024-07-03 16:31:58.656300 --  --- $log->save() --  ---294 -- 
2024-07-03 16:31:58.666800 -- AlientechLinkService processRequest -- 
2024-07-03 16:31:58.666900 -- isAuth -- 
2024-07-03 16:31:58.667000 -- AlientechLinkService isAuth_success -- 
2024-07-03 16:31:59.172400 -- AlientechLinkService processResponse -- 
2024-07-03 16:31:59.175300 --  --- $log->save() --  ---295 -- 
2024-07-03 16:31:59.177800 --  --- $log->save() --  ---296 -- 
2024-07-03 16:31:59.178000 -- AsyncOperationService createOperationDtoByData -- 
2024-07-03 16:31:59.178100 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-03 16:31:59.179500 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-03 16:32:03.526700 -- AlientechLinkService __construct -- 
2024-07-03 16:32:03.527800 -- AlientechLinkService processClient -- 
2024-07-03 16:32:03.527900 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 16:32:03.528900 -- AlientechLinkService initAccessToken -- 
2024-07-03 16:32:03.530600 -- iR8V9-KAuaE -- 
2024-07-03 16:32:03.533100 -- AlientechProjectService __construct -- 
2024-07-03 16:32:03.537400 -- FileSlotService __construct -- 
2024-07-03 16:32:03.537800 -- FileSlotService __construct -- 
2024-07-03 16:32:03.537900 -- AsyncOperationService __construct -- 
2024-07-03 16:32:03.540700 -- ApiController actionKess3Encoded -- 
2024-07-03 16:32:03.549300 --  --- $log->save() --  ---297 -- 
2024-07-03 16:32:03.549600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-03 16:32:03.549700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-03 16:32:03.553700 -- AsyncOperationService processCompletedOperation -- 
2024-07-03 16:32:03.553800 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-03 16:32:03.555800 -- AsyncOperationService finishCompletedOperation -- 
2024-07-03 16:32:03.555900 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-03 16:32:03.555900 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-03 16:32:03.558400 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-03 16:32:04.628900 -- AlientechLinkService __construct -- 
2024-07-03 16:32:04.629200 -- AlientechLinkService processClient -- 
2024-07-03 16:32:04.629200 -- AlientechLinkService is_null($this->client) -- 
2024-07-03 16:32:04.629700 -- AlientechLinkService initAccessToken -- 
2024-07-03 16:32:04.632700 -- iR8V9-KAuaE -- 
2024-07-03 16:32:04.634000 -- AlientechProjectService __construct -- 
2024-07-03 16:32:04.635600 -- FileSlotService __construct -- 
2024-07-03 16:32:04.636100 -- FileSlotService __construct -- 
2024-07-03 16:32:04.636200 -- AsyncOperationService __construct -- 
2024-07-03 16:32:04.636700 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-03 16:32:04.637700 -- FileService downloadFile -- 
2024-07-03 16:32:04.637700 -- AlientechLinkService processRequest -- 
2024-07-03 16:32:04.637800 -- isAuth -- 
2024-07-03 16:32:04.638100 -- AlientechLinkService isAuth_success -- 
2024-07-03 16:32:05.632900 -- AlientechLinkService processResponse -- 
2024-07-03 16:32:05.658500 --  --- $log->save() --  ---298 -- 
2024-07-03 16:32:06.015200 --  --- $log->save() --  ---299 -- 
2024-07-03 16:32:06.063200 -- FileService saveEncodedFile -- 
2024-07-03 16:32:06.101400 -- FileService $initFile->file_history={"id":"7591","value":"1","file_ver":"2","comment":null,"option":"can_download"} -- 
2024-07-03 16:32:06.116100 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-04 07:04:29.867600 -- AlientechLinkService __construct -- 
2024-07-04 07:04:29.868200 -- AlientechLinkService processClient -- 
2024-07-04 07:04:29.868400 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:04:29.869000 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:04:29.872000 -- iR8V9-KAuaE -- 
2024-07-04 07:04:29.872800 -- AlientechProjectService __construct -- 
2024-07-04 07:04:29.873900 -- FileSlotService __construct -- 
2024-07-04 07:04:29.874300 -- FileSlotService __construct -- 
2024-07-04 07:04:29.874500 -- AsyncOperationService __construct -- 
2024-07-04 07:04:29.875100 -- FileSlotService __construct -- 
2024-07-04 07:04:29.875400 -- Kess3Service __construct -- 
2024-07-04 07:04:29.875600 -- Kess3Service startDecoding -- 
2024-07-04 07:04:29.875800 -- AsyncOperationService startOperationDecode -- 
2024-07-04 07:04:29.876000 -- AlientechLinkService processRequest -- 
2024-07-04 07:04:29.876200 -- isAuth -- 
2024-07-04 07:04:29.876400 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:04:30.428300 -- AlientechLinkService processResponse -- 
2024-07-04 07:04:30.449100 --  --- $log->save() --  ---300 -- 
2024-07-04 07:04:30.449300 -- response_status=429 -- 
2024-07-04 07:04:30.449500 -- response=Http-Code: 429
Content-Type: text/plain; charset=utf-8
Retry-After: 0
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Thu, 04 Jul 2024 07:04:30 GMT
Connection: close

TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-07-04 07:04:30.449600 -- content=TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-07-04 07:04:30.449700 -- FileSlotService closeAllFileSlots -- 
2024-07-04 07:04:30.449800 -- FileSlotService getSlots -- 
2024-07-04 07:04:30.449900 -- AlientechLinkService processRequest -- 
2024-07-04 07:04:30.449900 -- isAuth -- 
2024-07-04 07:04:30.450000 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:04:30.992900 -- AlientechLinkService processResponse -- 
2024-07-04 07:04:30.995800 --  --- $log->save() --  ---301 -- 
2024-07-04 07:04:31.001600 --  --- $log->save() --  ---302 -- 
2024-07-04 07:04:31.002100 -- slot [093070c6-2c94-4535-8bd2-6dd299268eb0] dateDiff {"y":0,"m":0,"d":0,"h":20,"i":18,"s":49,"f":0.30875,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 07:04:31.002600 -- FileSlotService closeSlot -- 
2024-07-04 07:04:31.002900 -- /api/kess3/file-slots/093070c6-2c94-4535-8bd2-6dd299268eb0/close  -- 
2024-07-04 07:04:31.003100 -- AlientechLinkService processRequest -- 
2024-07-04 07:04:31.003400 -- isAuth -- 
2024-07-04 07:04:31.003600 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:04:31.219700 -- AlientechLinkService processResponse -- 
2024-07-04 07:04:31.222000 --  --- $log->save() --  ---303 -- 
2024-07-04 07:04:31.223400 --  --- $log->save() --  ---304 -- 
2024-07-04 07:04:31.223500 -- slot [093070c6-2c94-4535-8bd2-6dd299268eb0] closeSlot null -- 
2024-07-04 07:04:31.224000 -- slot [2692723c-171d-4d7d-9053-96a5f96118f5] dateDiff {"y":0,"m":0,"d":1,"h":19,"i":15,"s":4,"f":0.863956,"invert":1,"days":1,"from_string":false} -- 
2024-07-04 07:04:31.224300 -- FileSlotService closeSlot -- 
2024-07-04 07:04:31.224600 -- /api/kess3/file-slots/2692723c-171d-4d7d-9053-96a5f96118f5/close  -- 
2024-07-04 07:04:31.224900 -- AlientechLinkService processRequest -- 
2024-07-04 07:04:31.225200 -- isAuth -- 
2024-07-04 07:04:31.225500 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:04:31.809800 -- AlientechLinkService processResponse -- 
2024-07-04 07:04:31.812500 --  --- $log->save() --  ---305 -- 
2024-07-04 07:04:31.814600 --  --- $log->save() --  ---306 -- 
2024-07-04 07:04:31.814800 -- slot [2692723c-171d-4d7d-9053-96a5f96118f5] closeSlot null -- 
2024-07-04 07:04:31.814900 -- slot [70f9eb1e-ad47-4b9a-a6aa-6b761064a272] dateDiff {"y":0,"m":0,"d":2,"h":18,"i":48,"s":7,"f":0.314905,"invert":1,"days":2,"from_string":false} -- 
2024-07-04 07:04:31.815000 -- FileSlotService closeSlot -- 
2024-07-04 07:04:31.815100 -- /api/kess3/file-slots/70f9eb1e-ad47-4b9a-a6aa-6b761064a272/close  -- 
2024-07-04 07:04:31.815200 -- AlientechLinkService processRequest -- 
2024-07-04 07:04:31.815300 -- isAuth -- 
2024-07-04 07:04:31.815400 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:04:32.000800 -- AlientechLinkService processResponse -- 
2024-07-04 07:04:32.002400 --  --- $log->save() --  ---307 -- 
2024-07-04 07:04:32.003300 --  --- $log->save() --  ---308 -- 
2024-07-04 07:04:32.003300 -- slot [70f9eb1e-ad47-4b9a-a6aa-6b761064a272] closeSlot null -- 
2024-07-04 07:04:32.003400 -- closeAllSlots finish -- 
2024-07-04 07:04:32.003400 -- AsyncOperationService startOperationDecode -- 
2024-07-04 07:04:32.003400 -- AlientechLinkService processRequest -- 
2024-07-04 07:04:32.003500 -- isAuth -- 
2024-07-04 07:04:32.003500 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:04:32.484900 -- AlientechLinkService processResponse -- 
2024-07-04 07:04:32.487900 --  --- $log->save() --  ---309 -- 
2024-07-04 07:04:32.490100 --  --- $log->save() --  ---310 -- 
2024-07-04 07:04:32.490400 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 07:04:32.490600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 07:04:32.503900 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 07:04:34.249500 -- AlientechLinkService __construct -- 
2024-07-04 07:04:34.249900 -- AlientechLinkService processClient -- 
2024-07-04 07:04:34.249900 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:04:34.250100 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:04:34.250800 -- iR8V9-KAuaE -- 
2024-07-04 07:04:34.252800 -- AlientechProjectService __construct -- 
2024-07-04 07:04:34.256100 -- FileSlotService __construct -- 
2024-07-04 07:04:34.256300 -- FileSlotService __construct -- 
2024-07-04 07:04:34.256300 -- AsyncOperationService __construct -- 
2024-07-04 07:04:34.258000 -- ApiController actionKess3Decoded -- 
2024-07-04 07:04:34.264400 --  --- $log->save() --  ---311 -- 
2024-07-04 07:04:34.264500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 07:04:34.264500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 07:04:34.268500 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 07:04:34.268600 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 07:04:34.272900 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 07:04:34.273000 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-04 07:04:34.273100 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-04 07:04:34.276500 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 07:04:34.577200 -- AlientechLinkService __construct -- 
2024-07-04 07:04:34.577300 -- AlientechLinkService processClient -- 
2024-07-04 07:04:34.577400 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:04:34.577700 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:04:34.580400 -- iR8V9-KAuaE -- 
2024-07-04 07:04:34.581900 -- AlientechProjectService __construct -- 
2024-07-04 07:04:34.584700 -- FileSlotService __construct -- 
2024-07-04 07:04:34.585200 -- FileSlotService __construct -- 
2024-07-04 07:04:34.585400 -- AsyncOperationService __construct -- 
2024-07-04 07:04:34.586000 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-04 07:04:34.587700 -- FileService downloadFile -- 
2024-07-04 07:04:34.587900 -- AlientechLinkService processRequest -- 
2024-07-04 07:04:34.588000 -- isAuth -- 
2024-07-04 07:04:34.588100 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:04:35.570000 -- AlientechLinkService processResponse -- 
2024-07-04 07:04:35.593100 --  --- $log->save() --  ---312 -- 
2024-07-04 07:04:35.752100 --  --- $log->save() --  ---313 -- 
2024-07-04 07:04:35.770400 -- FileService saveDecodedFile -- 
2024-07-04 07:04:35.802400 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 07:44:34.827600 -- AlientechLinkService __construct -- 
2024-07-04 07:44:34.827900 -- AlientechLinkService processClient -- 
2024-07-04 07:44:34.828000 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:44:34.828300 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:44:34.830900 -- iR8V9-KAuaE -- 
2024-07-04 07:44:34.832400 -- AlientechProjectService __construct -- 
2024-07-04 07:44:34.835000 -- FileSlotService __construct -- 
2024-07-04 07:44:34.835400 -- FileSlotService __construct -- 
2024-07-04 07:44:34.835600 -- AsyncOperationService __construct -- 
2024-07-04 07:44:34.836900 -- FileSlotService __construct -- 
2024-07-04 07:44:34.837100 -- Kess3Service __construct -- 
2024-07-04 07:44:34.837100 -- Kess3Service startDecoding -- 
2024-07-04 07:44:34.837200 -- AsyncOperationService startOperationDecode -- 
2024-07-04 07:44:34.837300 -- AlientechLinkService processRequest -- 
2024-07-04 07:44:34.837300 -- isAuth -- 
2024-07-04 07:44:34.837400 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:44:36.564600 -- AlientechLinkService processResponse -- 
2024-07-04 07:44:36.578700 --  --- $log->save() --  ---314 -- 
2024-07-04 07:44:36.580400 --  --- $log->save() --  ---315 -- 
2024-07-04 07:44:36.580500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 07:44:36.580600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 07:44:36.587100 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 07:44:38.306000 -- AlientechLinkService __construct -- 
2024-07-04 07:44:38.306200 -- AlientechLinkService processClient -- 
2024-07-04 07:44:38.306200 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:44:38.306300 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:44:38.306700 -- iR8V9-KAuaE -- 
2024-07-04 07:44:38.306800 -- AlientechProjectService __construct -- 
2024-07-04 07:44:38.306900 -- FileSlotService __construct -- 
2024-07-04 07:44:38.307000 -- FileSlotService __construct -- 
2024-07-04 07:44:38.307000 -- AsyncOperationService __construct -- 
2024-07-04 07:44:38.307100 -- ApiController actionKess3Decoded -- 
2024-07-04 07:44:38.309300 --  --- $log->save() --  ---316 -- 
2024-07-04 07:44:38.309600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 07:44:38.309700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 07:44:38.311300 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 07:44:38.311300 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 07:44:38.313800 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 07:44:38.314000 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-04 07:44:38.314100 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-04 07:44:38.317200 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 07:44:39.845100 -- AlientechLinkService __construct -- 
2024-07-04 07:44:39.845400 -- AlientechLinkService processClient -- 
2024-07-04 07:44:39.845400 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:44:39.845800 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:44:39.848400 -- iR8V9-KAuaE -- 
2024-07-04 07:44:39.849800 -- AlientechProjectService __construct -- 
2024-07-04 07:44:39.852000 -- FileSlotService __construct -- 
2024-07-04 07:44:39.852300 -- FileSlotService __construct -- 
2024-07-04 07:44:39.852500 -- AsyncOperationService __construct -- 
2024-07-04 07:44:39.852900 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-04 07:44:39.854300 -- FileService downloadFile -- 
2024-07-04 07:44:39.854500 -- AlientechLinkService processRequest -- 
2024-07-04 07:44:39.854600 -- isAuth -- 
2024-07-04 07:44:39.854700 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:44:41.182600 -- AlientechLinkService processResponse -- 
2024-07-04 07:44:41.208500 --  --- $log->save() --  ---317 -- 
2024-07-04 07:44:41.424000 --  --- $log->save() --  ---318 -- 
2024-07-04 07:44:41.451300 -- FileService saveDecodedFile -- 
2024-07-04 07:44:41.491600 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 07:57:12.149500 -- AlientechLinkService __construct -- 
2024-07-04 07:57:12.149700 -- AlientechLinkService processClient -- 
2024-07-04 07:57:12.149700 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:57:12.150200 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:57:12.153600 -- iR8V9-KAuaE -- 
2024-07-04 07:57:12.154100 -- AlientechProjectService __construct -- 
2024-07-04 07:57:12.154900 -- FileSlotService __construct -- 
2024-07-04 07:57:12.155000 -- FileSlotService __construct -- 
2024-07-04 07:57:12.155100 -- AsyncOperationService __construct -- 
2024-07-04 07:57:12.155500 -- FileSlotService __construct -- 
2024-07-04 07:57:12.155600 -- Kess3Service __construct -- 
2024-07-04 07:57:12.155600 -- Kess3Service startDecoding -- 
2024-07-04 07:57:12.155700 -- AsyncOperationService startOperationDecode -- 
2024-07-04 07:57:12.155700 -- AlientechLinkService processRequest -- 
2024-07-04 07:57:12.155700 -- isAuth -- 
2024-07-04 07:57:12.155700 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:57:12.596900 -- AlientechLinkService processResponse -- 
2024-07-04 07:57:12.618400 --  --- $log->save() --  ---319 -- 
2024-07-04 07:57:12.620400 --  --- $log->save() --  ---320 -- 
2024-07-04 07:57:12.620600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 07:57:12.620800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 07:57:12.628900 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 07:57:14.489900 -- AlientechLinkService __construct -- 
2024-07-04 07:57:14.490200 -- AlientechLinkService processClient -- 
2024-07-04 07:57:14.490200 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:57:14.490200 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:57:14.491000 -- iR8V9-KAuaE -- 
2024-07-04 07:57:14.491400 -- AlientechProjectService __construct -- 
2024-07-04 07:57:14.491800 -- FileSlotService __construct -- 
2024-07-04 07:57:14.491900 -- FileSlotService __construct -- 
2024-07-04 07:57:14.492000 -- AsyncOperationService __construct -- 
2024-07-04 07:57:14.492300 -- ApiController actionKess3Decoded -- 
2024-07-04 07:57:14.496700 --  --- $log->save() --  ---321 -- 
2024-07-04 07:57:14.496900 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 07:57:14.496900 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 07:57:14.498400 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 07:57:14.498400 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 07:57:14.501100 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 07:57:14.501100 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-04 07:57:14.501200 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-04 07:57:14.504400 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 07:57:17.191400 -- AlientechLinkService __construct -- 
2024-07-04 07:57:17.191600 -- AlientechLinkService processClient -- 
2024-07-04 07:57:17.191700 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:57:17.192200 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:57:17.196100 -- iR8V9-KAuaE -- 
2024-07-04 07:57:17.198200 -- AlientechProjectService __construct -- 
2024-07-04 07:57:17.202200 -- FileSlotService __construct -- 
2024-07-04 07:57:17.203400 -- FileSlotService __construct -- 
2024-07-04 07:57:17.203500 -- AsyncOperationService __construct -- 
2024-07-04 07:57:17.204500 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-04 07:57:17.205600 -- FileService downloadFile -- 
2024-07-04 07:57:17.205800 -- AlientechLinkService processRequest -- 
2024-07-04 07:57:17.206100 -- isAuth -- 
2024-07-04 07:57:17.206400 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:57:17.633100 -- AlientechLinkService processResponse -- 
2024-07-04 07:57:17.655900 --  --- $log->save() --  ---322 -- 
2024-07-04 07:57:17.682800 --  --- $log->save() --  ---323 -- 
2024-07-04 07:57:17.684600 -- FileService saveDecodedFile -- 
2024-07-04 07:57:17.708100 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 07:57:17.774000 -- FileService downloadFile -- 
2024-07-04 07:57:17.774100 -- AlientechLinkService processRequest -- 
2024-07-04 07:57:17.774100 -- isAuth -- 
2024-07-04 07:57:17.774200 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:57:19.293300 -- AlientechLinkService processResponse -- 
2024-07-04 07:57:19.296300 --  --- $log->save() --  ---324 -- 
2024-07-04 07:57:19.643400 --  --- $log->save() --  ---325 -- 
2024-07-04 07:57:19.669400 -- FileService saveDecodedFile -- 
2024-07-04 07:57:19.695200 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 07:59:42.211500 -- AlientechLinkService __construct -- 
2024-07-04 07:59:42.211800 -- AlientechLinkService processClient -- 
2024-07-04 07:59:42.211800 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:59:42.212200 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:59:42.215100 -- iR8V9-KAuaE -- 
2024-07-04 07:59:42.216600 -- AlientechProjectService __construct -- 
2024-07-04 07:59:42.218800 -- FileSlotService __construct -- 
2024-07-04 07:59:42.219200 -- FileSlotService __construct -- 
2024-07-04 07:59:42.219300 -- AsyncOperationService __construct -- 
2024-07-04 07:59:42.220600 -- FileSlotService __construct -- 
2024-07-04 07:59:42.220700 -- Kess3Service __construct -- 
2024-07-04 07:59:42.220800 -- Kess3Service startEncoding -- 
2024-07-04 07:59:42.220900 -- AsyncOperationService startOperationEncode -- 
2024-07-04 07:59:42.252900 -- FileService uploadFiles -- 
2024-07-04 07:59:42.253700 -- FileSlotService reOpenSlot -- 
2024-07-04 07:59:42.253800 -- FileSlotService hasOpenFileSlots -- 
2024-07-04 07:59:42.253900 -- FileSlotService getSlots -- 
2024-07-04 07:59:42.254000 -- AlientechLinkService processRequest -- 
2024-07-04 07:59:42.254100 -- isAuth -- 
2024-07-04 07:59:42.254200 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:59:43.653200 -- AlientechLinkService processResponse -- 
2024-07-04 07:59:43.664600 --  --- $log->save() --  ---326 -- 
2024-07-04 07:59:43.668300 --  --- $log->save() --  ---327 -- 
2024-07-04 07:59:43.668800 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-04 07:59:43.669000 -- FileSlotService closeAllFileSlots -- 
2024-07-04 07:59:43.669100 -- FileSlotService getSlots -- 
2024-07-04 07:59:43.669200 -- AlientechLinkService processRequest -- 
2024-07-04 07:59:43.669200 -- isAuth -- 
2024-07-04 07:59:43.669300 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:59:44.207300 -- AlientechLinkService processResponse -- 
2024-07-04 07:59:44.209900 --  --- $log->save() --  ---328 -- 
2024-07-04 07:59:44.214700 --  --- $log->save() --  ---329 -- 
2024-07-04 07:59:44.215600 -- slot [065ef481-86fc-4bdc-9b43-830d64909ddb] dateDiff {"y":0,"m":0,"d":0,"h":0,"i":2,"s":31,"f":0.692166,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 07:59:44.215800 -- FileSlotService closeSlot -- 
2024-07-04 07:59:44.215900 -- /api/kess3/file-slots/065ef481-86fc-4bdc-9b43-830d64909ddb/close  -- 
2024-07-04 07:59:44.215900 -- AlientechLinkService processRequest -- 
2024-07-04 07:59:44.216200 -- isAuth -- 
2024-07-04 07:59:44.216500 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:59:44.441400 -- AlientechLinkService processResponse -- 
2024-07-04 07:59:44.444200 --  --- $log->save() --  ---330 -- 
2024-07-04 07:59:44.446200 --  --- $log->save() --  ---331 -- 
2024-07-04 07:59:44.446400 -- slot [065ef481-86fc-4bdc-9b43-830d64909ddb] closeSlot null -- 
2024-07-04 07:59:44.446500 -- slot [47ed44ee-eb42-4a15-ba8f-11e233daf51f] dateDiff {"y":0,"m":0,"d":0,"h":0,"i":15,"s":7,"f":0.936487,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 07:59:44.446600 -- FileSlotService closeSlot -- 
2024-07-04 07:59:44.446700 -- /api/kess3/file-slots/47ed44ee-eb42-4a15-ba8f-11e233daf51f/close  -- 
2024-07-04 07:59:44.446900 -- AlientechLinkService processRequest -- 
2024-07-04 07:59:44.447000 -- isAuth -- 
2024-07-04 07:59:44.447100 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:59:44.722000 -- AlientechLinkService processResponse -- 
2024-07-04 07:59:44.724800 --  --- $log->save() --  ---332 -- 
2024-07-04 07:59:44.726800 --  --- $log->save() --  ---333 -- 
2024-07-04 07:59:44.727000 -- slot [47ed44ee-eb42-4a15-ba8f-11e233daf51f] closeSlot null -- 
2024-07-04 07:59:44.727200 -- slot [9708b2bf-e4ed-47ea-bb97-a02762cab1eb] dateDiff {"y":0,"m":0,"d":0,"h":0,"i":55,"s":12,"f":0.29378,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 07:59:44.727400 -- FileSlotService closeSlot -- 
2024-07-04 07:59:44.727600 -- /api/kess3/file-slots/9708b2bf-e4ed-47ea-bb97-a02762cab1eb/close  -- 
2024-07-04 07:59:44.727900 -- AlientechLinkService processRequest -- 
2024-07-04 07:59:44.728100 -- isAuth -- 
2024-07-04 07:59:44.728300 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:59:45.026200 -- AlientechLinkService processResponse -- 
2024-07-04 07:59:45.028000 --  --- $log->save() --  ---334 -- 
2024-07-04 07:59:45.029600 --  --- $log->save() --  ---335 -- 
2024-07-04 07:59:45.029700 -- slot [9708b2bf-e4ed-47ea-bb97-a02762cab1eb] closeSlot null -- 
2024-07-04 07:59:45.029800 -- closeAllSlots finish -- 
2024-07-04 07:59:45.029800 -- /api/kess3/file-slots/9708b2bf-e4ed-47ea-bb97-a02762cab1eb/reopen -- 
2024-07-04 07:59:45.029900 -- AlientechLinkService processRequest -- 
2024-07-04 07:59:45.029900 -- isAuth -- 
2024-07-04 07:59:45.030000 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:59:45.207300 -- AlientechLinkService processResponse -- 
2024-07-04 07:59:45.209600 --  --- $log->save() --  ---336 -- 
2024-07-04 07:59:45.211400 --  --- $log->save() --  ---337 -- 
2024-07-04 07:59:45.211600 -- AlientechLinkService processRequest -- 
2024-07-04 07:59:45.211800 -- isAuth -- 
2024-07-04 07:59:45.212000 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:59:45.837200 -- AlientechLinkService processResponse -- 
2024-07-04 07:59:45.839900 --  --- $log->save() --  ---338 -- 
2024-07-04 07:59:45.841800 --  --- $log->save() --  ---339 -- 
2024-07-04 07:59:45.853500 -- AlientechLinkService processRequest -- 
2024-07-04 07:59:45.853700 -- isAuth -- 
2024-07-04 07:59:45.853700 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:59:46.391900 -- AlientechLinkService processResponse -- 
2024-07-04 07:59:46.394700 --  --- $log->save() --  ---340 -- 
2024-07-04 07:59:46.396600 --  --- $log->save() --  ---341 -- 
2024-07-04 07:59:46.396800 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 07:59:46.396900 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 07:59:46.397900 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 07:59:48.132200 -- AlientechLinkService __construct -- 
2024-07-04 07:59:48.132500 -- AlientechLinkService processClient -- 
2024-07-04 07:59:48.132600 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:59:48.132700 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:59:48.133400 -- iR8V9-KAuaE -- 
2024-07-04 07:59:48.133700 -- AlientechProjectService __construct -- 
2024-07-04 07:59:48.133900 -- FileSlotService __construct -- 
2024-07-04 07:59:48.134000 -- FileSlotService __construct -- 
2024-07-04 07:59:48.134100 -- AsyncOperationService __construct -- 
2024-07-04 07:59:48.134300 -- ApiController actionKess3Encoded -- 
2024-07-04 07:59:48.138400 --  --- $log->save() --  ---342 -- 
2024-07-04 07:59:48.138600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 07:59:48.138700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 07:59:48.140400 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 07:59:48.140500 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 07:59:48.143800 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 07:59:48.143900 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-04 07:59:48.144000 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-04 07:59:48.148400 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 07:59:48.269300 -- AlientechLinkService __construct -- 
2024-07-04 07:59:48.269500 -- AlientechLinkService processClient -- 
2024-07-04 07:59:48.269500 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 07:59:48.269900 -- AlientechLinkService initAccessToken -- 
2024-07-04 07:59:48.272500 -- iR8V9-KAuaE -- 
2024-07-04 07:59:48.272900 -- AlientechProjectService __construct -- 
2024-07-04 07:59:48.273800 -- FileSlotService __construct -- 
2024-07-04 07:59:48.274000 -- FileSlotService __construct -- 
2024-07-04 07:59:48.274100 -- AsyncOperationService __construct -- 
2024-07-04 07:59:48.274200 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-04 07:59:48.274300 -- FileService downloadFile -- 
2024-07-04 07:59:48.274400 -- AlientechLinkService processRequest -- 
2024-07-04 07:59:48.274400 -- isAuth -- 
2024-07-04 07:59:48.274400 -- AlientechLinkService isAuth_success -- 
2024-07-04 07:59:49.452300 -- AlientechLinkService processResponse -- 
2024-07-04 07:59:49.478900 --  --- $log->save() --  ---343 -- 
2024-07-04 07:59:49.645700 --  --- $log->save() --  ---344 -- 
2024-07-04 07:59:49.664800 -- FileService saveEncodedFile -- 
2024-07-04 07:59:49.689200 -- FileService $initFile->file_history={"id":"7604","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-04 07:59:49.698900 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-04 07:59:49.719600 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-07-04 08:13:31.802900 -- AlientechLinkService __construct -- 
2024-07-04 08:13:31.803100 -- AlientechLinkService processClient -- 
2024-07-04 08:13:31.803100 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 08:13:31.803400 -- AlientechLinkService initAccessToken -- 
2024-07-04 08:13:31.806500 -- iR8V9-KAuaE -- 
2024-07-04 08:13:31.808000 -- AlientechProjectService __construct -- 
2024-07-04 08:13:31.810500 -- FileSlotService __construct -- 
2024-07-04 08:13:31.811000 -- FileSlotService __construct -- 
2024-07-04 08:13:31.811100 -- AsyncOperationService __construct -- 
2024-07-04 08:13:31.812500 -- FileSlotService __construct -- 
2024-07-04 08:13:31.812600 -- Kess3Service __construct -- 
2024-07-04 08:13:31.812700 -- Kess3Service startDecoding -- 
2024-07-04 08:13:31.812800 -- AsyncOperationService startOperationDecode -- 
2024-07-04 08:13:31.812800 -- AlientechLinkService processRequest -- 
2024-07-04 08:13:31.812900 -- isAuth -- 
2024-07-04 08:13:31.813000 -- AlientechLinkService isAuth_success -- 
2024-07-04 08:13:33.735600 -- AlientechLinkService processResponse -- 
2024-07-04 08:13:33.754300 --  --- $log->save() --  ---345 -- 
2024-07-04 08:13:33.756100 --  --- $log->save() --  ---346 -- 
2024-07-04 08:13:33.756200 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 08:13:33.756300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 08:13:33.762900 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 08:13:35.614600 -- AlientechLinkService __construct -- 
2024-07-04 08:13:35.615400 -- AlientechLinkService processClient -- 
2024-07-04 08:13:35.615500 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 08:13:35.617200 -- AlientechLinkService initAccessToken -- 
2024-07-04 08:13:35.622600 -- iR8V9-KAuaE -- 
2024-07-04 08:13:35.623000 -- AlientechProjectService __construct -- 
2024-07-04 08:13:35.623200 -- FileSlotService __construct -- 
2024-07-04 08:13:35.623300 -- FileSlotService __construct -- 
2024-07-04 08:13:35.623400 -- AsyncOperationService __construct -- 
2024-07-04 08:13:35.623700 -- ApiController actionKess3Decoded -- 
2024-07-04 08:13:35.629900 --  --- $log->save() --  ---347 -- 
2024-07-04 08:13:35.630100 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 08:13:35.630200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 08:13:35.634100 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 08:13:35.634200 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 08:13:35.637900 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 08:13:35.638000 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-04 08:13:35.638100 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-04 08:13:35.641100 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 08:13:38.487500 -- AlientechLinkService __construct -- 
2024-07-04 08:13:38.487700 -- AlientechLinkService processClient -- 
2024-07-04 08:13:38.487800 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 08:13:38.488100 -- AlientechLinkService initAccessToken -- 
2024-07-04 08:13:38.490600 -- iR8V9-KAuaE -- 
2024-07-04 08:13:38.492100 -- AlientechProjectService __construct -- 
2024-07-04 08:13:38.494400 -- FileSlotService __construct -- 
2024-07-04 08:13:38.494800 -- FileSlotService __construct -- 
2024-07-04 08:13:38.494900 -- AsyncOperationService __construct -- 
2024-07-04 08:13:38.495400 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-04 08:13:38.496800 -- FileService downloadFile -- 
2024-07-04 08:13:38.497000 -- AlientechLinkService processRequest -- 
2024-07-04 08:13:38.497100 -- isAuth -- 
2024-07-04 08:13:38.497200 -- AlientechLinkService isAuth_success -- 
2024-07-04 08:13:40.050400 -- AlientechLinkService processResponse -- 
2024-07-04 08:13:40.077000 --  --- $log->save() --  ---348 -- 
2024-07-04 08:13:40.085500 --  --- $log->save() --  ---349 -- 
2024-07-04 08:13:40.086600 -- FileService saveDecodedFile -- 
2024-07-04 08:13:40.101600 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 08:13:40.146000 -- FileService downloadFile -- 
2024-07-04 08:13:40.146100 -- AlientechLinkService processRequest -- 
2024-07-04 08:13:40.146200 -- isAuth -- 
2024-07-04 08:13:40.146200 -- AlientechLinkService isAuth_success -- 
2024-07-04 08:13:41.054100 -- AlientechLinkService processResponse -- 
2024-07-04 08:13:41.056700 --  --- $log->save() --  ---350 -- 
2024-07-04 08:13:41.182700 --  --- $log->save() --  ---351 -- 
2024-07-04 08:13:41.198200 -- FileService saveDecodedFile -- 
2024-07-04 08:13:41.210800 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 08:25:57.140700 -- AlientechLinkService __construct -- 
2024-07-04 08:25:57.140900 -- AlientechLinkService processClient -- 
2024-07-04 08:25:57.140900 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 08:25:57.141300 -- AlientechLinkService initAccessToken -- 
2024-07-04 08:25:57.143900 -- iR8V9-KAuaE -- 
2024-07-04 08:25:57.145300 -- AlientechProjectService __construct -- 
2024-07-04 08:25:57.147500 -- FileSlotService __construct -- 
2024-07-04 08:25:57.147900 -- FileSlotService __construct -- 
2024-07-04 08:25:57.148100 -- AsyncOperationService __construct -- 
2024-07-04 08:25:57.149200 -- FileSlotService __construct -- 
2024-07-04 08:25:57.149400 -- Kess3Service __construct -- 
2024-07-04 08:25:57.149500 -- Kess3Service startEncoding -- 
2024-07-04 08:25:57.149600 -- AsyncOperationService startOperationEncode -- 
2024-07-04 08:25:57.181000 -- FileService uploadFiles -- 
2024-07-04 08:25:57.181300 -- FileSlotService reOpenSlot -- 
2024-07-04 08:25:57.181400 -- FileSlotService hasOpenFileSlots -- 
2024-07-04 08:25:57.181400 -- FileSlotService getSlots -- 
2024-07-04 08:25:57.181400 -- AlientechLinkService processRequest -- 
2024-07-04 08:25:57.181500 -- isAuth -- 
2024-07-04 08:25:57.181500 -- AlientechLinkService isAuth_success -- 
2024-07-04 08:25:57.425000 -- AlientechLinkService processResponse -- 
2024-07-04 08:25:57.436400 --  --- $log->save() --  ---352 -- 
2024-07-04 08:25:57.441200 --  --- $log->save() --  ---353 -- 
2024-07-04 08:25:57.441900 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-04 08:25:57.442000 -- /api/kess3/file-slots/47ed44ee-eb42-4a15-ba8f-11e233daf51f/reopen -- 
2024-07-04 08:25:57.442100 -- AlientechLinkService processRequest -- 
2024-07-04 08:25:57.442200 -- isAuth -- 
2024-07-04 08:25:57.442200 -- AlientechLinkService isAuth_success -- 
2024-07-04 08:25:57.623800 -- AlientechLinkService processResponse -- 
2024-07-04 08:25:57.627300 --  --- $log->save() --  ---354 -- 
2024-07-04 08:25:57.629300 --  --- $log->save() --  ---355 -- 
2024-07-04 08:25:57.629500 -- AlientechLinkService processRequest -- 
2024-07-04 08:25:57.629600 -- isAuth -- 
2024-07-04 08:25:57.629700 -- AlientechLinkService isAuth_success -- 
2024-07-04 08:25:59.553000 -- AlientechLinkService processResponse -- 
2024-07-04 08:25:59.555600 --  --- $log->save() --  ---356 -- 
2024-07-04 08:25:59.557600 --  --- $log->save() --  ---357 -- 
2024-07-04 08:25:59.567000 -- AlientechLinkService processRequest -- 
2024-07-04 08:25:59.567200 -- isAuth -- 
2024-07-04 08:25:59.567200 -- AlientechLinkService isAuth_success -- 
2024-07-04 08:26:00.104600 -- AlientechLinkService processResponse -- 
2024-07-04 08:26:00.107200 --  --- $log->save() --  ---358 -- 
2024-07-04 08:26:00.109400 --  --- $log->save() --  ---359 -- 
2024-07-04 08:26:00.109600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 08:26:00.109700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 08:26:00.110800 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 08:26:01.540400 -- AlientechLinkService __construct -- 
2024-07-04 08:26:01.540600 -- AlientechLinkService processClient -- 
2024-07-04 08:26:01.540600 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 08:26:01.540700 -- AlientechLinkService initAccessToken -- 
2024-07-04 08:26:01.541600 -- iR8V9-KAuaE -- 
2024-07-04 08:26:01.542000 -- AlientechProjectService __construct -- 
2024-07-04 08:26:01.542300 -- FileSlotService __construct -- 
2024-07-04 08:26:01.542400 -- FileSlotService __construct -- 
2024-07-04 08:26:01.542500 -- AsyncOperationService __construct -- 
2024-07-04 08:26:01.542700 -- ApiController actionKess3Encoded -- 
2024-07-04 08:26:01.545700 --  --- $log->save() --  ---360 -- 
2024-07-04 08:26:01.545800 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 08:26:01.545900 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 08:26:01.547200 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 08:26:01.547300 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 08:26:01.552300 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 08:26:01.552400 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-04 08:26:01.552500 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-04 08:26:01.555800 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 08:26:04.208500 -- AlientechLinkService __construct -- 
2024-07-04 08:26:04.208600 -- AlientechLinkService processClient -- 
2024-07-04 08:26:04.208700 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 08:26:04.209000 -- AlientechLinkService initAccessToken -- 
2024-07-04 08:26:04.211700 -- iR8V9-KAuaE -- 
2024-07-04 08:26:04.213000 -- AlientechProjectService __construct -- 
2024-07-04 08:26:04.215100 -- FileSlotService __construct -- 
2024-07-04 08:26:04.215600 -- FileSlotService __construct -- 
2024-07-04 08:26:04.215700 -- AsyncOperationService __construct -- 
2024-07-04 08:26:04.216200 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-04 08:26:04.216700 -- FileService downloadFile -- 
2024-07-04 08:26:04.216800 -- AlientechLinkService processRequest -- 
2024-07-04 08:26:04.216900 -- isAuth -- 
2024-07-04 08:26:04.217000 -- AlientechLinkService isAuth_success -- 
2024-07-04 08:26:05.460100 -- AlientechLinkService processResponse -- 
2024-07-04 08:26:05.487100 --  --- $log->save() --  ---361 -- 
2024-07-04 08:26:05.741900 --  --- $log->save() --  ---362 -- 
2024-07-04 08:26:05.769100 -- FileService saveEncodedFile -- 
2024-07-04 08:26:05.800500 -- FileService $initFile->file_history={"id":"7605","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-04 08:26:05.809400 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-04 09:18:36.819600 -- AlientechLinkService __construct -- 
2024-07-04 09:18:36.819800 -- AlientechLinkService processClient -- 
2024-07-04 09:18:36.819800 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:18:36.820200 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:18:36.822500 -- iR8V9-KAuaE -- 
2024-07-04 09:18:36.823000 -- AlientechProjectService __construct -- 
2024-07-04 09:18:36.823700 -- FileSlotService __construct -- 
2024-07-04 09:18:36.823900 -- FileSlotService __construct -- 
2024-07-04 09:18:36.823900 -- AsyncOperationService __construct -- 
2024-07-04 09:18:36.824400 -- FileSlotService __construct -- 
2024-07-04 09:18:36.824400 -- Kess3Service __construct -- 
2024-07-04 09:18:36.824400 -- Kess3Service startEncoding -- 
2024-07-04 09:18:36.824500 -- AsyncOperationService startOperationEncode -- 
2024-07-04 09:18:36.839600 -- FileService uploadFiles -- 
2024-07-04 09:18:36.839900 -- FileSlotService reOpenSlot -- 
2024-07-04 09:18:36.840000 -- FileSlotService hasOpenFileSlots -- 
2024-07-04 09:18:36.840000 -- FileSlotService getSlots -- 
2024-07-04 09:18:36.840000 -- AlientechLinkService processRequest -- 
2024-07-04 09:18:36.840100 -- isAuth -- 
2024-07-04 09:18:36.840100 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:18:37.862000 -- AlientechLinkService processResponse -- 
2024-07-04 09:18:37.872300 --  --- $log->save() --  ---363 -- 
2024-07-04 09:18:37.876600 --  --- $log->save() --  ---364 -- 
2024-07-04 09:18:37.877200 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-04 09:18:37.877300 -- FileSlotService closeAllFileSlots -- 
2024-07-04 09:18:37.877400 -- FileSlotService getSlots -- 
2024-07-04 09:18:37.877400 -- AlientechLinkService processRequest -- 
2024-07-04 09:18:37.877500 -- isAuth -- 
2024-07-04 09:18:37.877600 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:18:38.084300 -- AlientechLinkService processResponse -- 
2024-07-04 09:18:38.086600 --  --- $log->save() --  ---365 -- 
2024-07-04 09:18:38.090700 --  --- $log->save() --  ---366 -- 
2024-07-04 09:18:38.091800 -- slot [045e832f-3ede-4ed0-be1e-593f4ef5a91e] dateDiff {"y":0,"m":0,"d":0,"h":1,"i":5,"s":4,"f":0.408393,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 09:18:38.092000 -- FileSlotService closeSlot -- 
2024-07-04 09:18:38.092200 -- /api/kess3/file-slots/045e832f-3ede-4ed0-be1e-593f4ef5a91e/close  -- 
2024-07-04 09:18:38.092300 -- AlientechLinkService processRequest -- 
2024-07-04 09:18:38.092400 -- isAuth -- 
2024-07-04 09:18:38.092500 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:18:38.689700 -- AlientechLinkService processResponse -- 
2024-07-04 09:18:38.692300 --  --- $log->save() --  ---367 -- 
2024-07-04 09:18:38.695100 --  --- $log->save() --  ---368 -- 
2024-07-04 09:18:38.695400 -- slot [045e832f-3ede-4ed0-be1e-593f4ef5a91e] closeSlot null -- 
2024-07-04 09:18:38.695700 -- slot [47ed44ee-eb42-4a15-ba8f-11e233daf51f] dateDiff {"y":0,"m":0,"d":0,"h":1,"i":34,"s":2,"f":0.185626,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 09:18:38.695800 -- FileSlotService closeSlot -- 
2024-07-04 09:18:38.695900 -- /api/kess3/file-slots/47ed44ee-eb42-4a15-ba8f-11e233daf51f/close  -- 
2024-07-04 09:18:38.696100 -- AlientechLinkService processRequest -- 
2024-07-04 09:18:38.696200 -- isAuth -- 
2024-07-04 09:18:38.696300 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:18:38.881800 -- AlientechLinkService processResponse -- 
2024-07-04 09:18:38.883800 --  --- $log->save() --  ---369 -- 
2024-07-04 09:18:38.886400 --  --- $log->save() --  ---370 -- 
2024-07-04 09:18:38.886500 -- slot [47ed44ee-eb42-4a15-ba8f-11e233daf51f] closeSlot null -- 
2024-07-04 09:18:38.886600 -- slot [9708b2bf-e4ed-47ea-bb97-a02762cab1eb] dateDiff {"y":0,"m":0,"d":0,"h":2,"i":14,"s":6,"f":0.453222,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 09:18:38.886600 -- FileSlotService closeSlot -- 
2024-07-04 09:18:38.886700 -- /api/kess3/file-slots/9708b2bf-e4ed-47ea-bb97-a02762cab1eb/close  -- 
2024-07-04 09:18:38.886800 -- AlientechLinkService processRequest -- 
2024-07-04 09:18:38.886900 -- isAuth -- 
2024-07-04 09:18:38.887000 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:18:39.131800 -- AlientechLinkService processResponse -- 
2024-07-04 09:18:39.133800 --  --- $log->save() --  ---371 -- 
2024-07-04 09:18:39.135600 --  --- $log->save() --  ---372 -- 
2024-07-04 09:18:39.135700 -- slot [9708b2bf-e4ed-47ea-bb97-a02762cab1eb] closeSlot null -- 
2024-07-04 09:18:39.136000 -- closeAllSlots finish -- 
2024-07-04 09:18:39.136100 -- /api/kess3/file-slots/065ef481-86fc-4bdc-9b43-830d64909ddb/reopen -- 
2024-07-04 09:18:39.136200 -- AlientechLinkService processRequest -- 
2024-07-04 09:18:39.136400 -- isAuth -- 
2024-07-04 09:18:39.136500 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:18:39.325400 -- AlientechLinkService processResponse -- 
2024-07-04 09:18:39.328500 --  --- $log->save() --  ---373 -- 
2024-07-04 09:18:39.330700 --  --- $log->save() --  ---374 -- 
2024-07-04 09:18:39.330800 -- AlientechLinkService processRequest -- 
2024-07-04 09:18:39.330900 -- isAuth -- 
2024-07-04 09:18:39.330900 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:18:39.841700 -- AlientechLinkService processResponse -- 
2024-07-04 09:18:39.844400 --  --- $log->save() --  ---375 -- 
2024-07-04 09:18:39.846800 --  --- $log->save() --  ---376 -- 
2024-07-04 09:18:39.858100 -- AlientechLinkService processRequest -- 
2024-07-04 09:18:39.858200 -- isAuth -- 
2024-07-04 09:18:39.858300 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:18:40.846100 -- AlientechLinkService processResponse -- 
2024-07-04 09:18:40.848600 --  --- $log->save() --  ---377 -- 
2024-07-04 09:18:40.851000 --  --- $log->save() --  ---378 -- 
2024-07-04 09:18:40.851200 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 09:18:40.851300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 09:18:40.852400 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 09:18:44.302200 -- AlientechLinkService __construct -- 
2024-07-04 09:18:44.302600 -- AlientechLinkService processClient -- 
2024-07-04 09:18:44.302700 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:18:44.302800 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:18:44.303500 -- iR8V9-KAuaE -- 
2024-07-04 09:18:44.303600 -- AlientechProjectService __construct -- 
2024-07-04 09:18:44.303800 -- FileSlotService __construct -- 
2024-07-04 09:18:44.303800 -- FileSlotService __construct -- 
2024-07-04 09:18:44.303900 -- AsyncOperationService __construct -- 
2024-07-04 09:18:44.304000 -- ApiController actionKess3Encoded -- 
2024-07-04 09:18:44.308100 --  --- $log->save() --  ---379 -- 
2024-07-04 09:18:44.308300 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 09:18:44.308300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 09:18:44.313000 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 09:18:44.313100 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 09:18:44.316000 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 09:18:44.316100 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-04 09:18:44.316200 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-04 09:18:44.318600 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 09:18:45.690100 -- AlientechLinkService __construct -- 
2024-07-04 09:18:45.690200 -- AlientechLinkService processClient -- 
2024-07-04 09:18:45.690300 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:18:45.690600 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:18:45.693000 -- iR8V9-KAuaE -- 
2024-07-04 09:18:45.694000 -- AlientechProjectService __construct -- 
2024-07-04 09:18:45.695500 -- FileSlotService __construct -- 
2024-07-04 09:18:45.695800 -- FileSlotService __construct -- 
2024-07-04 09:18:45.695900 -- AsyncOperationService __construct -- 
2024-07-04 09:18:45.696200 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-04 09:18:45.696500 -- FileService downloadFile -- 
2024-07-04 09:18:45.696500 -- AlientechLinkService processRequest -- 
2024-07-04 09:18:45.696600 -- isAuth -- 
2024-07-04 09:18:45.696700 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:18:46.112300 -- AlientechLinkService processResponse -- 
2024-07-04 09:18:46.138000 --  --- $log->save() --  ---380 -- 
2024-07-04 09:18:46.295800 --  --- $log->save() --  ---381 -- 
2024-07-04 09:18:46.323200 -- FileService saveEncodedFile -- 
2024-07-04 09:18:46.352300 -- FileService $initFile->file_history={"id":"7606","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-04 09:18:46.369200 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-04 09:25:46.869400 -- AlientechLinkService __construct -- 
2024-07-04 09:25:46.869600 -- AlientechLinkService processClient -- 
2024-07-04 09:25:46.869600 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:25:46.870200 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:25:46.873200 -- iR8V9-KAuaE -- 
2024-07-04 09:25:46.873900 -- AlientechProjectService __construct -- 
2024-07-04 09:25:46.874900 -- FileSlotService __construct -- 
2024-07-04 09:25:46.875400 -- FileSlotService __construct -- 
2024-07-04 09:25:46.875400 -- AsyncOperationService __construct -- 
2024-07-04 09:25:46.876400 -- FileSlotService __construct -- 
2024-07-04 09:25:46.876500 -- Kess3Service __construct -- 
2024-07-04 09:25:46.876500 -- Kess3Service startDecoding -- 
2024-07-04 09:25:46.876500 -- AsyncOperationService startOperationDecode -- 
2024-07-04 09:25:46.876600 -- AlientechLinkService processRequest -- 
2024-07-04 09:25:46.876600 -- isAuth -- 
2024-07-04 09:25:46.876600 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:25:47.254800 -- AlientechLinkService processResponse -- 
2024-07-04 09:25:47.265100 --  --- $log->save() --  ---382 -- 
2024-07-04 09:25:47.270300 --  --- $log->save() --  ---383 -- 
2024-07-04 09:25:47.270400 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 09:25:47.270500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 09:25:47.276600 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 09:25:48.798500 -- AlientechLinkService __construct -- 
2024-07-04 09:25:48.798700 -- AlientechLinkService processClient -- 
2024-07-04 09:25:48.798800 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:25:48.798800 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:25:48.799600 -- iR8V9-KAuaE -- 
2024-07-04 09:25:48.799700 -- AlientechProjectService __construct -- 
2024-07-04 09:25:48.799800 -- FileSlotService __construct -- 
2024-07-04 09:25:48.799900 -- FileSlotService __construct -- 
2024-07-04 09:25:48.799900 -- AsyncOperationService __construct -- 
2024-07-04 09:25:48.800100 -- ApiController actionKess3Decoded -- 
2024-07-04 09:25:48.802500 --  --- $log->save() --  ---384 -- 
2024-07-04 09:25:48.802800 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 09:25:48.802800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 09:25:48.804300 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 09:25:48.804400 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 09:25:48.807100 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 09:25:48.807200 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-04 09:25:48.807200 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-04 09:25:48.809500 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 09:25:50.976800 -- AlientechLinkService __construct -- 
2024-07-04 09:25:50.976900 -- AlientechLinkService processClient -- 
2024-07-04 09:25:50.976900 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:25:50.977300 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:25:50.979600 -- iR8V9-KAuaE -- 
2024-07-04 09:25:50.980800 -- AlientechProjectService __construct -- 
2024-07-04 09:25:50.983000 -- FileSlotService __construct -- 
2024-07-04 09:25:50.984000 -- FileSlotService __construct -- 
2024-07-04 09:25:50.984100 -- AsyncOperationService __construct -- 
2024-07-04 09:25:50.984600 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-04 09:25:50.985800 -- FileService downloadFile -- 
2024-07-04 09:25:50.986000 -- AlientechLinkService processRequest -- 
2024-07-04 09:25:50.986100 -- isAuth -- 
2024-07-04 09:25:50.986100 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:25:51.315600 -- AlientechLinkService processResponse -- 
2024-07-04 09:25:51.349900 --  --- $log->save() --  ---385 -- 
2024-07-04 09:25:51.352200 --  --- $log->save() --  ---386 -- 
2024-07-04 09:25:51.352800 -- FileService saveDecodedFile -- 
2024-07-04 09:25:51.370700 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 09:25:51.424000 -- FileService downloadFile -- 
2024-07-04 09:25:51.424100 -- AlientechLinkService processRequest -- 
2024-07-04 09:25:51.424200 -- isAuth -- 
2024-07-04 09:25:51.424200 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:25:52.740600 -- AlientechLinkService processResponse -- 
2024-07-04 09:25:52.744700 --  --- $log->save() --  ---387 -- 
2024-07-04 09:25:52.890000 --  --- $log->save() --  ---388 -- 
2024-07-04 09:25:52.912900 -- FileService saveDecodedFile -- 
2024-07-04 09:25:52.931100 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 09:38:01.100200 -- AlientechLinkService __construct -- 
2024-07-04 09:38:01.100400 -- AlientechLinkService processClient -- 
2024-07-04 09:38:01.100400 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:38:01.100700 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:38:01.106700 -- iR8V9-KAuaE -- 
2024-07-04 09:38:01.108300 -- AlientechProjectService __construct -- 
2024-07-04 09:38:01.109200 -- FileSlotService __construct -- 
2024-07-04 09:38:01.109400 -- FileSlotService __construct -- 
2024-07-04 09:38:01.109400 -- AsyncOperationService __construct -- 
2024-07-04 09:38:01.110300 -- FileSlotService __construct -- 
2024-07-04 09:38:01.110300 -- Kess3Service __construct -- 
2024-07-04 09:38:01.110400 -- Kess3Service startEncoding -- 
2024-07-04 09:38:01.110400 -- AsyncOperationService startOperationEncode -- 
2024-07-04 09:38:01.129300 -- FileService uploadFiles -- 
2024-07-04 09:38:01.129600 -- FileSlotService reOpenSlot -- 
2024-07-04 09:38:01.129600 -- FileSlotService hasOpenFileSlots -- 
2024-07-04 09:38:01.129600 -- FileSlotService getSlots -- 
2024-07-04 09:38:01.129700 -- AlientechLinkService processRequest -- 
2024-07-04 09:38:01.129700 -- isAuth -- 
2024-07-04 09:38:01.129700 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:38:01.401800 -- AlientechLinkService processResponse -- 
2024-07-04 09:38:01.410200 --  --- $log->save() --  ---389 -- 
2024-07-04 09:38:01.414700 --  --- $log->save() --  ---390 -- 
2024-07-04 09:38:01.415400 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-04 09:38:01.415600 -- /api/kess3/file-slots/773ab7c4-0fb4-4360-a4b8-42faa5ddacf3/reopen -- 
2024-07-04 09:38:01.416400 -- AlientechLinkService processRequest -- 
2024-07-04 09:38:01.417300 -- isAuth -- 
2024-07-04 09:38:01.418200 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:38:02.254800 -- AlientechLinkService processResponse -- 
2024-07-04 09:38:02.257300 --  --- $log->save() --  ---391 -- 
2024-07-04 09:38:02.259100 --  --- $log->save() --  ---392 -- 
2024-07-04 09:38:02.259300 -- AlientechLinkService processRequest -- 
2024-07-04 09:38:02.259600 -- isAuth -- 
2024-07-04 09:38:02.259700 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:38:03.022400 -- AlientechLinkService processResponse -- 
2024-07-04 09:38:03.024900 --  --- $log->save() --  ---393 -- 
2024-07-04 09:38:03.027500 --  --- $log->save() --  ---394 -- 
2024-07-04 09:38:03.039200 -- AlientechLinkService processRequest -- 
2024-07-04 09:38:03.039400 -- isAuth -- 
2024-07-04 09:38:03.039500 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:38:04.167500 -- AlientechLinkService processResponse -- 
2024-07-04 09:38:04.170300 --  --- $log->save() --  ---395 -- 
2024-07-04 09:38:04.172400 --  --- $log->save() --  ---396 -- 
2024-07-04 09:38:04.172500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 09:38:04.172800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 09:38:04.173800 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 09:38:06.048600 -- AlientechLinkService __construct -- 
2024-07-04 09:38:06.048800 -- AlientechLinkService processClient -- 
2024-07-04 09:38:06.048800 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:38:06.048900 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:38:06.049500 -- iR8V9-KAuaE -- 
2024-07-04 09:38:06.049600 -- AlientechProjectService __construct -- 
2024-07-04 09:38:06.049800 -- FileSlotService __construct -- 
2024-07-04 09:38:06.049800 -- FileSlotService __construct -- 
2024-07-04 09:38:06.049800 -- AsyncOperationService __construct -- 
2024-07-04 09:38:06.050000 -- ApiController actionKess3Encoded -- 
2024-07-04 09:38:06.052200 --  --- $log->save() --  ---397 -- 
2024-07-04 09:38:06.052300 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 09:38:06.052400 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 09:38:06.053700 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 09:38:06.053800 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 09:38:06.055900 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 09:38:06.056000 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-04 09:38:06.056100 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-04 09:38:06.059000 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 09:38:06.320700 -- AlientechLinkService __construct -- 
2024-07-04 09:38:06.320900 -- AlientechLinkService processClient -- 
2024-07-04 09:38:06.320900 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:38:06.321300 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:38:06.323900 -- iR8V9-KAuaE -- 
2024-07-04 09:38:06.324600 -- AlientechProjectService __construct -- 
2024-07-04 09:38:06.325600 -- FileSlotService __construct -- 
2024-07-04 09:38:06.325800 -- FileSlotService __construct -- 
2024-07-04 09:38:06.325900 -- AsyncOperationService __construct -- 
2024-07-04 09:38:06.326100 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-04 09:38:06.326300 -- FileService downloadFile -- 
2024-07-04 09:38:06.326300 -- AlientechLinkService processRequest -- 
2024-07-04 09:38:06.326400 -- isAuth -- 
2024-07-04 09:38:06.326400 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:38:07.109300 -- AlientechLinkService processResponse -- 
2024-07-04 09:38:07.134600 --  --- $log->save() --  ---398 -- 
2024-07-04 09:38:07.198300 --  --- $log->save() --  ---399 -- 
2024-07-04 09:38:07.213400 -- FileService saveEncodedFile -- 
2024-07-04 09:38:07.239700 -- FileService $initFile->file_history={"id":"7609","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-04 09:38:07.251800 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-04 09:38:07.270800 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-07-04 09:43:22.566000 -- AlientechLinkService __construct -- 
2024-07-04 09:43:22.566500 -- AlientechLinkService processClient -- 
2024-07-04 09:43:22.566700 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:43:22.567400 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:43:22.569900 -- iR8V9-KAuaE -- 
2024-07-04 09:43:22.570600 -- AlientechProjectService __construct -- 
2024-07-04 09:43:22.571800 -- FileSlotService __construct -- 
2024-07-04 09:43:22.572100 -- FileSlotService __construct -- 
2024-07-04 09:43:22.572300 -- AsyncOperationService __construct -- 
2024-07-04 09:43:22.573000 -- FileSlotService __construct -- 
2024-07-04 09:43:22.573400 -- Kess3Service __construct -- 
2024-07-04 09:43:22.573800 -- Kess3Service startDecoding -- 
2024-07-04 09:43:22.574000 -- AsyncOperationService startOperationDecode -- 
2024-07-04 09:43:22.574300 -- AlientechLinkService processRequest -- 
2024-07-04 09:43:22.574500 -- isAuth -- 
2024-07-04 09:43:22.574700 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:43:22.976400 -- AlientechLinkService processResponse -- 
2024-07-04 09:43:22.993800 --  --- $log->save() --  ---400 -- 
2024-07-04 09:43:22.995200 --  --- $log->save() --  ---401 -- 
2024-07-04 09:43:22.995400 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 09:43:22.995500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 09:43:23.002200 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 09:43:24.781800 -- AlientechLinkService __construct -- 
2024-07-04 09:43:24.782000 -- AlientechLinkService processClient -- 
2024-07-04 09:43:24.782100 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:43:24.782200 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:43:24.784000 -- iR8V9-KAuaE -- 
2024-07-04 09:43:24.784400 -- AlientechProjectService __construct -- 
2024-07-04 09:43:24.784700 -- FileSlotService __construct -- 
2024-07-04 09:43:24.784800 -- FileSlotService __construct -- 
2024-07-04 09:43:24.784900 -- AsyncOperationService __construct -- 
2024-07-04 09:43:24.785100 -- ApiController actionKess3Decoded -- 
2024-07-04 09:43:24.789100 --  --- $log->save() --  ---402 -- 
2024-07-04 09:43:24.789400 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 09:43:24.789500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 09:43:24.791800 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 09:43:24.791900 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 09:43:24.794600 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 09:43:24.794700 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-04 09:43:24.794800 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-04 09:43:24.797300 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 09:43:28.046700 -- AlientechLinkService __construct -- 
2024-07-04 09:43:28.046900 -- AlientechLinkService processClient -- 
2024-07-04 09:43:28.046900 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 09:43:28.047200 -- AlientechLinkService initAccessToken -- 
2024-07-04 09:43:28.050100 -- iR8V9-KAuaE -- 
2024-07-04 09:43:28.051600 -- AlientechProjectService __construct -- 
2024-07-04 09:43:28.054000 -- FileSlotService __construct -- 
2024-07-04 09:43:28.054400 -- FileSlotService __construct -- 
2024-07-04 09:43:28.054500 -- AsyncOperationService __construct -- 
2024-07-04 09:43:28.054800 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-04 09:43:28.055900 -- FileService downloadFile -- 
2024-07-04 09:43:28.056000 -- AlientechLinkService processRequest -- 
2024-07-04 09:43:28.056100 -- isAuth -- 
2024-07-04 09:43:28.056100 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:43:28.417100 -- AlientechLinkService processResponse -- 
2024-07-04 09:43:28.442000 --  --- $log->save() --  ---403 -- 
2024-07-04 09:43:28.463100 --  --- $log->save() --  ---404 -- 
2024-07-04 09:43:28.465700 -- FileService saveDecodedFile -- 
2024-07-04 09:43:28.494900 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 09:43:28.561600 -- FileService downloadFile -- 
2024-07-04 09:43:28.561800 -- AlientechLinkService processRequest -- 
2024-07-04 09:43:28.561900 -- isAuth -- 
2024-07-04 09:43:28.561900 -- AlientechLinkService isAuth_success -- 
2024-07-04 09:43:29.589500 -- AlientechLinkService processResponse -- 
2024-07-04 09:43:29.592000 --  --- $log->save() --  ---405 -- 
2024-07-04 09:43:29.775100 --  --- $log->save() --  ---406 -- 
2024-07-04 09:43:29.791800 -- FileService saveDecodedFile -- 
2024-07-04 09:43:29.809400 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 10:13:18.519200 -- AlientechLinkService __construct -- 
2024-07-04 10:13:18.519400 -- AlientechLinkService processClient -- 
2024-07-04 10:13:18.519400 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 10:13:18.519800 -- AlientechLinkService initAccessToken -- 
2024-07-04 10:13:18.522800 -- iR8V9-KAuaE -- 
2024-07-04 10:13:18.523400 -- AlientechProjectService __construct -- 
2024-07-04 10:13:18.524200 -- FileSlotService __construct -- 
2024-07-04 10:13:18.524400 -- FileSlotService __construct -- 
2024-07-04 10:13:18.524400 -- AsyncOperationService __construct -- 
2024-07-04 10:13:18.524900 -- FileSlotService __construct -- 
2024-07-04 10:13:18.524900 -- Kess3Service __construct -- 
2024-07-04 10:13:18.525000 -- Kess3Service startEncoding -- 
2024-07-04 10:13:18.525000 -- AsyncOperationService startOperationEncode -- 
2024-07-04 10:13:18.545000 -- FileService uploadFiles -- 
2024-07-04 10:13:18.545400 -- FileSlotService reOpenSlot -- 
2024-07-04 10:13:18.545500 -- FileSlotService hasOpenFileSlots -- 
2024-07-04 10:13:18.545600 -- FileSlotService getSlots -- 
2024-07-04 10:13:18.545600 -- AlientechLinkService processRequest -- 
2024-07-04 10:13:18.545700 -- isAuth -- 
2024-07-04 10:13:18.545700 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:13:18.860200 -- AlientechLinkService processResponse -- 
2024-07-04 10:13:18.870800 --  --- $log->save() --  ---407 -- 
2024-07-04 10:13:18.875100 --  --- $log->save() --  ---408 -- 
2024-07-04 10:13:18.876300 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-04 10:13:18.876500 -- FileSlotService closeAllFileSlots -- 
2024-07-04 10:13:18.876600 -- FileSlotService getSlots -- 
2024-07-04 10:13:18.876700 -- AlientechLinkService processRequest -- 
2024-07-04 10:13:18.876800 -- isAuth -- 
2024-07-04 10:13:18.876900 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:13:19.425200 -- AlientechLinkService processResponse -- 
2024-07-04 10:13:19.428100 --  --- $log->save() --  ---409 -- 
2024-07-04 10:13:19.433900 --  --- $log->save() --  ---410 -- 
2024-07-04 10:13:19.434700 -- slot [3764cfed-35f6-4419-907e-9cb6e6675279] dateDiff {"y":0,"m":0,"d":0,"h":0,"i":29,"s":56,"f":0.511283,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 10:13:19.434800 -- FileSlotService closeSlot -- 
2024-07-04 10:13:19.434800 -- /api/kess3/file-slots/3764cfed-35f6-4419-907e-9cb6e6675279/close  -- 
2024-07-04 10:13:19.434900 -- AlientechLinkService processRequest -- 
2024-07-04 10:13:19.434900 -- isAuth -- 
2024-07-04 10:13:19.435000 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:13:20.026800 -- AlientechLinkService processResponse -- 
2024-07-04 10:13:20.028900 --  --- $log->save() --  ---411 -- 
2024-07-04 10:13:20.030400 --  --- $log->save() --  ---412 -- 
2024-07-04 10:13:20.030600 -- slot [3764cfed-35f6-4419-907e-9cb6e6675279] closeSlot null -- 
2024-07-04 10:13:20.030700 -- slot [773ab7c4-0fb4-4360-a4b8-42faa5ddacf3] dateDiff {"y":0,"m":0,"d":0,"h":0,"i":47,"s":32,"f":0.820636,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 10:13:20.030700 -- FileSlotService closeSlot -- 
2024-07-04 10:13:20.030700 -- /api/kess3/file-slots/773ab7c4-0fb4-4360-a4b8-42faa5ddacf3/close  -- 
2024-07-04 10:13:20.030800 -- AlientechLinkService processRequest -- 
2024-07-04 10:13:20.030900 -- isAuth -- 
2024-07-04 10:13:20.031000 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:13:20.611500 -- AlientechLinkService processResponse -- 
2024-07-04 10:13:20.613100 --  --- $log->save() --  ---413 -- 
2024-07-04 10:13:20.614700 --  --- $log->save() --  ---414 -- 
2024-07-04 10:13:20.614900 -- slot [773ab7c4-0fb4-4360-a4b8-42faa5ddacf3] closeSlot null -- 
2024-07-04 10:13:20.614900 -- slot [065ef481-86fc-4bdc-9b43-830d64909ddb] dateDiff {"y":0,"m":0,"d":0,"h":2,"i":16,"s":8,"f":0.091581,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 10:13:20.615000 -- FileSlotService closeSlot -- 
2024-07-04 10:13:20.615000 -- /api/kess3/file-slots/065ef481-86fc-4bdc-9b43-830d64909ddb/close  -- 
2024-07-04 10:13:20.615200 -- AlientechLinkService processRequest -- 
2024-07-04 10:13:20.615200 -- isAuth -- 
2024-07-04 10:13:20.615300 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:13:20.794900 -- AlientechLinkService processResponse -- 
2024-07-04 10:13:20.797700 --  --- $log->save() --  ---415 -- 
2024-07-04 10:13:20.799900 --  --- $log->save() --  ---416 -- 
2024-07-04 10:13:20.800100 -- slot [065ef481-86fc-4bdc-9b43-830d64909ddb] closeSlot null -- 
2024-07-04 10:13:20.800200 -- closeAllSlots finish -- 
2024-07-04 10:13:20.800200 -- /api/kess3/file-slots/045e832f-3ede-4ed0-be1e-593f4ef5a91e/reopen -- 
2024-07-04 10:13:20.800300 -- AlientechLinkService processRequest -- 
2024-07-04 10:13:20.800500 -- isAuth -- 
2024-07-04 10:13:20.800700 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:13:20.977600 -- AlientechLinkService processResponse -- 
2024-07-04 10:13:20.980100 --  --- $log->save() --  ---417 -- 
2024-07-04 10:13:20.982300 --  --- $log->save() --  ---418 -- 
2024-07-04 10:13:20.982500 -- AlientechLinkService processRequest -- 
2024-07-04 10:13:20.982600 -- isAuth -- 
2024-07-04 10:13:20.983100 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:13:21.698100 -- AlientechLinkService processResponse -- 
2024-07-04 10:13:21.700700 --  --- $log->save() --  ---419 -- 
2024-07-04 10:13:21.703000 --  --- $log->save() --  ---420 -- 
2024-07-04 10:13:21.714500 -- AlientechLinkService processRequest -- 
2024-07-04 10:13:21.714700 -- isAuth -- 
2024-07-04 10:13:21.714800 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:13:22.576100 -- AlientechLinkService processResponse -- 
2024-07-04 10:13:22.578800 --  --- $log->save() --  ---421 -- 
2024-07-04 10:13:22.581200 --  --- $log->save() --  ---422 -- 
2024-07-04 10:13:22.581400 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 10:13:22.581400 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 10:13:22.582300 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 10:13:24.751800 -- AlientechLinkService __construct -- 
2024-07-04 10:13:24.751900 -- AlientechLinkService processClient -- 
2024-07-04 10:13:24.752000 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 10:13:24.752100 -- AlientechLinkService initAccessToken -- 
2024-07-04 10:13:24.752700 -- iR8V9-KAuaE -- 
2024-07-04 10:13:24.752900 -- AlientechProjectService __construct -- 
2024-07-04 10:13:24.753100 -- FileSlotService __construct -- 
2024-07-04 10:13:24.753100 -- FileSlotService __construct -- 
2024-07-04 10:13:24.753200 -- AsyncOperationService __construct -- 
2024-07-04 10:13:24.753300 -- ApiController actionKess3Encoded -- 
2024-07-04 10:13:24.755400 --  --- $log->save() --  ---423 -- 
2024-07-04 10:13:24.755500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 10:13:24.755500 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 10:13:24.756600 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 10:13:24.756700 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 10:13:24.758900 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 10:13:24.759000 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-04 10:13:24.759000 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-04 10:13:24.761600 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 10:13:25.657500 -- AlientechLinkService __construct -- 
2024-07-04 10:13:25.657800 -- AlientechLinkService processClient -- 
2024-07-04 10:13:25.657800 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 10:13:25.658300 -- AlientechLinkService initAccessToken -- 
2024-07-04 10:13:25.661200 -- iR8V9-KAuaE -- 
2024-07-04 10:13:25.662000 -- AlientechProjectService __construct -- 
2024-07-04 10:13:25.663100 -- FileSlotService __construct -- 
2024-07-04 10:13:25.663400 -- FileSlotService __construct -- 
2024-07-04 10:13:25.663400 -- AsyncOperationService __construct -- 
2024-07-04 10:13:25.663700 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-04 10:13:25.663800 -- FileService downloadFile -- 
2024-07-04 10:13:25.663900 -- AlientechLinkService processRequest -- 
2024-07-04 10:13:25.663900 -- isAuth -- 
2024-07-04 10:13:25.663900 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:13:25.999800 -- AlientechLinkService processResponse -- 
2024-07-04 10:13:26.027000 --  --- $log->save() --  ---424 -- 
2024-07-04 10:13:26.090800 --  --- $log->save() --  ---425 -- 
2024-07-04 10:13:26.098700 -- FileService saveEncodedFile -- 
2024-07-04 10:13:26.129200 -- FileService $initFile->file_history={"id":"7607","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-04 10:13:26.151000 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-04 10:36:46.652300 -- AlientechLinkService __construct -- 
2024-07-04 10:36:46.652800 -- AlientechLinkService processClient -- 
2024-07-04 10:36:46.652800 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 10:36:46.653300 -- AlientechLinkService initAccessToken -- 
2024-07-04 10:36:46.655900 -- iR8V9-KAuaE -- 
2024-07-04 10:36:46.656500 -- AlientechProjectService __construct -- 
2024-07-04 10:36:46.657600 -- FileSlotService __construct -- 
2024-07-04 10:36:46.657700 -- FileSlotService __construct -- 
2024-07-04 10:36:46.657800 -- AsyncOperationService __construct -- 
2024-07-04 10:36:46.658400 -- FileSlotService __construct -- 
2024-07-04 10:36:46.658400 -- Kess3Service __construct -- 
2024-07-04 10:36:46.658500 -- Kess3Service startDecoding -- 
2024-07-04 10:36:46.658500 -- AsyncOperationService startOperationDecode -- 
2024-07-04 10:36:46.658500 -- AlientechLinkService processRequest -- 
2024-07-04 10:36:46.658500 -- isAuth -- 
2024-07-04 10:36:46.658600 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:36:47.106400 -- AlientechLinkService processResponse -- 
2024-07-04 10:36:47.124500 --  --- $log->save() --  ---426 -- 
2024-07-04 10:36:47.126500 --  --- $log->save() --  ---427 -- 
2024-07-04 10:36:47.126700 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 10:36:47.126800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 10:36:47.136300 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 10:36:49.606500 -- AlientechLinkService __construct -- 
2024-07-04 10:36:49.606700 -- AlientechLinkService processClient -- 
2024-07-04 10:36:49.606700 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 10:36:49.606800 -- AlientechLinkService initAccessToken -- 
2024-07-04 10:36:49.607200 -- iR8V9-KAuaE -- 
2024-07-04 10:36:49.607400 -- AlientechProjectService __construct -- 
2024-07-04 10:36:49.607400 -- FileSlotService __construct -- 
2024-07-04 10:36:49.607500 -- FileSlotService __construct -- 
2024-07-04 10:36:49.607500 -- AsyncOperationService __construct -- 
2024-07-04 10:36:49.607700 -- ApiController actionKess3Decoded -- 
2024-07-04 10:36:49.611400 --  --- $log->save() --  ---428 -- 
2024-07-04 10:36:49.611500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 10:36:49.611600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 10:36:49.613700 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 10:36:49.613700 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 10:36:49.615200 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 10:36:49.615300 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-04 10:36:49.615300 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-04 10:36:49.617000 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 10:36:49.910600 -- AlientechLinkService __construct -- 
2024-07-04 10:36:49.910800 -- AlientechLinkService processClient -- 
2024-07-04 10:36:49.910800 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 10:36:49.911500 -- AlientechLinkService initAccessToken -- 
2024-07-04 10:36:49.914600 -- iR8V9-KAuaE -- 
2024-07-04 10:36:49.915300 -- AlientechProjectService __construct -- 
2024-07-04 10:36:49.916400 -- FileSlotService __construct -- 
2024-07-04 10:36:49.916800 -- FileSlotService __construct -- 
2024-07-04 10:36:49.916800 -- AsyncOperationService __construct -- 
2024-07-04 10:36:49.917400 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-04 10:36:49.918200 -- FileService downloadFile -- 
2024-07-04 10:36:49.918300 -- AlientechLinkService processRequest -- 
2024-07-04 10:36:49.918300 -- isAuth -- 
2024-07-04 10:36:49.918500 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:36:51.372200 -- AlientechLinkService processResponse -- 
2024-07-04 10:36:51.395600 --  --- $log->save() --  ---429 -- 
2024-07-04 10:36:51.402900 --  --- $log->save() --  ---430 -- 
2024-07-04 10:36:51.403800 -- FileService saveDecodedFile -- 
2024-07-04 10:36:51.422900 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 10:36:51.471700 -- FileService downloadFile -- 
2024-07-04 10:36:51.471800 -- AlientechLinkService processRequest -- 
2024-07-04 10:36:51.471900 -- isAuth -- 
2024-07-04 10:36:51.472000 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:36:52.265000 -- AlientechLinkService processResponse -- 
2024-07-04 10:36:52.267100 --  --- $log->save() --  ---431 -- 
2024-07-04 10:36:52.397900 --  --- $log->save() --  ---432 -- 
2024-07-04 10:36:52.407400 -- FileService saveDecodedFile -- 
2024-07-04 10:36:52.420600 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 10:44:08.010000 -- AlientechLinkService __construct -- 
2024-07-04 10:44:08.010300 -- AlientechLinkService processClient -- 
2024-07-04 10:44:08.010400 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 10:44:08.010900 -- AlientechLinkService initAccessToken -- 
2024-07-04 10:44:08.014200 -- iR8V9-KAuaE -- 
2024-07-04 10:44:08.015300 -- AlientechProjectService __construct -- 
2024-07-04 10:44:08.017700 -- FileSlotService __construct -- 
2024-07-04 10:44:08.018200 -- FileSlotService __construct -- 
2024-07-04 10:44:08.018400 -- AsyncOperationService __construct -- 
2024-07-04 10:44:08.019900 -- FileSlotService __construct -- 
2024-07-04 10:44:08.020100 -- Kess3Service __construct -- 
2024-07-04 10:44:08.020200 -- Kess3Service startDecoding -- 
2024-07-04 10:44:08.020200 -- AsyncOperationService startOperationDecode -- 
2024-07-04 10:44:08.020300 -- AlientechLinkService processRequest -- 
2024-07-04 10:44:08.020400 -- isAuth -- 
2024-07-04 10:44:08.020500 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:44:09.919400 -- AlientechLinkService processResponse -- 
2024-07-04 10:44:09.944000 --  --- $log->save() --  ---433 -- 
2024-07-04 10:44:09.946500 --  --- $log->save() --  ---434 -- 
2024-07-04 10:44:09.946800 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 10:44:09.946900 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 10:44:09.958000 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 10:44:11.993000 -- AlientechLinkService __construct -- 
2024-07-04 10:44:11.993300 -- AlientechLinkService processClient -- 
2024-07-04 10:44:11.993400 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 10:44:11.993600 -- AlientechLinkService initAccessToken -- 
2024-07-04 10:44:11.994200 -- iR8V9-KAuaE -- 
2024-07-04 10:44:11.994500 -- AlientechProjectService __construct -- 
2024-07-04 10:44:11.994700 -- FileSlotService __construct -- 
2024-07-04 10:44:11.994800 -- FileSlotService __construct -- 
2024-07-04 10:44:11.994800 -- AsyncOperationService __construct -- 
2024-07-04 10:44:11.995100 -- ApiController actionKess3Decoded -- 
2024-07-04 10:44:11.998100 --  --- $log->save() --  ---435 -- 
2024-07-04 10:44:11.998200 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 10:44:11.998300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 10:44:11.999700 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 10:44:11.999800 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 10:44:12.002500 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 10:44:12.002600 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-04 10:44:12.002700 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-04 10:44:12.006000 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 10:44:13.022200 -- AlientechLinkService __construct -- 
2024-07-04 10:44:13.022400 -- AlientechLinkService processClient -- 
2024-07-04 10:44:13.022400 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 10:44:13.022700 -- AlientechLinkService initAccessToken -- 
2024-07-04 10:44:13.025500 -- iR8V9-KAuaE -- 
2024-07-04 10:44:13.027000 -- AlientechProjectService __construct -- 
2024-07-04 10:44:13.029600 -- FileSlotService __construct -- 
2024-07-04 10:44:13.030100 -- FileSlotService __construct -- 
2024-07-04 10:44:13.030300 -- AsyncOperationService __construct -- 
2024-07-04 10:44:13.031000 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-04 10:44:13.032400 -- FileService downloadFile -- 
2024-07-04 10:44:13.032600 -- AlientechLinkService processRequest -- 
2024-07-04 10:44:13.032700 -- isAuth -- 
2024-07-04 10:44:13.032800 -- AlientechLinkService isAuth_success -- 
2024-07-04 10:44:13.655900 -- AlientechLinkService processResponse -- 
2024-07-04 10:44:13.671500 --  --- $log->save() --  ---436 -- 
2024-07-04 10:44:13.869600 --  --- $log->save() --  ---437 -- 
2024-07-04 10:44:13.881100 -- FileService saveDecodedFile -- 
2024-07-04 10:44:13.915000 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-04 11:37:50.725800 -- AlientechLinkService __construct -- 
2024-07-04 11:37:50.726600 -- AlientechLinkService processClient -- 
2024-07-04 11:37:50.726600 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 11:37:50.727100 -- AlientechLinkService initAccessToken -- 
2024-07-04 11:37:50.729700 -- iR8V9-KAuaE -- 
2024-07-04 11:37:50.730200 -- AlientechProjectService __construct -- 
2024-07-04 11:37:50.731000 -- FileSlotService __construct -- 
2024-07-04 11:37:50.731100 -- FileSlotService __construct -- 
2024-07-04 11:37:50.731200 -- AsyncOperationService __construct -- 
2024-07-04 11:37:50.731600 -- FileSlotService __construct -- 
2024-07-04 11:37:50.731700 -- Kess3Service __construct -- 
2024-07-04 11:37:50.731700 -- Kess3Service startEncoding -- 
2024-07-04 11:37:50.731700 -- AsyncOperationService startOperationEncode -- 
2024-07-04 11:37:50.749700 -- FileService uploadFiles -- 
2024-07-04 11:37:50.750000 -- FileSlotService reOpenSlot -- 
2024-07-04 11:37:50.750100 -- FileSlotService hasOpenFileSlots -- 
2024-07-04 11:37:50.750100 -- FileSlotService getSlots -- 
2024-07-04 11:37:50.750200 -- AlientechLinkService processRequest -- 
2024-07-04 11:37:50.750200 -- isAuth -- 
2024-07-04 11:37:50.750200 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:37:52.150300 -- AlientechLinkService processResponse -- 
2024-07-04 11:37:52.158900 --  --- $log->save() --  ---438 -- 
2024-07-04 11:37:52.164900 --  --- $log->save() --  ---439 -- 
2024-07-04 11:37:52.165500 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-04 11:37:52.165600 -- FileSlotService closeAllFileSlots -- 
2024-07-04 11:37:52.165700 -- FileSlotService getSlots -- 
2024-07-04 11:37:52.165700 -- AlientechLinkService processRequest -- 
2024-07-04 11:37:52.165700 -- isAuth -- 
2024-07-04 11:37:52.165800 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:37:52.311200 -- AlientechLinkService processResponse -- 
2024-07-04 11:37:52.315400 --  --- $log->save() --  ---440 -- 
2024-07-04 11:37:52.320300 --  --- $log->save() --  ---441 -- 
2024-07-04 11:37:52.321300 -- slot [dafb6f58-1409-4a1b-b4ba-2e3b3580c0f9] dateDiff {"y":0,"m":0,"d":0,"h":0,"i":53,"s":42,"f":0.451171,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 11:37:52.321400 -- FileSlotService closeSlot -- 
2024-07-04 11:37:52.321500 -- /api/kess3/file-slots/dafb6f58-1409-4a1b-b4ba-2e3b3580c0f9/close  -- 
2024-07-04 11:37:52.321600 -- AlientechLinkService processRequest -- 
2024-07-04 11:37:52.321600 -- isAuth -- 
2024-07-04 11:37:52.321700 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:37:52.610800 -- AlientechLinkService processResponse -- 
2024-07-04 11:37:52.614200 --  --- $log->save() --  ---442 -- 
2024-07-04 11:37:52.616600 --  --- $log->save() --  ---443 -- 
2024-07-04 11:37:52.616800 -- slot [dafb6f58-1409-4a1b-b4ba-2e3b3580c0f9] closeSlot null -- 
2024-07-04 11:37:52.617000 -- slot [027dfd0d-01c2-4e5d-a83b-09f653ec3a7c] dateDiff {"y":0,"m":0,"d":0,"h":1,"i":1,"s":5,"f":0.556915,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 11:37:52.617000 -- FileSlotService closeSlot -- 
2024-07-04 11:37:52.617100 -- /api/kess3/file-slots/027dfd0d-01c2-4e5d-a83b-09f653ec3a7c/close  -- 
2024-07-04 11:37:52.617200 -- AlientechLinkService processRequest -- 
2024-07-04 11:37:52.617200 -- isAuth -- 
2024-07-04 11:37:52.617300 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:37:52.794300 -- AlientechLinkService processResponse -- 
2024-07-04 11:37:52.795900 --  --- $log->save() --  ---444 -- 
2024-07-04 11:37:52.798400 --  --- $log->save() --  ---445 -- 
2024-07-04 11:37:52.798500 -- slot [027dfd0d-01c2-4e5d-a83b-09f653ec3a7c] closeSlot null -- 
2024-07-04 11:37:52.798600 -- slot [045e832f-3ede-4ed0-be1e-593f4ef5a91e] dateDiff {"y":0,"m":0,"d":0,"h":3,"i":24,"s":19,"f":0.115227,"invert":1,"days":0,"from_string":false} -- 
2024-07-04 11:37:52.798600 -- FileSlotService closeSlot -- 
2024-07-04 11:37:52.798600 -- /api/kess3/file-slots/045e832f-3ede-4ed0-be1e-593f4ef5a91e/close  -- 
2024-07-04 11:37:52.798700 -- AlientechLinkService processRequest -- 
2024-07-04 11:37:52.798700 -- isAuth -- 
2024-07-04 11:37:52.798700 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:37:52.972800 -- AlientechLinkService processResponse -- 
2024-07-04 11:37:52.974700 --  --- $log->save() --  ---446 -- 
2024-07-04 11:37:52.977200 --  --- $log->save() --  ---447 -- 
2024-07-04 11:37:52.977400 -- slot [045e832f-3ede-4ed0-be1e-593f4ef5a91e] closeSlot null -- 
2024-07-04 11:37:52.977500 -- closeAllSlots finish -- 
2024-07-04 11:37:52.978200 -- /api/kess3/file-slots/dafb6f58-1409-4a1b-b4ba-2e3b3580c0f9/reopen -- 
2024-07-04 11:37:52.978700 -- AlientechLinkService processRequest -- 
2024-07-04 11:37:52.979100 -- isAuth -- 
2024-07-04 11:37:52.980000 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:37:53.168300 -- AlientechLinkService processResponse -- 
2024-07-04 11:37:53.171400 --  --- $log->save() --  ---448 -- 
2024-07-04 11:37:53.174100 --  --- $log->save() --  ---449 -- 
2024-07-04 11:37:53.174300 -- AlientechLinkService processRequest -- 
2024-07-04 11:37:53.174500 -- isAuth -- 
2024-07-04 11:37:53.174700 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:37:53.841000 -- AlientechLinkService processResponse -- 
2024-07-04 11:37:53.843800 --  --- $log->save() --  ---450 -- 
2024-07-04 11:37:53.845700 --  --- $log->save() --  ---451 -- 
2024-07-04 11:37:53.858800 -- AlientechLinkService processRequest -- 
2024-07-04 11:37:53.859000 -- isAuth -- 
2024-07-04 11:37:53.859200 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:37:54.352900 -- AlientechLinkService processResponse -- 
2024-07-04 11:37:54.355000 --  --- $log->save() --  ---452 -- 
2024-07-04 11:37:54.356500 --  --- $log->save() --  ---453 -- 
2024-07-04 11:37:54.356600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 11:37:54.356600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 11:37:54.357800 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 11:37:57.462000 -- AlientechLinkService __construct -- 
2024-07-04 11:37:57.462200 -- AlientechLinkService processClient -- 
2024-07-04 11:37:57.462200 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 11:37:57.462300 -- AlientechLinkService initAccessToken -- 
2024-07-04 11:37:57.462700 -- iR8V9-KAuaE -- 
2024-07-04 11:37:57.462900 -- AlientechProjectService __construct -- 
2024-07-04 11:37:57.463100 -- FileSlotService __construct -- 
2024-07-04 11:37:57.463100 -- FileSlotService __construct -- 
2024-07-04 11:37:57.463200 -- AsyncOperationService __construct -- 
2024-07-04 11:37:57.463400 -- ApiController actionKess3Encoded -- 
2024-07-04 11:37:57.472000 --  --- $log->save() --  ---454 -- 
2024-07-04 11:37:57.472100 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 11:37:57.472100 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 11:37:57.474600 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 11:37:57.474600 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 11:37:57.476700 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 11:37:57.476800 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-04 11:37:57.476800 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-04 11:37:57.479300 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 11:37:58.928700 -- AlientechLinkService __construct -- 
2024-07-04 11:37:58.929100 -- AlientechLinkService processClient -- 
2024-07-04 11:37:58.929200 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 11:37:58.930100 -- AlientechLinkService initAccessToken -- 
2024-07-04 11:37:58.934800 -- iR8V9-KAuaE -- 
2024-07-04 11:37:58.935800 -- AlientechProjectService __construct -- 
2024-07-04 11:37:58.937600 -- FileSlotService __construct -- 
2024-07-04 11:37:58.937900 -- FileSlotService __construct -- 
2024-07-04 11:37:58.938000 -- AsyncOperationService __construct -- 
2024-07-04 11:37:58.938700 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-04 11:37:58.939400 -- FileService downloadFile -- 
2024-07-04 11:37:58.939500 -- AlientechLinkService processRequest -- 
2024-07-04 11:37:58.939600 -- isAuth -- 
2024-07-04 11:37:58.939600 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:37:59.353400 -- AlientechLinkService processResponse -- 
2024-07-04 11:37:59.377200 --  --- $log->save() --  ---455 -- 
2024-07-04 11:37:59.591300 --  --- $log->save() --  ---456 -- 
2024-07-04 11:37:59.629600 -- FileService saveEncodedFile -- 
2024-07-04 11:37:59.672100 -- FileService $initFile->file_history={"id":"7613","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-04 11:37:59.685600 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-04 11:37:59.723600 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-07-04 11:48:44.830300 -- AlientechLinkService __construct -- 
2024-07-04 11:48:44.830500 -- AlientechLinkService processClient -- 
2024-07-04 11:48:44.830600 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 11:48:44.831000 -- AlientechLinkService initAccessToken -- 
2024-07-04 11:48:44.834200 -- iR8V9-KAuaE -- 
2024-07-04 11:48:44.834800 -- AlientechProjectService __construct -- 
2024-07-04 11:48:44.835700 -- FileSlotService __construct -- 
2024-07-04 11:48:44.835900 -- FileSlotService __construct -- 
2024-07-04 11:48:44.836000 -- AsyncOperationService __construct -- 
2024-07-04 11:48:44.836500 -- FileSlotService __construct -- 
2024-07-04 11:48:44.836600 -- Kess3Service __construct -- 
2024-07-04 11:48:44.836600 -- Kess3Service startEncoding -- 
2024-07-04 11:48:44.836700 -- AsyncOperationService startOperationEncode -- 
2024-07-04 11:48:44.852900 -- FileService uploadFiles -- 
2024-07-04 11:48:44.853300 -- FileSlotService reOpenSlot -- 
2024-07-04 11:48:44.853300 -- FileSlotService hasOpenFileSlots -- 
2024-07-04 11:48:44.853400 -- FileSlotService getSlots -- 
2024-07-04 11:48:44.853400 -- AlientechLinkService processRequest -- 
2024-07-04 11:48:44.853500 -- isAuth -- 
2024-07-04 11:48:44.853500 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:48:46.192300 -- AlientechLinkService processResponse -- 
2024-07-04 11:48:46.202000 --  --- $log->save() --  ---457 -- 
2024-07-04 11:48:46.205200 --  --- $log->save() --  ---458 -- 
2024-07-04 11:48:46.205800 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-04 11:48:46.205900 -- /api/kess3/file-slots/3764cfed-35f6-4419-907e-9cb6e6675279/reopen -- 
2024-07-04 11:48:46.205900 -- AlientechLinkService processRequest -- 
2024-07-04 11:48:46.205900 -- isAuth -- 
2024-07-04 11:48:46.206000 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:48:46.452000 -- AlientechLinkService processResponse -- 
2024-07-04 11:48:46.454600 --  --- $log->save() --  ---459 -- 
2024-07-04 11:48:46.456900 --  --- $log->save() --  ---460 -- 
2024-07-04 11:48:46.457100 -- AlientechLinkService processRequest -- 
2024-07-04 11:48:46.457200 -- isAuth -- 
2024-07-04 11:48:46.457200 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:48:46.993300 -- AlientechLinkService processResponse -- 
2024-07-04 11:48:46.996400 --  --- $log->save() --  ---461 -- 
2024-07-04 11:48:46.998700 --  --- $log->save() --  ---462 -- 
2024-07-04 11:48:47.010700 -- AlientechLinkService processRequest -- 
2024-07-04 11:48:47.010900 -- isAuth -- 
2024-07-04 11:48:47.011000 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:48:47.985900 -- AlientechLinkService processResponse -- 
2024-07-04 11:48:47.988700 --  --- $log->save() --  ---463 -- 
2024-07-04 11:48:47.991400 --  --- $log->save() --  ---464 -- 
2024-07-04 11:48:47.991500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 11:48:47.991600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 11:48:47.992800 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 11:48:50.077900 -- AlientechLinkService __construct -- 
2024-07-04 11:48:50.078200 -- AlientechLinkService processClient -- 
2024-07-04 11:48:50.078200 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 11:48:50.078400 -- AlientechLinkService initAccessToken -- 
2024-07-04 11:48:50.079000 -- iR8V9-KAuaE -- 
2024-07-04 11:48:50.079300 -- AlientechProjectService __construct -- 
2024-07-04 11:48:50.079600 -- FileSlotService __construct -- 
2024-07-04 11:48:50.079900 -- FileSlotService __construct -- 
2024-07-04 11:48:50.079900 -- AsyncOperationService __construct -- 
2024-07-04 11:48:50.080400 -- ApiController actionKess3Encoded -- 
2024-07-04 11:48:50.083500 --  --- $log->save() --  ---465 -- 
2024-07-04 11:48:50.083700 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 11:48:50.083800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 11:48:50.085200 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 11:48:50.085300 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 11:48:50.088400 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 11:48:50.088600 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-04 11:48:50.088600 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-04 11:48:50.091400 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 11:48:50.366100 -- AlientechLinkService __construct -- 
2024-07-04 11:48:50.366300 -- AlientechLinkService processClient -- 
2024-07-04 11:48:50.366300 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 11:48:50.366600 -- AlientechLinkService initAccessToken -- 
2024-07-04 11:48:50.369100 -- iR8V9-KAuaE -- 
2024-07-04 11:48:50.370400 -- AlientechProjectService __construct -- 
2024-07-04 11:48:50.372500 -- FileSlotService __construct -- 
2024-07-04 11:48:50.372900 -- FileSlotService __construct -- 
2024-07-04 11:48:50.373000 -- AsyncOperationService __construct -- 
2024-07-04 11:48:50.373500 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-04 11:48:50.373900 -- FileService downloadFile -- 
2024-07-04 11:48:50.374000 -- AlientechLinkService processRequest -- 
2024-07-04 11:48:50.374100 -- isAuth -- 
2024-07-04 11:48:50.374100 -- AlientechLinkService isAuth_success -- 
2024-07-04 11:48:51.217500 -- AlientechLinkService processResponse -- 
2024-07-04 11:48:51.239400 --  --- $log->save() --  ---466 -- 
2024-07-04 11:48:51.339500 --  --- $log->save() --  ---467 -- 
2024-07-04 11:48:51.359300 -- FileService saveEncodedFile -- 
2024-07-04 11:48:51.386500 -- FileService $initFile->file_history={"id":"7610","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-04 11:48:51.407000 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-04 11:48:51.445300 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-07-04 12:20:40.343600 -- AlientechLinkService __construct -- 
2024-07-04 12:20:40.344000 -- AlientechLinkService processClient -- 
2024-07-04 12:20:40.344100 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 12:20:40.344700 -- AlientechLinkService initAccessToken -- 
2024-07-04 12:20:40.347400 -- iR8V9-KAuaE -- 
2024-07-04 12:20:40.348100 -- AlientechProjectService __construct -- 
2024-07-04 12:20:40.349200 -- FileSlotService __construct -- 
2024-07-04 12:20:40.349500 -- FileSlotService __construct -- 
2024-07-04 12:20:40.349600 -- AsyncOperationService __construct -- 
2024-07-04 12:20:40.350400 -- FileSlotService __construct -- 
2024-07-04 12:20:40.350500 -- Kess3Service __construct -- 
2024-07-04 12:20:40.350600 -- Kess3Service startEncoding -- 
2024-07-04 12:20:40.350700 -- AsyncOperationService startOperationEncode -- 
2024-07-04 12:20:40.372500 -- FileService uploadFiles -- 
2024-07-04 12:20:40.373000 -- FileSlotService reOpenSlot -- 
2024-07-04 12:20:40.373100 -- FileSlotService hasOpenFileSlots -- 
2024-07-04 12:20:40.373200 -- FileSlotService getSlots -- 
2024-07-04 12:20:40.373300 -- AlientechLinkService processRequest -- 
2024-07-04 12:20:40.373400 -- isAuth -- 
2024-07-04 12:20:40.373400 -- AlientechLinkService isAuth_success -- 
2024-07-04 12:20:41.098100 -- AlientechLinkService processResponse -- 
2024-07-04 12:20:41.107700 --  --- $log->save() --  ---468 -- 
2024-07-04 12:20:41.111700 --  --- $log->save() --  ---469 -- 
2024-07-04 12:20:41.112300 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-04 12:20:41.112400 -- /api/kess3/file-slots/027dfd0d-01c2-4e5d-a83b-09f653ec3a7c/reopen -- 
2024-07-04 12:20:41.112400 -- AlientechLinkService processRequest -- 
2024-07-04 12:20:41.112500 -- isAuth -- 
2024-07-04 12:20:41.112600 -- AlientechLinkService isAuth_success -- 
2024-07-04 12:20:41.336300 -- AlientechLinkService processResponse -- 
2024-07-04 12:20:41.338300 --  --- $log->save() --  ---470 -- 
2024-07-04 12:20:41.340600 --  --- $log->save() --  ---471 -- 
2024-07-04 12:20:41.340800 -- AlientechLinkService processRequest -- 
2024-07-04 12:20:41.341000 -- isAuth -- 
2024-07-04 12:20:41.341100 -- AlientechLinkService isAuth_success -- 
2024-07-04 12:20:42.278300 -- AlientechLinkService processResponse -- 
2024-07-04 12:20:42.281100 --  --- $log->save() --  ---472 -- 
2024-07-04 12:20:42.283300 --  --- $log->save() --  ---473 -- 
2024-07-04 12:20:42.294100 -- AlientechLinkService processRequest -- 
2024-07-04 12:20:42.294300 -- isAuth -- 
2024-07-04 12:20:42.294400 -- AlientechLinkService isAuth_success -- 
2024-07-04 12:20:43.362500 -- AlientechLinkService processResponse -- 
2024-07-04 12:20:43.365600 --  --- $log->save() --  ---474 -- 
2024-07-04 12:20:43.367600 --  --- $log->save() --  ---475 -- 
2024-07-04 12:20:43.367800 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 12:20:43.368600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 12:20:43.370600 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-04 12:20:59.302700 -- AlientechLinkService __construct -- 
2024-07-04 12:20:59.302900 -- AlientechLinkService processClient -- 
2024-07-04 12:20:59.303000 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 12:20:59.303200 -- AlientechLinkService initAccessToken -- 
2024-07-04 12:20:59.304000 -- iR8V9-KAuaE -- 
2024-07-04 12:20:59.304300 -- AlientechProjectService __construct -- 
2024-07-04 12:20:59.304600 -- FileSlotService __construct -- 
2024-07-04 12:20:59.304800 -- FileSlotService __construct -- 
2024-07-04 12:20:59.304900 -- AsyncOperationService __construct -- 
2024-07-04 12:20:59.305200 -- ApiController actionKess3Encoded -- 
2024-07-04 12:20:59.309000 --  --- $log->save() --  ---476 -- 
2024-07-04 12:20:59.309300 -- AsyncOperationService createOperationDtoByData -- 
2024-07-04 12:20:59.309400 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-04 12:20:59.311800 -- AsyncOperationService processCompletedOperation -- 
2024-07-04 12:20:59.312000 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-04 12:20:59.315400 -- AsyncOperationService finishCompletedOperation -- 
2024-07-04 12:20:59.315500 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-04 12:20:59.315600 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-04 12:20:59.318600 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-04 12:21:00.227200 -- AlientechLinkService __construct -- 
2024-07-04 12:21:00.227300 -- AlientechLinkService processClient -- 
2024-07-04 12:21:00.227400 -- AlientechLinkService is_null($this->client) -- 
2024-07-04 12:21:00.227700 -- AlientechLinkService initAccessToken -- 
2024-07-04 12:21:00.230400 -- iR8V9-KAuaE -- 
2024-07-04 12:21:00.231800 -- AlientechProjectService __construct -- 
2024-07-04 12:21:00.234700 -- FileSlotService __construct -- 
2024-07-04 12:21:00.235200 -- FileSlotService __construct -- 
2024-07-04 12:21:00.235400 -- AsyncOperationService __construct -- 
2024-07-04 12:21:00.236000 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-04 12:21:00.236400 -- FileService downloadFile -- 
2024-07-04 12:21:00.236500 -- AlientechLinkService processRequest -- 
2024-07-04 12:21:00.236600 -- isAuth -- 
2024-07-04 12:21:00.236600 -- AlientechLinkService isAuth_success -- 
2024-07-04 12:21:00.648900 -- AlientechLinkService processResponse -- 
2024-07-04 12:21:00.679800 --  --- $log->save() --  ---477 -- 
2024-07-04 12:21:00.777800 --  --- $log->save() --  ---478 -- 
2024-07-04 12:21:00.787400 -- FileService saveEncodedFile -- 
2024-07-04 12:21:00.808100 -- FileService $initFile->file_history={"id":"7612","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-04 12:21:00.819000 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-04 12:21:00.864400 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-07-05 07:28:43.599600 -- AlientechLinkService __construct -- 
2024-07-05 07:28:43.599800 -- AlientechLinkService processClient -- 
2024-07-05 07:28:43.599900 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 07:28:43.600200 -- AlientechLinkService initAccessToken -- 
2024-07-05 07:28:43.603100 -- iR8V9-KAuaE -- 
2024-07-05 07:28:43.603600 -- AlientechProjectService __construct -- 
2024-07-05 07:28:43.604500 -- FileSlotService __construct -- 
2024-07-05 07:28:43.604700 -- FileSlotService __construct -- 
2024-07-05 07:28:43.604800 -- AsyncOperationService __construct -- 
2024-07-05 07:28:43.605300 -- FileSlotService __construct -- 
2024-07-05 07:28:43.605400 -- Kess3Service __construct -- 
2024-07-05 07:28:43.605400 -- Kess3Service startDecoding -- 
2024-07-05 07:28:43.605500 -- AsyncOperationService startOperationDecode -- 
2024-07-05 07:28:43.605500 -- AlientechLinkService processRequest -- 
2024-07-05 07:28:43.605600 -- isAuth -- 
2024-07-05 07:28:43.605600 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:28:45.267900 -- AlientechLinkService processResponse -- 
2024-07-05 07:28:45.285900 --  --- $log->save() --  ---479 -- 
2024-07-05 07:28:45.286000 -- response_status=429 -- 
2024-07-05 07:28:45.286100 -- response=Http-Code: 429
Content-Type: text/plain; charset=utf-8
Retry-After: 0
Server: Microsoft-IIS/10.0
Strict-Transport-Security: max-age=2592000
Date: Fri, 05 Jul 2024 07:28:45 GMT
Connection: close

TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-07-05 07:28:45.286100 -- content=TOO_MANY_OPEN_KESS3_FILE_SLOTS -- 
2024-07-05 07:28:45.286200 -- FileSlotService closeAllFileSlots -- 
2024-07-05 07:28:45.286200 -- FileSlotService getSlots -- 
2024-07-05 07:28:45.286300 -- AlientechLinkService processRequest -- 
2024-07-05 07:28:45.286300 -- isAuth -- 
2024-07-05 07:28:45.286300 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:28:46.687100 -- AlientechLinkService processResponse -- 
2024-07-05 07:28:46.689800 --  --- $log->save() --  ---480 -- 
2024-07-05 07:28:46.697500 --  --- $log->save() --  ---481 -- 
2024-07-05 07:28:46.698600 -- slot [dafb6f58-1409-4a1b-b4ba-2e3b3580c0f9] dateDiff {"y":0,"m":0,"d":0,"h":20,"i":44,"s":36,"f":0.828474,"invert":1,"days":0,"from_string":false} -- 
2024-07-05 07:28:46.698800 -- FileSlotService closeSlot -- 
2024-07-05 07:28:46.698900 -- /api/kess3/file-slots/dafb6f58-1409-4a1b-b4ba-2e3b3580c0f9/close  -- 
2024-07-05 07:28:46.699000 -- AlientechLinkService processRequest -- 
2024-07-05 07:28:46.699200 -- isAuth -- 
2024-07-05 07:28:46.699300 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:28:46.891700 -- AlientechLinkService processResponse -- 
2024-07-05 07:28:46.894200 --  --- $log->save() --  ---482 -- 
2024-07-05 07:28:46.895900 --  --- $log->save() --  ---483 -- 
2024-07-05 07:28:46.896100 -- slot [dafb6f58-1409-4a1b-b4ba-2e3b3580c0f9] closeSlot null -- 
2024-07-05 07:28:46.896200 -- slot [027dfd0d-01c2-4e5d-a83b-09f653ec3a7c] dateDiff {"y":0,"m":0,"d":0,"h":20,"i":51,"s":59,"f":0.836149,"invert":1,"days":0,"from_string":false} -- 
2024-07-05 07:28:46.896200 -- FileSlotService closeSlot -- 
2024-07-05 07:28:46.896400 -- /api/kess3/file-slots/027dfd0d-01c2-4e5d-a83b-09f653ec3a7c/close  -- 
2024-07-05 07:28:46.896600 -- AlientechLinkService processRequest -- 
2024-07-05 07:28:46.896700 -- isAuth -- 
2024-07-05 07:28:46.896700 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:28:47.101900 -- AlientechLinkService processResponse -- 
2024-07-05 07:28:47.104900 --  --- $log->save() --  ---484 -- 
2024-07-05 07:28:47.107100 --  --- $log->save() --  ---485 -- 
2024-07-05 07:28:47.107300 -- slot [027dfd0d-01c2-4e5d-a83b-09f653ec3a7c] closeSlot null -- 
2024-07-05 07:28:47.107400 -- slot [3764cfed-35f6-4419-907e-9cb6e6675279] dateDiff {"y":0,"m":0,"d":0,"h":21,"i":45,"s":24,"f":0.183997,"invert":1,"days":0,"from_string":false} -- 
2024-07-05 07:28:47.107400 -- FileSlotService closeSlot -- 
2024-07-05 07:28:47.107500 -- /api/kess3/file-slots/3764cfed-35f6-4419-907e-9cb6e6675279/close  -- 
2024-07-05 07:28:47.107500 -- AlientechLinkService processRequest -- 
2024-07-05 07:28:47.107600 -- isAuth -- 
2024-07-05 07:28:47.107700 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:28:47.290200 -- AlientechLinkService processResponse -- 
2024-07-05 07:28:47.298200 --  --- $log->save() --  ---486 -- 
2024-07-05 07:28:47.301000 --  --- $log->save() --  ---487 -- 
2024-07-05 07:28:47.301200 -- slot [3764cfed-35f6-4419-907e-9cb6e6675279] closeSlot null -- 
2024-07-05 07:28:47.301400 -- closeAllSlots finish -- 
2024-07-05 07:28:47.301400 -- AsyncOperationService startOperationDecode -- 
2024-07-05 07:28:47.301600 -- AlientechLinkService processRequest -- 
2024-07-05 07:28:47.301800 -- isAuth -- 
2024-07-05 07:28:47.301900 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:28:47.594500 -- AlientechLinkService processResponse -- 
2024-07-05 07:28:47.597100 --  --- $log->save() --  ---488 -- 
2024-07-05 07:28:47.600200 --  --- $log->save() --  ---489 -- 
2024-07-05 07:28:47.600500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 07:28:47.600600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 07:28:47.617300 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-05 07:28:49.818100 -- AlientechLinkService __construct -- 
2024-07-05 07:28:49.818300 -- AlientechLinkService processClient -- 
2024-07-05 07:28:49.818400 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 07:28:49.818500 -- AlientechLinkService initAccessToken -- 
2024-07-05 07:28:49.819100 -- iR8V9-KAuaE -- 
2024-07-05 07:28:49.819400 -- AlientechProjectService __construct -- 
2024-07-05 07:28:49.819600 -- FileSlotService __construct -- 
2024-07-05 07:28:49.819700 -- FileSlotService __construct -- 
2024-07-05 07:28:49.819800 -- AsyncOperationService __construct -- 
2024-07-05 07:28:49.820000 -- ApiController actionKess3Decoded -- 
2024-07-05 07:28:49.825900 --  --- $log->save() --  ---490 -- 
2024-07-05 07:28:49.826100 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 07:28:49.826100 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 07:28:49.831200 -- AsyncOperationService processCompletedOperation -- 
2024-07-05 07:28:49.831400 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-05 07:28:49.834700 -- AsyncOperationService finishCompletedOperation -- 
2024-07-05 07:28:49.834800 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-05 07:28:49.834900 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-05 07:28:49.838900 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-05 07:28:50.058300 -- AlientechLinkService __construct -- 
2024-07-05 07:28:50.058500 -- AlientechLinkService processClient -- 
2024-07-05 07:28:50.058500 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 07:28:50.058900 -- AlientechLinkService initAccessToken -- 
2024-07-05 07:28:50.061300 -- iR8V9-KAuaE -- 
2024-07-05 07:28:50.062800 -- AlientechProjectService __construct -- 
2024-07-05 07:28:50.065600 -- FileSlotService __construct -- 
2024-07-05 07:28:50.066000 -- FileSlotService __construct -- 
2024-07-05 07:28:50.066200 -- AsyncOperationService __construct -- 
2024-07-05 07:28:50.066600 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-05 07:28:50.068100 -- FileService downloadFile -- 
2024-07-05 07:28:50.068200 -- AlientechLinkService processRequest -- 
2024-07-05 07:28:50.068300 -- isAuth -- 
2024-07-05 07:28:50.068400 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:28:50.350300 -- AlientechLinkService processResponse -- 
2024-07-05 07:28:50.378400 --  --- $log->save() --  ---491 -- 
2024-07-05 07:28:50.394200 --  --- $log->save() --  ---492 -- 
2024-07-05 07:28:50.395000 -- FileService saveDecodedFile -- 
2024-07-05 07:28:50.417300 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-05 07:28:50.478200 -- FileService downloadFile -- 
2024-07-05 07:28:50.478400 -- AlientechLinkService processRequest -- 
2024-07-05 07:28:50.478500 -- isAuth -- 
2024-07-05 07:28:50.478500 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:28:52.439400 -- AlientechLinkService processResponse -- 
2024-07-05 07:28:52.441700 --  --- $log->save() --  ---493 -- 
2024-07-05 07:28:52.778500 --  --- $log->save() --  ---494 -- 
2024-07-05 07:28:52.811200 -- FileService saveDecodedFile -- 
2024-07-05 07:28:52.834900 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-05 07:43:03.313900 -- AlientechLinkService __construct -- 
2024-07-05 07:43:03.314200 -- AlientechLinkService processClient -- 
2024-07-05 07:43:03.314300 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 07:43:03.314600 -- AlientechLinkService initAccessToken -- 
2024-07-05 07:43:03.317600 -- iR8V9-KAuaE -- 
2024-07-05 07:43:03.318100 -- AlientechProjectService __construct -- 
2024-07-05 07:43:03.319100 -- FileSlotService __construct -- 
2024-07-05 07:43:03.319300 -- FileSlotService __construct -- 
2024-07-05 07:43:03.319400 -- AsyncOperationService __construct -- 
2024-07-05 07:43:03.320000 -- FileSlotService __construct -- 
2024-07-05 07:43:03.320100 -- Kess3Service __construct -- 
2024-07-05 07:43:03.320200 -- Kess3Service startEncoding -- 
2024-07-05 07:43:03.320300 -- AsyncOperationService startOperationEncode -- 
2024-07-05 07:43:03.338500 -- FileService uploadFiles -- 
2024-07-05 07:43:03.338900 -- FileSlotService reOpenSlot -- 
2024-07-05 07:43:03.339100 -- FileSlotService hasOpenFileSlots -- 
2024-07-05 07:43:03.339100 -- FileSlotService getSlots -- 
2024-07-05 07:43:03.339200 -- AlientechLinkService processRequest -- 
2024-07-05 07:43:03.339300 -- isAuth -- 
2024-07-05 07:43:03.339400 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:43:03.634700 -- AlientechLinkService processResponse -- 
2024-07-05 07:43:03.644200 --  --- $log->save() --  ---495 -- 
2024-07-05 07:43:03.648100 --  --- $log->save() --  ---496 -- 
2024-07-05 07:43:03.648400 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-05 07:43:03.648500 -- /api/kess3/file-slots/fcae2f27-1b0d-465d-8102-1910b057bb98/reopen -- 
2024-07-05 07:43:03.648500 -- AlientechLinkService processRequest -- 
2024-07-05 07:43:03.648500 -- isAuth -- 
2024-07-05 07:43:03.648500 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:43:05.114800 -- AlientechLinkService processResponse -- 
2024-07-05 07:43:05.116600 --  --- $log->save() --  ---497 -- 
2024-07-05 07:43:05.117900 --  --- $log->save() --  ---498 -- 
2024-07-05 07:43:05.118000 -- AlientechLinkService processRequest -- 
2024-07-05 07:43:05.118000 -- isAuth -- 
2024-07-05 07:43:05.118100 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:43:06.183400 -- AlientechLinkService processResponse -- 
2024-07-05 07:43:06.185900 --  --- $log->save() --  ---499 -- 
2024-07-05 07:43:06.187900 --  --- $log->save() --  ---500 -- 
2024-07-05 07:43:06.196600 -- AlientechLinkService processRequest -- 
2024-07-05 07:43:06.196700 -- isAuth -- 
2024-07-05 07:43:06.196700 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:43:07.118100 -- AlientechLinkService processResponse -- 
2024-07-05 07:43:07.119900 --  --- $log->save() --  ---501 -- 
2024-07-05 07:43:07.121900 --  --- $log->save() --  ---502 -- 
2024-07-05 07:43:07.122100 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 07:43:07.122200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 07:43:07.123100 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-05 07:43:09.959600 -- AlientechLinkService __construct -- 
2024-07-05 07:43:09.959900 -- AlientechLinkService processClient -- 
2024-07-05 07:43:09.960000 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 07:43:09.960100 -- AlientechLinkService initAccessToken -- 
2024-07-05 07:43:09.960700 -- iR8V9-KAuaE -- 
2024-07-05 07:43:09.960900 -- AlientechProjectService __construct -- 
2024-07-05 07:43:09.961100 -- FileSlotService __construct -- 
2024-07-05 07:43:09.961200 -- FileSlotService __construct -- 
2024-07-05 07:43:09.961300 -- AsyncOperationService __construct -- 
2024-07-05 07:43:09.961600 -- ApiController actionKess3Encoded -- 
2024-07-05 07:43:09.965200 --  --- $log->save() --  ---503 -- 
2024-07-05 07:43:09.965400 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 07:43:09.965400 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 07:43:09.967000 -- AsyncOperationService processCompletedOperation -- 
2024-07-05 07:43:09.967200 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-05 07:43:09.969900 -- AsyncOperationService finishCompletedOperation -- 
2024-07-05 07:43:09.970000 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-05 07:43:09.970100 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-05 07:43:09.973000 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-05 07:43:11.702200 -- AlientechLinkService __construct -- 
2024-07-05 07:43:11.702300 -- AlientechLinkService processClient -- 
2024-07-05 07:43:11.702300 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 07:43:11.702700 -- AlientechLinkService initAccessToken -- 
2024-07-05 07:43:11.705800 -- iR8V9-KAuaE -- 
2024-07-05 07:43:11.706900 -- AlientechProjectService __construct -- 
2024-07-05 07:43:11.709000 -- FileSlotService __construct -- 
2024-07-05 07:43:11.709300 -- FileSlotService __construct -- 
2024-07-05 07:43:11.709400 -- AsyncOperationService __construct -- 
2024-07-05 07:43:11.709800 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-05 07:43:11.710000 -- FileService downloadFile -- 
2024-07-05 07:43:11.710100 -- AlientechLinkService processRequest -- 
2024-07-05 07:43:11.710200 -- isAuth -- 
2024-07-05 07:43:11.710200 -- AlientechLinkService isAuth_success -- 
2024-07-05 07:43:12.457800 -- AlientechLinkService processResponse -- 
2024-07-05 07:43:12.481100 --  --- $log->save() --  ---504 -- 
2024-07-05 07:43:12.575800 --  --- $log->save() --  ---505 -- 
2024-07-05 07:43:12.592700 -- FileService saveEncodedFile -- 
2024-07-05 07:43:12.624000 -- FileService $initFile->file_history={"id":"7615","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-05 07:43:12.648500 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-05 08:01:42.963600 -- AlientechLinkService __construct -- 
2024-07-05 08:01:42.963800 -- AlientechLinkService processClient -- 
2024-07-05 08:01:42.963900 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 08:01:42.964200 -- AlientechLinkService initAccessToken -- 
2024-07-05 08:01:42.966500 -- iR8V9-KAuaE -- 
2024-07-05 08:01:42.967200 -- AlientechProjectService __construct -- 
2024-07-05 08:01:42.968400 -- FileSlotService __construct -- 
2024-07-05 08:01:42.968600 -- FileSlotService __construct -- 
2024-07-05 08:01:42.968700 -- AsyncOperationService __construct -- 
2024-07-05 08:01:42.969300 -- FileSlotService __construct -- 
2024-07-05 08:01:42.969400 -- Kess3Service __construct -- 
2024-07-05 08:01:42.969400 -- Kess3Service startDecoding -- 
2024-07-05 08:01:42.969400 -- AsyncOperationService startOperationDecode -- 
2024-07-05 08:01:42.969500 -- AlientechLinkService processRequest -- 
2024-07-05 08:01:42.969500 -- isAuth -- 
2024-07-05 08:01:42.969600 -- AlientechLinkService isAuth_success -- 
2024-07-05 08:01:44.485700 -- AlientechLinkService processResponse -- 
2024-07-05 08:01:44.505100 --  --- $log->save() --  ---506 -- 
2024-07-05 08:01:44.507000 --  --- $log->save() --  ---507 -- 
2024-07-05 08:01:44.507200 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 08:01:44.507300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 08:01:44.514100 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-05 08:01:46.748100 -- AlientechLinkService __construct -- 
2024-07-05 08:01:46.748400 -- AlientechLinkService processClient -- 
2024-07-05 08:01:46.748400 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 08:01:46.748500 -- AlientechLinkService initAccessToken -- 
2024-07-05 08:01:46.749000 -- iR8V9-KAuaE -- 
2024-07-05 08:01:46.749200 -- AlientechProjectService __construct -- 
2024-07-05 08:01:46.749400 -- FileSlotService __construct -- 
2024-07-05 08:01:46.749400 -- FileSlotService __construct -- 
2024-07-05 08:01:46.749500 -- AsyncOperationService __construct -- 
2024-07-05 08:01:46.749600 -- ApiController actionKess3Decoded -- 
2024-07-05 08:01:46.751900 --  --- $log->save() --  ---508 -- 
2024-07-05 08:01:46.752000 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 08:01:46.752100 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 08:01:46.753100 -- AsyncOperationService processCompletedOperation -- 
2024-07-05 08:01:46.753100 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-05 08:01:46.758900 -- AsyncOperationService finishCompletedOperation -- 
2024-07-05 08:01:46.759000 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-05 08:01:46.759200 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-05 08:01:46.763200 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-05 08:01:48.181900 -- AlientechLinkService __construct -- 
2024-07-05 08:01:48.182000 -- AlientechLinkService processClient -- 
2024-07-05 08:01:48.182100 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 08:01:48.182400 -- AlientechLinkService initAccessToken -- 
2024-07-05 08:01:48.185100 -- iR8V9-KAuaE -- 
2024-07-05 08:01:48.186500 -- AlientechProjectService __construct -- 
2024-07-05 08:01:48.188800 -- FileSlotService __construct -- 
2024-07-05 08:01:48.189200 -- FileSlotService __construct -- 
2024-07-05 08:01:48.189300 -- AsyncOperationService __construct -- 
2024-07-05 08:01:48.189900 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-05 08:01:48.191100 -- FileService downloadFile -- 
2024-07-05 08:01:48.191300 -- AlientechLinkService processRequest -- 
2024-07-05 08:01:48.191400 -- isAuth -- 
2024-07-05 08:01:48.191400 -- AlientechLinkService isAuth_success -- 
2024-07-05 08:01:50.263100 -- AlientechLinkService processResponse -- 
2024-07-05 08:01:50.281100 --  --- $log->save() --  ---509 -- 
2024-07-05 08:01:50.400900 --  --- $log->save() --  ---510 -- 
2024-07-05 08:01:50.417100 -- FileService saveDecodedFile -- 
2024-07-05 08:01:50.449700 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-05 09:17:59.337700 -- AlientechLinkService __construct -- 
2024-07-05 09:17:59.337900 -- AlientechLinkService processClient -- 
2024-07-05 09:17:59.337900 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 09:17:59.338200 -- AlientechLinkService initAccessToken -- 
2024-07-05 09:17:59.341300 -- iR8V9-KAuaE -- 
2024-07-05 09:17:59.342800 -- AlientechProjectService __construct -- 
2024-07-05 09:17:59.345400 -- FileSlotService __construct -- 
2024-07-05 09:17:59.345900 -- FileSlotService __construct -- 
2024-07-05 09:17:59.346000 -- AsyncOperationService __construct -- 
2024-07-05 09:17:59.347200 -- FileSlotService __construct -- 
2024-07-05 09:17:59.347300 -- Kess3Service __construct -- 
2024-07-05 09:17:59.347400 -- Kess3Service startEncoding -- 
2024-07-05 09:17:59.347500 -- AsyncOperationService startOperationEncode -- 
2024-07-05 09:17:59.378800 -- FileService uploadFiles -- 
2024-07-05 09:17:59.379500 -- FileSlotService reOpenSlot -- 
2024-07-05 09:17:59.379600 -- FileSlotService hasOpenFileSlots -- 
2024-07-05 09:17:59.379700 -- FileSlotService getSlots -- 
2024-07-05 09:17:59.379700 -- AlientechLinkService processRequest -- 
2024-07-05 09:17:59.379800 -- isAuth -- 
2024-07-05 09:17:59.379800 -- AlientechLinkService isAuth_success -- 
2024-07-05 09:18:00.008400 -- AlientechLinkService processResponse -- 
2024-07-05 09:18:00.017100 --  --- $log->save() --  ---511 -- 
2024-07-05 09:18:00.025200 --  --- $log->save() --  ---512 -- 
2024-07-05 09:18:00.025600 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-05 09:18:00.025600 -- /api/kess3/file-slots/32c4cdee-8675-4723-8678-3a6c6c7c3645/reopen -- 
2024-07-05 09:18:00.025700 -- AlientechLinkService processRequest -- 
2024-07-05 09:18:00.025700 -- isAuth -- 
2024-07-05 09:18:00.025800 -- AlientechLinkService isAuth_success -- 
2024-07-05 09:18:01.573400 -- AlientechLinkService processResponse -- 
2024-07-05 09:18:01.575600 --  --- $log->save() --  ---513 -- 
2024-07-05 09:18:01.577200 --  --- $log->save() --  ---514 -- 
2024-07-05 09:18:01.577300 -- AlientechLinkService processRequest -- 
2024-07-05 09:18:01.577300 -- isAuth -- 
2024-07-05 09:18:01.577400 -- AlientechLinkService isAuth_success -- 
2024-07-05 09:18:02.318800 -- AlientechLinkService processResponse -- 
2024-07-05 09:18:02.321000 --  --- $log->save() --  ---515 -- 
2024-07-05 09:18:02.322900 --  --- $log->save() --  ---516 -- 
2024-07-05 09:18:02.332000 -- AlientechLinkService processRequest -- 
2024-07-05 09:18:02.332100 -- isAuth -- 
2024-07-05 09:18:02.332200 -- AlientechLinkService isAuth_success -- 
2024-07-05 09:18:02.976900 -- AlientechLinkService processResponse -- 
2024-07-05 09:18:02.979600 --  --- $log->save() --  ---517 -- 
2024-07-05 09:18:02.981800 --  --- $log->save() --  ---518 -- 
2024-07-05 09:18:02.982000 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 09:18:02.982100 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 09:18:02.983400 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-05 09:18:05.303000 -- AlientechLinkService __construct -- 
2024-07-05 09:18:05.303400 -- AlientechLinkService processClient -- 
2024-07-05 09:18:05.303500 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 09:18:05.303700 -- AlientechLinkService initAccessToken -- 
2024-07-05 09:18:05.304600 -- iR8V9-KAuaE -- 
2024-07-05 09:18:05.304900 -- AlientechProjectService __construct -- 
2024-07-05 09:18:05.305200 -- FileSlotService __construct -- 
2024-07-05 09:18:05.305400 -- FileSlotService __construct -- 
2024-07-05 09:18:05.305600 -- AsyncOperationService __construct -- 
2024-07-05 09:18:05.305900 -- ApiController actionKess3Encoded -- 
2024-07-05 09:18:05.312000 --  --- $log->save() --  ---519 -- 
2024-07-05 09:18:05.312200 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 09:18:05.312200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 09:18:05.316600 -- AsyncOperationService processCompletedOperation -- 
2024-07-05 09:18:05.316800 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-05 09:18:05.320100 -- AsyncOperationService finishCompletedOperation -- 
2024-07-05 09:18:05.320200 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-05 09:18:05.320300 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-05 09:18:05.323600 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-05 09:18:06.750900 -- AlientechLinkService __construct -- 
2024-07-05 09:18:06.751500 -- AlientechLinkService processClient -- 
2024-07-05 09:18:06.751700 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 09:18:06.752300 -- AlientechLinkService initAccessToken -- 
2024-07-05 09:18:06.754700 -- iR8V9-KAuaE -- 
2024-07-05 09:18:06.755400 -- AlientechProjectService __construct -- 
2024-07-05 09:18:06.756400 -- FileSlotService __construct -- 
2024-07-05 09:18:06.756700 -- FileSlotService __construct -- 
2024-07-05 09:18:06.756900 -- AsyncOperationService __construct -- 
2024-07-05 09:18:06.757300 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-05 09:18:06.757600 -- FileService downloadFile -- 
2024-07-05 09:18:06.757900 -- AlientechLinkService processRequest -- 
2024-07-05 09:18:06.758100 -- isAuth -- 
2024-07-05 09:18:06.758300 -- AlientechLinkService isAuth_success -- 
2024-07-05 09:18:07.503200 -- AlientechLinkService processResponse -- 
2024-07-05 09:18:07.524500 --  --- $log->save() --  ---520 -- 
2024-07-05 09:18:07.659600 --  --- $log->save() --  ---521 -- 
2024-07-05 09:18:07.689200 -- FileService saveEncodedFile -- 
2024-07-05 09:18:07.720400 -- FileService $initFile->file_history={"id":"7617","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-05 09:18:07.733800 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-05 10:10:20.721900 -- AlientechLinkService __construct -- 
2024-07-05 10:10:20.722100 -- AlientechLinkService processClient -- 
2024-07-05 10:10:20.722200 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 10:10:20.722600 -- AlientechLinkService initAccessToken -- 
2024-07-05 10:10:20.725600 -- iR8V9-KAuaE -- 
2024-07-05 10:10:20.726400 -- AlientechProjectService __construct -- 
2024-07-05 10:10:20.728600 -- FileSlotService __construct -- 
2024-07-05 10:10:20.728800 -- FileSlotService __construct -- 
2024-07-05 10:10:20.728900 -- AsyncOperationService __construct -- 
2024-07-05 10:10:20.729600 -- FileSlotService __construct -- 
2024-07-05 10:10:20.729700 -- Kess3Service __construct -- 
2024-07-05 10:10:20.729800 -- Kess3Service startDecoding -- 
2024-07-05 10:10:20.729800 -- AsyncOperationService startOperationDecode -- 
2024-07-05 10:10:20.729900 -- AlientechLinkService processRequest -- 
2024-07-05 10:10:20.729900 -- isAuth -- 
2024-07-05 10:10:20.729900 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:10:21.614000 -- AlientechLinkService processResponse -- 
2024-07-05 10:10:21.626300 --  --- $log->save() --  ---522 -- 
2024-07-05 10:10:21.627800 --  --- $log->save() --  ---523 -- 
2024-07-05 10:10:21.627900 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 10:10:21.628000 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 10:10:21.634400 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-05 10:10:23.472400 -- AlientechLinkService __construct -- 
2024-07-05 10:10:23.472700 -- AlientechLinkService processClient -- 
2024-07-05 10:10:23.472800 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 10:10:23.473000 -- AlientechLinkService initAccessToken -- 
2024-07-05 10:10:23.473800 -- iR8V9-KAuaE -- 
2024-07-05 10:10:23.474000 -- AlientechProjectService __construct -- 
2024-07-05 10:10:23.474200 -- FileSlotService __construct -- 
2024-07-05 10:10:23.474200 -- FileSlotService __construct -- 
2024-07-05 10:10:23.474300 -- AsyncOperationService __construct -- 
2024-07-05 10:10:23.474400 -- ApiController actionKess3Decoded -- 
2024-07-05 10:10:23.481300 --  --- $log->save() --  ---524 -- 
2024-07-05 10:10:23.481400 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 10:10:23.481600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 10:10:23.483300 -- AsyncOperationService processCompletedOperation -- 
2024-07-05 10:10:23.483300 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-05 10:10:23.486300 -- AsyncOperationService finishCompletedOperation -- 
2024-07-05 10:10:23.486300 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-05 10:10:23.486400 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-05 10:10:23.489100 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-05 10:10:23.618100 -- AlientechLinkService __construct -- 
2024-07-05 10:10:23.618200 -- AlientechLinkService processClient -- 
2024-07-05 10:10:23.618300 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 10:10:23.618800 -- AlientechLinkService initAccessToken -- 
2024-07-05 10:10:23.621800 -- iR8V9-KAuaE -- 
2024-07-05 10:10:23.623300 -- AlientechProjectService __construct -- 
2024-07-05 10:10:23.626300 -- FileSlotService __construct -- 
2024-07-05 10:10:23.626700 -- FileSlotService __construct -- 
2024-07-05 10:10:23.626900 -- AsyncOperationService __construct -- 
2024-07-05 10:10:23.627300 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-05 10:10:23.628600 -- FileService downloadFile -- 
2024-07-05 10:10:23.628800 -- AlientechLinkService processRequest -- 
2024-07-05 10:10:23.628900 -- isAuth -- 
2024-07-05 10:10:23.629000 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:10:25.665700 -- AlientechLinkService processResponse -- 
2024-07-05 10:10:25.695300 --  --- $log->save() --  ---525 -- 
2024-07-05 10:10:25.816100 --  --- $log->save() --  ---526 -- 
2024-07-05 10:10:25.823000 -- FileService saveDecodedFile -- 
2024-07-05 10:10:25.844400 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-05 10:36:16.653300 -- AlientechLinkService __construct -- 
2024-07-05 10:36:16.653800 -- AlientechLinkService processClient -- 
2024-07-05 10:36:16.654000 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 10:36:16.654500 -- AlientechLinkService initAccessToken -- 
2024-07-05 10:36:16.656900 -- iR8V9-KAuaE -- 
2024-07-05 10:36:16.657600 -- AlientechProjectService __construct -- 
2024-07-05 10:36:16.658700 -- FileSlotService __construct -- 
2024-07-05 10:36:16.659000 -- FileSlotService __construct -- 
2024-07-05 10:36:16.659200 -- AsyncOperationService __construct -- 
2024-07-05 10:36:16.660000 -- FileSlotService __construct -- 
2024-07-05 10:36:16.660200 -- Kess3Service __construct -- 
2024-07-05 10:36:16.660400 -- Kess3Service startEncoding -- 
2024-07-05 10:36:16.660600 -- AsyncOperationService startOperationEncode -- 
2024-07-05 10:36:16.676300 -- FileService uploadFiles -- 
2024-07-05 10:36:16.677000 -- FileSlotService reOpenSlot -- 
2024-07-05 10:36:16.677300 -- FileSlotService hasOpenFileSlots -- 
2024-07-05 10:36:16.677500 -- FileSlotService getSlots -- 
2024-07-05 10:36:16.677700 -- AlientechLinkService processRequest -- 
2024-07-05 10:36:16.677900 -- isAuth -- 
2024-07-05 10:36:16.678100 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:36:17.265100 -- AlientechLinkService processResponse -- 
2024-07-05 10:36:17.276700 --  --- $log->save() --  ---527 -- 
2024-07-05 10:36:17.281800 --  --- $log->save() --  ---528 -- 
2024-07-05 10:36:17.282300 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-05 10:36:17.282400 -- FileSlotService closeAllFileSlots -- 
2024-07-05 10:36:17.282400 -- FileSlotService getSlots -- 
2024-07-05 10:36:17.282500 -- AlientechLinkService processRequest -- 
2024-07-05 10:36:17.282500 -- isAuth -- 
2024-07-05 10:36:17.282600 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:36:18.611600 -- AlientechLinkService processResponse -- 
2024-07-05 10:36:18.613400 --  --- $log->save() --  ---529 -- 
2024-07-05 10:36:18.621000 --  --- $log->save() --  ---530 -- 
2024-07-05 10:36:18.622800 -- slot [774f4ea7-8c0e-455a-be93-709cd975f685] dateDiff {"y":0,"m":0,"d":0,"h":0,"i":25,"s":57,"f":0.055987,"invert":1,"days":0,"from_string":false} -- 
2024-07-05 10:36:18.623000 -- FileSlotService closeSlot -- 
2024-07-05 10:36:18.623200 -- /api/kess3/file-slots/774f4ea7-8c0e-455a-be93-709cd975f685/close  -- 
2024-07-05 10:36:18.623300 -- AlientechLinkService processRequest -- 
2024-07-05 10:36:18.623400 -- isAuth -- 
2024-07-05 10:36:18.623500 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:36:18.870100 -- AlientechLinkService processResponse -- 
2024-07-05 10:36:18.871600 --  --- $log->save() --  ---531 -- 
2024-07-05 10:36:18.872800 --  --- $log->save() --  ---532 -- 
2024-07-05 10:36:18.873000 -- slot [774f4ea7-8c0e-455a-be93-709cd975f685] closeSlot null -- 
2024-07-05 10:36:18.873100 -- slot [32c4cdee-8675-4723-8678-3a6c6c7c3645] dateDiff {"y":0,"m":0,"d":0,"h":2,"i":34,"s":34,"f":0.433093,"invert":1,"days":0,"from_string":false} -- 
2024-07-05 10:36:18.873400 -- FileSlotService closeSlot -- 
2024-07-05 10:36:18.873600 -- /api/kess3/file-slots/32c4cdee-8675-4723-8678-3a6c6c7c3645/close  -- 
2024-07-05 10:36:18.873800 -- AlientechLinkService processRequest -- 
2024-07-05 10:36:18.873900 -- isAuth -- 
2024-07-05 10:36:18.874000 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:36:19.135200 -- AlientechLinkService processResponse -- 
2024-07-05 10:36:19.138000 --  --- $log->save() --  ---533 -- 
2024-07-05 10:36:19.140100 --  --- $log->save() --  ---534 -- 
2024-07-05 10:36:19.140200 -- slot [32c4cdee-8675-4723-8678-3a6c6c7c3645] closeSlot null -- 
2024-07-05 10:36:19.140400 -- slot [fcae2f27-1b0d-465d-8102-1910b057bb98] dateDiff {"y":0,"m":0,"d":0,"h":3,"i":7,"s":31,"f":0.59366,"invert":1,"days":0,"from_string":false} -- 
2024-07-05 10:36:19.140600 -- FileSlotService closeSlot -- 
2024-07-05 10:36:19.140700 -- /api/kess3/file-slots/fcae2f27-1b0d-465d-8102-1910b057bb98/close  -- 
2024-07-05 10:36:19.140900 -- AlientechLinkService processRequest -- 
2024-07-05 10:36:19.141000 -- isAuth -- 
2024-07-05 10:36:19.141100 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:36:19.418000 -- AlientechLinkService processResponse -- 
2024-07-05 10:36:19.420000 --  --- $log->save() --  ---535 -- 
2024-07-05 10:36:19.421900 --  --- $log->save() --  ---536 -- 
2024-07-05 10:36:19.422100 -- slot [fcae2f27-1b0d-465d-8102-1910b057bb98] closeSlot null -- 
2024-07-05 10:36:19.422200 -- closeAllSlots finish -- 
2024-07-05 10:36:19.422300 -- /api/kess3/file-slots/774f4ea7-8c0e-455a-be93-709cd975f685/reopen -- 
2024-07-05 10:36:19.422400 -- AlientechLinkService processRequest -- 
2024-07-05 10:36:19.422600 -- isAuth -- 
2024-07-05 10:36:19.422700 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:36:19.701100 -- AlientechLinkService processResponse -- 
2024-07-05 10:36:19.703600 --  --- $log->save() --  ---537 -- 
2024-07-05 10:36:19.705800 --  --- $log->save() --  ---538 -- 
2024-07-05 10:36:19.706000 -- AlientechLinkService processRequest -- 
2024-07-05 10:36:19.706100 -- isAuth -- 
2024-07-05 10:36:19.706200 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:36:20.167000 -- AlientechLinkService processResponse -- 
2024-07-05 10:36:20.169600 --  --- $log->save() --  ---539 -- 
2024-07-05 10:36:20.171600 --  --- $log->save() --  ---540 -- 
2024-07-05 10:36:20.182600 -- AlientechLinkService processRequest -- 
2024-07-05 10:36:20.182800 -- isAuth -- 
2024-07-05 10:36:20.182900 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:36:20.755000 -- AlientechLinkService processResponse -- 
2024-07-05 10:36:20.758100 --  --- $log->save() --  ---541 -- 
2024-07-05 10:36:20.760600 --  --- $log->save() --  ---542 -- 
2024-07-05 10:36:20.760900 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 10:36:20.761000 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 10:36:20.762600 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-05 10:36:22.863200 -- AlientechLinkService __construct -- 
2024-07-05 10:36:22.863500 -- AlientechLinkService processClient -- 
2024-07-05 10:36:22.863600 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 10:36:22.863700 -- AlientechLinkService initAccessToken -- 
2024-07-05 10:36:22.865200 -- iR8V9-KAuaE -- 
2024-07-05 10:36:22.865400 -- AlientechProjectService __construct -- 
2024-07-05 10:36:22.865500 -- FileSlotService __construct -- 
2024-07-05 10:36:22.865600 -- FileSlotService __construct -- 
2024-07-05 10:36:22.865700 -- AsyncOperationService __construct -- 
2024-07-05 10:36:22.865800 -- ApiController actionKess3Encoded -- 
2024-07-05 10:36:22.871900 --  --- $log->save() --  ---543 -- 
2024-07-05 10:36:22.872100 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 10:36:22.872200 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 10:36:22.877500 -- AsyncOperationService processCompletedOperation -- 
2024-07-05 10:36:22.877600 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-05 10:36:22.881300 -- AsyncOperationService finishCompletedOperation -- 
2024-07-05 10:36:22.881400 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-05 10:36:22.881500 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-05 10:36:22.885700 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-05 10:36:23.825600 -- AlientechLinkService __construct -- 
2024-07-05 10:36:23.825800 -- AlientechLinkService processClient -- 
2024-07-05 10:36:23.825800 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 10:36:23.826200 -- AlientechLinkService initAccessToken -- 
2024-07-05 10:36:23.828600 -- iR8V9-KAuaE -- 
2024-07-05 10:36:23.830100 -- AlientechProjectService __construct -- 
2024-07-05 10:36:23.832200 -- FileSlotService __construct -- 
2024-07-05 10:36:23.832600 -- FileSlotService __construct -- 
2024-07-05 10:36:23.832700 -- AsyncOperationService __construct -- 
2024-07-05 10:36:23.833100 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-05 10:36:23.833400 -- FileService downloadFile -- 
2024-07-05 10:36:23.833600 -- AlientechLinkService processRequest -- 
2024-07-05 10:36:23.833600 -- isAuth -- 
2024-07-05 10:36:23.833700 -- AlientechLinkService isAuth_success -- 
2024-07-05 10:36:25.130900 -- AlientechLinkService processResponse -- 
2024-07-05 10:36:25.155100 --  --- $log->save() --  ---544 -- 
2024-07-05 10:36:25.291800 --  --- $log->save() --  ---545 -- 
2024-07-05 10:36:25.318200 -- FileService saveEncodedFile -- 
2024-07-05 10:36:25.347100 -- FileService $initFile->file_history={"id":"7620","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-05 10:36:25.366500 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-05 11:18:50.964800 -- AlientechLinkService __construct -- 
2024-07-05 11:18:50.965000 -- AlientechLinkService processClient -- 
2024-07-05 11:18:50.965100 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 11:18:50.965400 -- AlientechLinkService initAccessToken -- 
2024-07-05 11:18:50.968200 -- iR8V9-KAuaE -- 
2024-07-05 11:18:50.968900 -- AlientechProjectService __construct -- 
2024-07-05 11:18:50.969900 -- FileSlotService __construct -- 
2024-07-05 11:18:50.970100 -- FileSlotService __construct -- 
2024-07-05 11:18:50.970100 -- AsyncOperationService __construct -- 
2024-07-05 11:18:50.970800 -- FileSlotService __construct -- 
2024-07-05 11:18:50.970800 -- Kess3Service __construct -- 
2024-07-05 11:18:50.970900 -- Kess3Service startDecoding -- 
2024-07-05 11:18:50.970900 -- AsyncOperationService startOperationDecode -- 
2024-07-05 11:18:50.970900 -- AlientechLinkService processRequest -- 
2024-07-05 11:18:50.971000 -- isAuth -- 
2024-07-05 11:18:50.971000 -- AlientechLinkService isAuth_success -- 
2024-07-05 11:18:52.538800 -- AlientechLinkService processResponse -- 
2024-07-05 11:18:52.550600 --  --- $log->save() --  ---546 -- 
2024-07-05 11:18:52.552100 --  --- $log->save() --  ---547 -- 
2024-07-05 11:18:52.552300 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 11:18:52.552300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 11:18:52.557200 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-05 11:18:54.687200 -- AlientechLinkService __construct -- 
2024-07-05 11:18:54.687300 -- AlientechLinkService processClient -- 
2024-07-05 11:18:54.687400 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 11:18:54.687500 -- AlientechLinkService initAccessToken -- 
2024-07-05 11:18:54.688500 -- iR8V9-KAuaE -- 
2024-07-05 11:18:54.688600 -- AlientechProjectService __construct -- 
2024-07-05 11:18:54.688700 -- FileSlotService __construct -- 
2024-07-05 11:18:54.688800 -- FileSlotService __construct -- 
2024-07-05 11:18:54.688800 -- AsyncOperationService __construct -- 
2024-07-05 11:18:54.688900 -- ApiController actionKess3Decoded -- 
2024-07-05 11:18:54.691600 --  --- $log->save() --  ---548 -- 
2024-07-05 11:18:54.691800 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 11:18:54.691800 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 11:18:54.692800 -- AsyncOperationService processCompletedOperation -- 
2024-07-05 11:18:54.692900 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-05 11:18:54.695200 -- AsyncOperationService finishCompletedOperation -- 
2024-07-05 11:18:54.695400 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-05 11:18:54.695500 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-05 11:18:54.698700 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-05 11:18:56.051500 -- AlientechLinkService __construct -- 
2024-07-05 11:18:56.052000 -- AlientechLinkService processClient -- 
2024-07-05 11:18:56.052300 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 11:18:56.052900 -- AlientechLinkService initAccessToken -- 
2024-07-05 11:18:56.055500 -- iR8V9-KAuaE -- 
2024-07-05 11:18:56.056400 -- AlientechProjectService __construct -- 
2024-07-05 11:18:56.057700 -- FileSlotService __construct -- 
2024-07-05 11:18:56.058200 -- FileSlotService __construct -- 
2024-07-05 11:18:56.058500 -- AsyncOperationService __construct -- 
2024-07-05 11:18:56.059000 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-05 11:18:56.059800 -- FileService downloadFile -- 
2024-07-05 11:18:56.060100 -- AlientechLinkService processRequest -- 
2024-07-05 11:18:56.060400 -- isAuth -- 
2024-07-05 11:18:56.060700 -- AlientechLinkService isAuth_success -- 
2024-07-05 11:18:56.755700 -- AlientechLinkService processResponse -- 
2024-07-05 11:18:56.781900 --  --- $log->save() --  ---549 -- 
2024-07-05 11:18:56.791600 --  --- $log->save() --  ---550 -- 
2024-07-05 11:18:56.793700 -- FileService saveDecodedFile -- 
2024-07-05 11:18:56.818100 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-05 11:18:56.872500 -- FileService downloadFile -- 
2024-07-05 11:18:56.872600 -- AlientechLinkService processRequest -- 
2024-07-05 11:18:56.872800 -- isAuth -- 
2024-07-05 11:18:56.872800 -- AlientechLinkService isAuth_success -- 
2024-07-05 11:18:57.873000 -- AlientechLinkService processResponse -- 
2024-07-05 11:18:57.875500 --  --- $log->save() --  ---551 -- 
2024-07-05 11:18:58.208500 --  --- $log->save() --  ---552 -- 
2024-07-05 11:18:58.229500 -- FileService saveDecodedFile -- 
2024-07-05 11:18:58.254200 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-05 12:33:13.021900 -- AlientechLinkService __construct -- 
2024-07-05 12:33:13.022200 -- AlientechLinkService processClient -- 
2024-07-05 12:33:13.022200 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 12:33:13.022500 -- AlientechLinkService initAccessToken -- 
2024-07-05 12:33:13.025200 -- iR8V9-KAuaE -- 
2024-07-05 12:33:13.025800 -- AlientechProjectService __construct -- 
2024-07-05 12:33:13.026700 -- FileSlotService __construct -- 
2024-07-05 12:33:13.026900 -- FileSlotService __construct -- 
2024-07-05 12:33:13.026900 -- AsyncOperationService __construct -- 
2024-07-05 12:33:13.027300 -- FileSlotService __construct -- 
2024-07-05 12:33:13.027400 -- Kess3Service __construct -- 
2024-07-05 12:33:13.027400 -- Kess3Service startEncoding -- 
2024-07-05 12:33:13.027400 -- AsyncOperationService startOperationEncode -- 
2024-07-05 12:33:13.045600 -- FileService uploadFiles -- 
2024-07-05 12:33:13.045900 -- FileSlotService reOpenSlot -- 
2024-07-05 12:33:13.045900 -- FileSlotService hasOpenFileSlots -- 
2024-07-05 12:33:13.046000 -- FileSlotService getSlots -- 
2024-07-05 12:33:13.046000 -- AlientechLinkService processRequest -- 
2024-07-05 12:33:13.046000 -- isAuth -- 
2024-07-05 12:33:13.046000 -- AlientechLinkService isAuth_success -- 
2024-07-05 12:33:14.490300 -- AlientechLinkService processResponse -- 
2024-07-05 12:33:14.502100 --  --- $log->save() --  ---553 -- 
2024-07-05 12:33:14.509500 --  --- $log->save() --  ---554 -- 
2024-07-05 12:33:14.510200 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-05 12:33:14.510400 -- /api/kess3/file-slots/7ed53d18-ac49-4ac3-83ec-08abac7580fb/reopen -- 
2024-07-05 12:33:14.510400 -- AlientechLinkService processRequest -- 
2024-07-05 12:33:14.510400 -- isAuth -- 
2024-07-05 12:33:14.510500 -- AlientechLinkService isAuth_success -- 
2024-07-05 12:33:14.715100 -- AlientechLinkService processResponse -- 
2024-07-05 12:33:14.717400 --  --- $log->save() --  ---555 -- 
2024-07-05 12:33:14.720200 --  --- $log->save() --  ---556 -- 
2024-07-05 12:33:14.720400 -- AlientechLinkService processRequest -- 
2024-07-05 12:33:14.720600 -- isAuth -- 
2024-07-05 12:33:14.720600 -- AlientechLinkService isAuth_success -- 
2024-07-05 12:33:16.049300 -- AlientechLinkService processResponse -- 
2024-07-05 12:33:16.051500 --  --- $log->save() --  ---557 -- 
2024-07-05 12:33:16.053000 --  --- $log->save() --  ---558 -- 
2024-07-05 12:33:16.060800 -- AlientechLinkService processRequest -- 
2024-07-05 12:33:16.060900 -- isAuth -- 
2024-07-05 12:33:16.060900 -- AlientechLinkService isAuth_success -- 
2024-07-05 12:33:16.975900 -- AlientechLinkService processResponse -- 
2024-07-05 12:33:16.977800 --  --- $log->save() --  ---559 -- 
2024-07-05 12:33:16.980200 --  --- $log->save() --  ---560 -- 
2024-07-05 12:33:16.980500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 12:33:16.980600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 12:33:16.981800 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-05 12:33:19.308200 -- AlientechLinkService __construct -- 
2024-07-05 12:33:19.308500 -- AlientechLinkService processClient -- 
2024-07-05 12:33:19.308600 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 12:33:19.308700 -- AlientechLinkService initAccessToken -- 
2024-07-05 12:33:19.310300 -- iR8V9-KAuaE -- 
2024-07-05 12:33:19.310700 -- AlientechProjectService __construct -- 
2024-07-05 12:33:19.311100 -- FileSlotService __construct -- 
2024-07-05 12:33:19.311200 -- FileSlotService __construct -- 
2024-07-05 12:33:19.311300 -- AsyncOperationService __construct -- 
2024-07-05 12:33:19.311700 -- ApiController actionKess3Encoded -- 
2024-07-05 12:33:19.317100 --  --- $log->save() --  ---561 -- 
2024-07-05 12:33:19.317300 -- AsyncOperationService createOperationDtoByData -- 
2024-07-05 12:33:19.317300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-05 12:33:19.322100 -- AsyncOperationService processCompletedOperation -- 
2024-07-05 12:33:19.322300 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-05 12:33:19.326000 -- AsyncOperationService finishCompletedOperation -- 
2024-07-05 12:33:19.326200 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-05 12:33:19.326200 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-05 12:33:19.329200 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-05 12:33:22.386400 -- AlientechLinkService __construct -- 
2024-07-05 12:33:22.386500 -- AlientechLinkService processClient -- 
2024-07-05 12:33:22.386600 -- AlientechLinkService is_null($this->client) -- 
2024-07-05 12:33:22.386900 -- AlientechLinkService initAccessToken -- 
2024-07-05 12:33:22.389500 -- iR8V9-KAuaE -- 
2024-07-05 12:33:22.390600 -- AlientechProjectService __construct -- 
2024-07-05 12:33:22.392200 -- FileSlotService __construct -- 
2024-07-05 12:33:22.392700 -- FileSlotService __construct -- 
2024-07-05 12:33:22.392800 -- AsyncOperationService __construct -- 
2024-07-05 12:33:22.393700 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-05 12:33:22.394200 -- FileService downloadFile -- 
2024-07-05 12:33:22.394300 -- AlientechLinkService processRequest -- 
2024-07-05 12:33:22.394600 -- isAuth -- 
2024-07-05 12:33:22.394600 -- AlientechLinkService isAuth_success -- 
2024-07-05 12:33:23.177500 -- AlientechLinkService processResponse -- 
2024-07-05 12:33:23.192700 --  --- $log->save() --  ---562 -- 
2024-07-05 12:33:23.289800 --  --- $log->save() --  ---563 -- 
2024-07-05 12:33:23.310600 -- FileService saveEncodedFile -- 
2024-07-05 12:33:23.342700 -- FileService $initFile->file_history={"id":"7621","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-05 12:33:23.364300 -- AlientechProjectService processSuccessAddEncodedFile -- 
2024-07-05 12:33:23.397600 -- AlientechProjectService setProjectStatusClientChanged -- 
2024-07-06 09:04:27.983900 -- AlientechLinkService __construct -- 
2024-07-06 09:04:27.984000 -- AlientechLinkService processClient -- 
2024-07-06 09:04:27.984100 -- AlientechLinkService is_null($this->client) -- 
2024-07-06 09:04:27.984400 -- AlientechLinkService initAccessToken -- 
2024-07-06 09:04:27.987800 -- iR8V9-KAuaE -- 
2024-07-06 09:04:27.989200 -- AlientechProjectService __construct -- 
2024-07-06 09:04:27.992000 -- FileSlotService __construct -- 
2024-07-06 09:04:27.992400 -- FileSlotService __construct -- 
2024-07-06 09:04:27.992500 -- AsyncOperationService __construct -- 
2024-07-06 09:04:27.994100 -- FileSlotService __construct -- 
2024-07-06 09:04:27.994200 -- Kess3Service __construct -- 
2024-07-06 09:04:27.994300 -- Kess3Service startDecoding -- 
2024-07-06 09:04:27.994500 -- AsyncOperationService startOperationDecode -- 
2024-07-06 09:04:27.994700 -- AlientechLinkService processRequest -- 
2024-07-06 09:04:27.994800 -- isAuth -- 
2024-07-06 09:04:27.994800 -- AlientechLinkService isAuth_success -- 
2024-07-06 09:04:29.540800 -- AlientechLinkService processResponse -- 
2024-07-06 09:04:29.559100 --  --- $log->save() --  ---564 -- 
2024-07-06 09:04:29.561000 --  --- $log->save() --  ---565 -- 
2024-07-06 09:04:29.561200 -- AsyncOperationService createOperationDtoByData -- 
2024-07-06 09:04:29.561300 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-06 09:04:29.571500 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-06 09:04:31.345700 -- AlientechLinkService __construct -- 
2024-07-06 09:04:31.346000 -- AlientechLinkService processClient -- 
2024-07-06 09:04:31.346100 -- AlientechLinkService is_null($this->client) -- 
2024-07-06 09:04:31.346200 -- AlientechLinkService initAccessToken -- 
2024-07-06 09:04:31.348300 -- iR8V9-KAuaE -- 
2024-07-06 09:04:31.348600 -- AlientechProjectService __construct -- 
2024-07-06 09:04:31.348800 -- FileSlotService __construct -- 
2024-07-06 09:04:31.348900 -- FileSlotService __construct -- 
2024-07-06 09:04:31.348900 -- AsyncOperationService __construct -- 
2024-07-06 09:04:31.349100 -- ApiController actionKess3Decoded -- 
2024-07-06 09:04:31.357800 --  --- $log->save() --  ---566 -- 
2024-07-06 09:04:31.358000 -- AsyncOperationService createOperationDtoByData -- 
2024-07-06 09:04:31.358100 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-06 09:04:31.362400 -- AsyncOperationService processCompletedOperation -- 
2024-07-06 09:04:31.362700 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-06 09:04:31.367600 -- AsyncOperationService finishCompletedOperation -- 
2024-07-06 09:04:31.367600 -- AsyncOperationService finishCompletedOperationDecode -- 
2024-07-06 09:04:31.367700 -- AlientechProjectService processSuccessOperationDecode -- 
2024-07-06 09:04:31.370400 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-06 09:04:32.968200 -- AlientechLinkService __construct -- 
2024-07-06 09:04:32.968400 -- AlientechLinkService processClient -- 
2024-07-06 09:04:32.968400 -- AlientechLinkService is_null($this->client) -- 
2024-07-06 09:04:32.968700 -- AlientechLinkService initAccessToken -- 
2024-07-06 09:04:32.979400 -- iR8V9-KAuaE -- 
2024-07-06 09:04:32.980100 -- AlientechProjectService __construct -- 
2024-07-06 09:04:32.986800 -- FileSlotService __construct -- 
2024-07-06 09:04:32.988200 -- FileSlotService __construct -- 
2024-07-06 09:04:32.988400 -- AsyncOperationService __construct -- 
2024-07-06 09:04:32.988800 -- AsyncOperationService downloadDecodedFiles -- 
2024-07-06 09:04:32.990200 -- FileService downloadFile -- 
2024-07-06 09:04:32.991100 -- AlientechLinkService processRequest -- 
2024-07-06 09:04:32.992200 -- isAuth -- 
2024-07-06 09:04:32.992400 -- AlientechLinkService isAuth_success -- 
2024-07-06 09:04:33.237700 -- AlientechLinkService processResponse -- 
2024-07-06 09:04:33.260800 --  --- $log->save() --  ---567 -- 
2024-07-06 09:04:33.263000 --  --- $log->save() --  ---568 -- 
2024-07-06 09:04:33.263500 -- FileService saveDecodedFile -- 
2024-07-06 09:04:33.280200 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-06 09:04:33.317000 -- FileService downloadFile -- 
2024-07-06 09:04:33.317100 -- AlientechLinkService processRequest -- 
2024-07-06 09:04:33.317200 -- isAuth -- 
2024-07-06 09:04:33.317700 -- AlientechLinkService isAuth_success -- 
2024-07-06 09:04:34.130900 -- AlientechLinkService processResponse -- 
2024-07-06 09:04:34.133400 --  --- $log->save() --  ---569 -- 
2024-07-06 09:04:34.292800 --  --- $log->save() --  ---570 -- 
2024-07-06 09:04:34.303800 -- FileService saveDecodedFile -- 
2024-07-06 09:04:34.318900 -- AlientechProjectService processSuccessAddDecodedFile -- 
2024-07-06 12:09:54.542100 -- AlientechLinkService __construct -- 
2024-07-06 12:09:54.542300 -- AlientechLinkService processClient -- 
2024-07-06 12:09:54.542300 -- AlientechLinkService is_null($this->client) -- 
2024-07-06 12:09:54.542700 -- AlientechLinkService initAccessToken -- 
2024-07-06 12:09:54.544800 -- iR8V9-KAuaE -- 
2024-07-06 12:09:54.545300 -- AlientechProjectService __construct -- 
2024-07-06 12:09:54.546100 -- FileSlotService __construct -- 
2024-07-06 12:09:54.546200 -- FileSlotService __construct -- 
2024-07-06 12:09:54.546300 -- AsyncOperationService __construct -- 
2024-07-06 12:09:54.546700 -- FileSlotService __construct -- 
2024-07-06 12:09:54.546700 -- Kess3Service __construct -- 
2024-07-06 12:09:54.546800 -- Kess3Service startEncoding -- 
2024-07-06 12:09:54.546800 -- AsyncOperationService startOperationEncode -- 
2024-07-06 12:09:54.564300 -- FileService uploadFiles -- 
2024-07-06 12:09:54.564600 -- FileSlotService reOpenSlot -- 
2024-07-06 12:09:54.564700 -- FileSlotService hasOpenFileSlots -- 
2024-07-06 12:09:54.564700 -- FileSlotService getSlots -- 
2024-07-06 12:09:54.564700 -- AlientechLinkService processRequest -- 
2024-07-06 12:09:54.564800 -- isAuth -- 
2024-07-06 12:09:54.564800 -- AlientechLinkService isAuth_success -- 
2024-07-06 12:09:55.208500 -- AlientechLinkService processResponse -- 
2024-07-06 12:09:55.218200 --  --- $log->save() --  ---571 -- 
2024-07-06 12:09:55.226700 --  --- $log->save() --  ---572 -- 
2024-07-06 12:09:55.227300 -- FileSlotService fileSlotLimitIsNotReached -- 
2024-07-06 12:09:55.227300 -- FileSlotService closeAllFileSlots -- 
2024-07-06 12:09:55.227400 -- FileSlotService getSlots -- 
2024-07-06 12:09:55.227400 -- AlientechLinkService processRequest -- 
2024-07-06 12:09:55.227500 -- isAuth -- 
2024-07-06 12:09:55.227500 -- AlientechLinkService isAuth_success -- 
2024-07-06 12:09:56.648000 -- AlientechLinkService processResponse -- 
2024-07-06 12:09:56.651600 --  --- $log->save() --  ---573 -- 
2024-07-06 12:09:56.659400 --  --- $log->save() --  ---574 -- 
2024-07-06 12:09:56.660900 -- slot [61dadc04-3b7f-42b3-b395-16cee3e2a0ed] dateDiff {"y":0,"m":0,"d":0,"h":3,"i":5,"s":27,"f":0.174182,"invert":1,"days":0,"from_string":false} -- 
2024-07-06 12:09:56.661100 -- FileSlotService closeSlot -- 
2024-07-06 12:09:56.661200 -- /api/kess3/file-slots/61dadc04-3b7f-42b3-b395-16cee3e2a0ed/close  -- 
2024-07-06 12:09:56.661300 -- AlientechLinkService processRequest -- 
2024-07-06 12:09:56.661400 -- isAuth -- 
2024-07-06 12:09:56.661500 -- AlientechLinkService isAuth_success -- 
2024-07-06 12:09:56.850300 -- AlientechLinkService processResponse -- 
2024-07-06 12:09:56.852300 --  --- $log->save() --  ---575 -- 
2024-07-06 12:09:56.854800 --  --- $log->save() --  ---576 -- 
2024-07-06 12:09:56.855100 -- slot [61dadc04-3b7f-42b3-b395-16cee3e2a0ed] closeSlot null -- 
2024-07-06 12:09:56.855200 -- slot [7ed53d18-ac49-4ac3-83ec-08abac7580fb] dateDiff {"y":0,"m":0,"d":1,"h":0,"i":51,"s":4,"f":0.358541,"invert":1,"days":1,"from_string":false} -- 
2024-07-06 12:09:56.855300 -- FileSlotService closeSlot -- 
2024-07-06 12:09:56.855400 -- /api/kess3/file-slots/7ed53d18-ac49-4ac3-83ec-08abac7580fb/close  -- 
2024-07-06 12:09:56.855500 -- AlientechLinkService processRequest -- 
2024-07-06 12:09:56.855600 -- isAuth -- 
2024-07-06 12:09:56.855800 -- AlientechLinkService isAuth_success -- 
2024-07-06 12:09:58.249800 -- AlientechLinkService processResponse -- 
2024-07-06 12:09:58.253100 --  --- $log->save() --  ---577 -- 
2024-07-06 12:09:58.255200 --  --- $log->save() --  ---578 -- 
2024-07-06 12:09:58.255400 -- slot [7ed53d18-ac49-4ac3-83ec-08abac7580fb] closeSlot null -- 
2024-07-06 12:09:58.255600 -- slot [774f4ea7-8c0e-455a-be93-709cd975f685] dateDiff {"y":0,"m":0,"d":1,"h":1,"i":59,"s":36,"f":0.688886,"invert":1,"days":1,"from_string":false} -- 
2024-07-06 12:09:58.255900 -- FileSlotService closeSlot -- 
2024-07-06 12:09:58.256100 -- /api/kess3/file-slots/774f4ea7-8c0e-455a-be93-709cd975f685/close  -- 
2024-07-06 12:09:58.256300 -- AlientechLinkService processRequest -- 
2024-07-06 12:09:58.256400 -- isAuth -- 
2024-07-06 12:09:58.256500 -- AlientechLinkService isAuth_success -- 
2024-07-06 12:09:58.474800 -- AlientechLinkService processResponse -- 
2024-07-06 12:09:58.477200 --  --- $log->save() --  ---579 -- 
2024-07-06 12:09:58.479300 --  --- $log->save() --  ---580 -- 
2024-07-06 12:09:58.479500 -- slot [774f4ea7-8c0e-455a-be93-709cd975f685] closeSlot null -- 
2024-07-06 12:09:58.479600 -- closeAllSlots finish -- 
2024-07-06 12:09:58.479700 -- /api/kess3/file-slots/61dadc04-3b7f-42b3-b395-16cee3e2a0ed/reopen -- 
2024-07-06 12:09:58.479700 -- AlientechLinkService processRequest -- 
2024-07-06 12:09:58.480000 -- isAuth -- 
2024-07-06 12:09:58.480200 -- AlientechLinkService isAuth_success -- 
2024-07-06 12:09:58.682700 -- AlientechLinkService processResponse -- 
2024-07-06 12:09:58.685900 --  --- $log->save() --  ---581 -- 
2024-07-06 12:09:58.688200 --  --- $log->save() --  ---582 -- 
2024-07-06 12:09:58.688400 -- AlientechLinkService processRequest -- 
2024-07-06 12:09:58.688800 -- isAuth -- 
2024-07-06 12:09:58.689000 -- AlientechLinkService isAuth_success -- 
2024-07-06 12:09:59.233400 -- AlientechLinkService processResponse -- 
2024-07-06 12:09:59.236600 --  --- $log->save() --  ---583 -- 
2024-07-06 12:09:59.238800 --  --- $log->save() --  ---584 -- 
2024-07-06 12:09:59.252800 -- AlientechLinkService processRequest -- 
2024-07-06 12:09:59.253000 -- isAuth -- 
2024-07-06 12:09:59.253100 -- AlientechLinkService isAuth_success -- 
2024-07-06 12:10:00.143500 -- AlientechLinkService processResponse -- 
2024-07-06 12:10:00.145400 --  --- $log->save() --  ---585 -- 
2024-07-06 12:10:00.147300 --  --- $log->save() --  ---586 -- 
2024-07-06 12:10:00.147600 -- AsyncOperationService createOperationDtoByData -- 
2024-07-06 12:10:00.147700 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-06 12:10:00.148600 -- AsyncOperationRepository createAsyncOperation -- 
2024-07-06 12:10:02.271000 -- AlientechLinkService __construct -- 
2024-07-06 12:10:02.271300 -- AlientechLinkService processClient -- 
2024-07-06 12:10:02.271400 -- AlientechLinkService is_null($this->client) -- 
2024-07-06 12:10:02.271500 -- AlientechLinkService initAccessToken -- 
2024-07-06 12:10:02.273400 -- iR8V9-KAuaE -- 
2024-07-06 12:10:02.273700 -- AlientechProjectService __construct -- 
2024-07-06 12:10:02.274000 -- FileSlotService __construct -- 
2024-07-06 12:10:02.274200 -- FileSlotService __construct -- 
2024-07-06 12:10:02.274200 -- AsyncOperationService __construct -- 
2024-07-06 12:10:02.274500 -- ApiController actionKess3Encoded -- 
2024-07-06 12:10:02.280300 --  --- $log->save() --  ---587 -- 
2024-07-06 12:10:02.280500 -- AsyncOperationService createOperationDtoByData -- 
2024-07-06 12:10:02.280600 -- AsyncOperationRepository buildAsyncOperationByData -- 
2024-07-06 12:10:02.284300 -- AsyncOperationService processCompletedOperation -- 
2024-07-06 12:10:02.284500 -- AsyncOperationRepository updateAsyncOperation -- 
2024-07-06 12:10:02.287200 -- AsyncOperationService finishCompletedOperation -- 
2024-07-06 12:10:02.287300 -- AsyncOperationService finishCompletedOperationEncode -- 
2024-07-06 12:10:02.287400 -- AlientechProjectService processSuccessOperationEncode -- 
2024-07-06 12:10:02.290000 -- AlientechProjectService setProjectStatusAdminChanged -- 
2024-07-06 12:10:04.235500 -- AlientechLinkService __construct -- 
2024-07-06 12:10:04.235600 -- AlientechLinkService processClient -- 
2024-07-06 12:10:04.235700 -- AlientechLinkService is_null($this->client) -- 
2024-07-06 12:10:04.236000 -- AlientechLinkService initAccessToken -- 
2024-07-06 12:10:04.239100 -- iR8V9-KAuaE -- 
2024-07-06 12:10:04.240900 -- AlientechProjectService __construct -- 
2024-07-06 12:10:04.243400 -- FileSlotService __construct -- 
2024-07-06 12:10:04.243900 -- FileSlotService __construct -- 
2024-07-06 12:10:04.244100 -- AsyncOperationService __construct -- 
2024-07-06 12:10:04.244700 -- AsyncOperationService downloadEncodedFiles -- 
2024-07-06 12:10:04.245100 -- FileService downloadFile -- 
2024-07-06 12:10:04.245300 -- AlientechLinkService processRequest -- 
2024-07-06 12:10:04.245400 -- isAuth -- 
2024-07-06 12:10:04.245500 -- AlientechLinkService isAuth_success -- 
2024-07-06 12:10:04.582800 -- AlientechLinkService processResponse -- 
2024-07-06 12:10:04.610000 --  --- $log->save() --  ---588 -- 
2024-07-06 12:10:04.661300 --  --- $log->save() --  ---589 -- 
2024-07-06 12:10:04.670300 -- FileService saveEncodedFile -- 
2024-07-06 12:10:04.695500 -- FileService $initFile->file_history={"id":"7628","value":"1","file_ver":"1","comment":null,"option":"can_download"} -- 
2024-07-06 12:10:04.715400 -- AlientechProjectService processSuccessAddEncodedFile -- 
2025-05-27 16:07:56.586500 -- AlientechLinkService __construct -- 
2025-05-27 16:07:56.591300 -- AlientechLinkService processClient -- 
2025-05-27 16:07:56.592000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 16:07:56.598000 -- AlientechLinkService initAccessToken -- 
2025-05-27 16:07:56.604800 -- iR8V9-KAuaE -- 
2025-05-27 16:07:56.612500 -- FileSlotService __construct -- 
2025-05-27 16:10:54.415100 -- AlientechLinkService __construct -- 
2025-05-27 16:10:54.422500 -- AlientechLinkService processClient -- 
2025-05-27 16:10:54.424000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 16:10:54.432600 -- AlientechLinkService initAccessToken -- 
2025-05-27 16:10:54.435200 -- iR8V9-KAuaE -- 
2025-05-27 16:10:54.448800 -- FileSlotService __construct -- 
2025-05-27 16:11:45.296600 -- AlientechLinkService __construct -- 
2025-05-27 16:11:45.301000 -- AlientechLinkService processClient -- 
2025-05-27 16:11:45.301800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 16:11:45.306600 -- AlientechLinkService initAccessToken -- 
2025-05-27 16:11:45.308500 -- iR8V9-KAuaE -- 
2025-05-27 16:11:45.315400 -- FileSlotService __construct -- 
2025-05-27 16:18:09.062800 -- AlientechLinkService __construct -- 
2025-05-27 16:18:09.069600 -- AlientechLinkService processClient -- 
2025-05-27 16:18:09.070700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 16:18:09.077600 -- AlientechLinkService initAccessToken -- 
2025-05-27 16:18:09.079500 -- iR8V9-KAuaE -- 
2025-05-27 16:18:09.090700 -- FileSlotService __construct -- 
2025-05-27 16:18:37.479400 -- AlientechLinkService __construct -- 
2025-05-27 16:18:37.486600 -- AlientechLinkService processClient -- 
2025-05-27 16:18:37.487800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 16:18:37.495200 -- AlientechLinkService initAccessToken -- 
2025-05-27 16:18:37.497300 -- iR8V9-KAuaE -- 
2025-05-27 16:18:37.509000 -- FileSlotService __construct -- 
2025-05-27 16:20:22.477200 -- AlientechLinkService __construct -- 
2025-05-27 16:20:22.484700 -- AlientechLinkService processClient -- 
2025-05-27 16:20:22.485900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 16:20:22.493200 -- AlientechLinkService initAccessToken -- 
2025-05-27 16:20:22.495300 -- iR8V9-KAuaE -- 
2025-05-27 16:20:22.506900 -- FileSlotService __construct -- 
2025-05-27 16:21:28.930700 -- AlientechLinkService __construct -- 
2025-05-27 16:21:28.937300 -- AlientechLinkService processClient -- 
2025-05-27 16:21:28.938400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 16:21:28.945500 -- AlientechLinkService initAccessToken -- 
2025-05-27 16:21:28.947400 -- iR8V9-KAuaE -- 
2025-05-27 16:21:28.958400 -- FileSlotService __construct -- 
2025-05-27 17:01:12.195000 -- AlientechLinkService __construct -- 
2025-05-27 17:01:12.201900 -- AlientechLinkService processClient -- 
2025-05-27 17:01:12.203000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:01:12.209900 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:01:12.211900 -- iR8V9-KAuaE -- 
2025-05-27 17:01:12.223000 -- FileSlotService __construct -- 
2025-05-27 17:20:22.534700 -- AlientechLinkService __construct -- 
2025-05-27 17:20:22.542500 -- AlientechLinkService processClient -- 
2025-05-27 17:20:22.543700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:20:22.552300 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:20:22.554500 -- iR8V9-KAuaE -- 
2025-05-27 17:20:22.575900 -- AlientechProjectService __construct -- 
2025-05-27 17:20:22.588600 -- FileSlotService __construct -- 
2025-05-27 17:20:22.593900 -- FileSlotService __construct -- 
2025-05-27 17:20:22.595100 -- AsyncOperationService __construct -- 
2025-05-27 17:20:22.615500 -- ApiController actionKess3Decoded -- 
2025-05-27 17:20:22.616700 -- AsyncOperationService processDataToOperation -- 
2025-05-27 17:20:22.670500 --  --- $log->save() --  ---610 -- 
2025-05-27 17:20:22.674000 --  --- $log->save() --  ---611 -- 
2025-05-27 17:23:45.342300 -- AlientechLinkService __construct -- 
2025-05-27 17:23:45.351500 -- AlientechLinkService processClient -- 
2025-05-27 17:23:45.352800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:23:45.362100 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:23:45.363800 -- iR8V9-KAuaE -- 
2025-05-27 17:23:45.383300 -- AlientechProjectService __construct -- 
2025-05-27 17:23:45.395600 -- FileSlotService __construct -- 
2025-05-27 17:23:45.400700 -- FileSlotService __construct -- 
2025-05-27 17:23:45.401900 -- AsyncOperationService __construct -- 
2025-05-27 17:23:45.421200 -- ApiController actionKess3Decoded -- 
2025-05-27 17:23:45.422500 -- AsyncOperationService processDataToOperation -- 
2025-05-27 17:23:45.484100 --  --- $log->save() --  ---612 -- 
2025-05-27 17:23:45.487800 --  --- $log->save() --  ---613 -- 
2025-05-27 17:27:24.348500 -- AlientechLinkService __construct -- 
2025-05-27 17:27:24.357400 -- AlientechLinkService processClient -- 
2025-05-27 17:27:24.358700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:27:24.367900 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:27:24.369700 -- iR8V9-KAuaE -- 
2025-05-27 17:27:24.381400 -- FileSlotService __construct -- 
2025-05-27 17:41:07.468000 -- AlientechLinkService __construct -- 
2025-05-27 17:41:07.477000 -- AlientechLinkService processClient -- 
2025-05-27 17:41:07.478500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:41:07.487500 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:41:07.489800 -- iR8V9-KAuaE -- 
2025-05-27 17:41:07.501700 -- FileSlotService __construct -- 
2025-05-27 17:42:36.080500 -- AlientechLinkService __construct -- 
2025-05-27 17:42:36.087400 -- AlientechLinkService processClient -- 
2025-05-27 17:42:36.088800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:42:36.094200 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:42:36.096000 -- iR8V9-KAuaE -- 
2025-05-27 17:42:36.104800 -- FileSlotService __construct -- 
2025-05-27 17:47:11.392000 -- AlientechLinkService __construct -- 
2025-05-27 17:47:11.401100 -- AlientechLinkService processClient -- 
2025-05-27 17:47:11.402400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:47:11.412000 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:47:11.414500 -- iR8V9-KAuaE -- 
2025-05-27 17:47:11.426800 -- FileSlotService __construct -- 
2025-05-27 17:48:08.841800 -- AlientechLinkService __construct -- 
2025-05-27 17:48:08.850100 -- AlientechLinkService processClient -- 
2025-05-27 17:48:08.851400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:48:08.860100 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:48:08.866600 -- iR8V9-KAuaE -- 
2025-05-27 17:48:08.877800 -- FileSlotService __construct -- 
2025-05-27 17:49:30.977700 -- AlientechLinkService __construct -- 
2025-05-27 17:49:31.010600 -- AlientechLinkService processClient -- 
2025-05-27 17:49:31.012700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:49:31.024900 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:49:31.027500 -- iR8V9-KAuaE -- 
2025-05-27 17:49:31.041300 -- FileSlotService __construct -- 
2025-05-27 17:50:04.891300 -- AlientechLinkService __construct -- 
2025-05-27 17:50:04.897900 -- AlientechLinkService processClient -- 
2025-05-27 17:50:04.899200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:50:04.904400 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:50:04.910100 -- iR8V9-KAuaE -- 
2025-05-27 17:50:04.919100 -- FileSlotService __construct -- 
2025-05-27 17:51:02.699000 -- AlientechLinkService __construct -- 
2025-05-27 17:51:02.707800 -- AlientechLinkService processClient -- 
2025-05-27 17:51:02.709100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:51:02.718400 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:51:02.720000 -- iR8V9-KAuaE -- 
2025-05-27 17:51:02.731600 -- FileSlotService __construct -- 
2025-05-27 17:52:48.586600 -- AlientechLinkService __construct -- 
2025-05-27 17:52:48.593500 -- AlientechLinkService processClient -- 
2025-05-27 17:52:48.594600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:52:48.602000 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:52:48.603600 -- iR8V9-KAuaE -- 
2025-05-27 17:52:48.615300 -- FileSlotService __construct -- 
2025-05-27 17:56:23.297400 -- AlientechLinkService __construct -- 
2025-05-27 17:56:23.306800 -- AlientechLinkService processClient -- 
2025-05-27 17:56:23.308300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 17:56:23.317400 -- AlientechLinkService initAccessToken -- 
2025-05-27 17:56:23.320100 -- iR8V9-KAuaE -- 
2025-05-27 17:56:23.332800 -- FileSlotService __construct -- 
2025-05-27 18:06:18.711300 -- AlientechLinkService __construct -- 
2025-05-27 18:06:18.718700 -- AlientechLinkService processClient -- 
2025-05-27 18:06:18.719800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 18:06:18.727200 -- AlientechLinkService initAccessToken -- 
2025-05-27 18:06:18.728600 -- iR8V9-KAuaE -- 
2025-05-27 18:06:18.740000 -- FileSlotService __construct -- 
2025-05-27 18:07:18.400400 -- AlientechLinkService __construct -- 
2025-05-27 18:07:18.407800 -- AlientechLinkService processClient -- 
2025-05-27 18:07:18.408900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 18:07:18.417800 -- AlientechLinkService initAccessToken -- 
2025-05-27 18:07:18.419500 -- iR8V9-KAuaE -- 
2025-05-27 18:07:18.431800 -- FileSlotService __construct -- 
2025-05-27 18:40:24.473800 -- AlientechLinkService __construct -- 
2025-05-27 18:40:24.481100 -- AlientechLinkService processClient -- 
2025-05-27 18:40:24.482500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 18:40:24.489900 -- AlientechLinkService initAccessToken -- 
2025-05-27 18:40:24.491200 -- iR8V9-KAuaE -- 
2025-05-27 18:40:24.502600 -- FileSlotService __construct -- 
2025-05-27 18:40:58.792400 -- AlientechLinkService __construct -- 
2025-05-27 18:40:58.800500 -- AlientechLinkService processClient -- 
2025-05-27 18:40:58.801700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 18:40:58.809500 -- AlientechLinkService initAccessToken -- 
2025-05-27 18:40:58.811000 -- iR8V9-KAuaE -- 
2025-05-27 18:40:58.824100 -- FileSlotService __construct -- 
2025-05-27 18:42:35.756900 -- AlientechLinkService __construct -- 
2025-05-27 18:42:35.764300 -- AlientechLinkService processClient -- 
2025-05-27 18:42:35.765400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 18:42:35.772400 -- AlientechLinkService initAccessToken -- 
2025-05-27 18:42:35.773800 -- iR8V9-KAuaE -- 
2025-05-27 18:42:35.785100 -- FileSlotService __construct -- 
2025-05-27 18:43:53.433400 -- AlientechLinkService __construct -- 
2025-05-27 18:43:53.440500 -- AlientechLinkService processClient -- 
2025-05-27 18:43:53.441900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 18:43:53.449000 -- AlientechLinkService initAccessToken -- 
2025-05-27 18:43:53.451000 -- iR8V9-KAuaE -- 
2025-05-27 18:43:53.462300 -- FileSlotService __construct -- 
2025-05-27 19:11:16.834800 -- AlientechLinkService __construct -- 
2025-05-27 19:11:16.842100 -- AlientechLinkService processClient -- 
2025-05-27 19:11:16.843200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:11:16.851700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:11:22.425300 -- AlientechLinkService __construct -- 
2025-05-27 19:11:22.432600 -- AlientechLinkService processClient -- 
2025-05-27 19:11:22.433800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:11:22.443500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:11:42.715700 -- AlientechLinkService __construct -- 
2025-05-27 19:11:42.720000 -- AlientechLinkService processClient -- 
2025-05-27 19:11:42.720800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:11:42.726400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:12:03.341100 -- AlientechLinkService __construct -- 
2025-05-27 19:12:03.348400 -- AlientechLinkService processClient -- 
2025-05-27 19:12:03.349400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:12:03.359000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:12:23.674400 -- AlientechLinkService __construct -- 
2025-05-27 19:12:23.678900 -- AlientechLinkService processClient -- 
2025-05-27 19:12:23.680000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:12:23.684000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:12:43.808000 -- AlientechLinkService __construct -- 
2025-05-27 19:12:43.812500 -- AlientechLinkService processClient -- 
2025-05-27 19:12:43.813500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:12:43.817700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:13:04.230700 -- AlientechLinkService __construct -- 
2025-05-27 19:13:04.235500 -- AlientechLinkService processClient -- 
2025-05-27 19:13:04.236700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:13:04.240900 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:13:24.501600 -- AlientechLinkService __construct -- 
2025-05-27 19:13:24.508800 -- AlientechLinkService processClient -- 
2025-05-27 19:13:24.509900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:13:24.519400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:13:44.757900 -- AlientechLinkService __construct -- 
2025-05-27 19:13:44.765200 -- AlientechLinkService processClient -- 
2025-05-27 19:13:44.766300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:13:44.776000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:14:05.299000 -- AlientechLinkService __construct -- 
2025-05-27 19:14:05.306100 -- AlientechLinkService processClient -- 
2025-05-27 19:14:05.307200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:14:05.317000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:14:25.811200 -- AlientechLinkService __construct -- 
2025-05-27 19:14:25.815800 -- AlientechLinkService processClient -- 
2025-05-27 19:14:25.816900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:14:25.821100 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:14:46.004800 -- AlientechLinkService __construct -- 
2025-05-27 19:14:46.009500 -- AlientechLinkService processClient -- 
2025-05-27 19:14:46.010600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:14:46.014600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:15:06.257900 -- AlientechLinkService __construct -- 
2025-05-27 19:15:06.262500 -- AlientechLinkService processClient -- 
2025-05-27 19:15:06.263600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:15:06.267600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:15:26.606300 -- AlientechLinkService __construct -- 
2025-05-27 19:15:26.613400 -- AlientechLinkService processClient -- 
2025-05-27 19:15:26.614700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:15:26.624200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:15:46.968600 -- AlientechLinkService __construct -- 
2025-05-27 19:15:46.973800 -- AlientechLinkService processClient -- 
2025-05-27 19:15:46.974600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:15:46.980200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:16:07.656200 -- AlientechLinkService __construct -- 
2025-05-27 19:16:07.663800 -- AlientechLinkService processClient -- 
2025-05-27 19:16:07.665100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:16:07.674800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:16:28.069600 -- AlientechLinkService __construct -- 
2025-05-27 19:16:28.074500 -- AlientechLinkService processClient -- 
2025-05-27 19:16:28.075600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:16:28.080000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:16:48.352300 -- AlientechLinkService __construct -- 
2025-05-27 19:16:48.356900 -- AlientechLinkService processClient -- 
2025-05-27 19:16:48.358200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:16:48.362400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:17:08.353300 -- AlientechLinkService __construct -- 
2025-05-27 19:17:08.357900 -- AlientechLinkService processClient -- 
2025-05-27 19:17:08.359000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:17:08.363200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:17:28.633400 -- AlientechLinkService __construct -- 
2025-05-27 19:17:28.640600 -- AlientechLinkService processClient -- 
2025-05-27 19:17:28.641700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:17:28.651600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:17:49.034800 -- AlientechLinkService __construct -- 
2025-05-27 19:17:49.042100 -- AlientechLinkService processClient -- 
2025-05-27 19:17:49.043200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:17:49.053300 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:18:09.725500 -- AlientechLinkService __construct -- 
2025-05-27 19:18:09.733000 -- AlientechLinkService processClient -- 
2025-05-27 19:18:09.734100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:18:09.743800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:18:30.010500 -- AlientechLinkService __construct -- 
2025-05-27 19:18:30.015700 -- AlientechLinkService processClient -- 
2025-05-27 19:18:30.016900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:18:30.020900 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:18:50.479500 -- AlientechLinkService __construct -- 
2025-05-27 19:18:50.484300 -- AlientechLinkService processClient -- 
2025-05-27 19:18:50.485400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:18:50.489600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:19:10.537400 -- AlientechLinkService __construct -- 
2025-05-27 19:19:10.542200 -- AlientechLinkService processClient -- 
2025-05-27 19:19:10.543400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:19:10.547700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:19:30.748300 -- AlientechLinkService __construct -- 
2025-05-27 19:19:30.752800 -- AlientechLinkService processClient -- 
2025-05-27 19:19:30.753500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:19:30.759600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:19:51.300700 -- AlientechLinkService __construct -- 
2025-05-27 19:19:51.308200 -- AlientechLinkService processClient -- 
2025-05-27 19:19:51.309300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:19:51.319600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:20:11.876200 -- AlientechLinkService __construct -- 
2025-05-27 19:20:11.883700 -- AlientechLinkService processClient -- 
2025-05-27 19:20:11.884800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:20:11.895100 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:20:32.095000 -- AlientechLinkService __construct -- 
2025-05-27 19:20:32.100200 -- AlientechLinkService processClient -- 
2025-05-27 19:20:32.101400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:20:32.105800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:20:52.496600 -- AlientechLinkService __construct -- 
2025-05-27 19:20:52.501300 -- AlientechLinkService processClient -- 
2025-05-27 19:20:52.502500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:20:52.506600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:21:12.535800 -- AlientechLinkService __construct -- 
2025-05-27 19:21:12.540500 -- AlientechLinkService processClient -- 
2025-05-27 19:21:12.541700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:21:12.546000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:21:33.024500 -- AlientechLinkService __construct -- 
2025-05-27 19:21:33.031700 -- AlientechLinkService processClient -- 
2025-05-27 19:21:33.032900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:21:33.042600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:21:53.377900 -- AlientechLinkService __construct -- 
2025-05-27 19:21:53.385200 -- AlientechLinkService processClient -- 
2025-05-27 19:21:53.386300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:21:53.398600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:22:13.892500 -- AlientechLinkService __construct -- 
2025-05-27 19:22:13.899800 -- AlientechLinkService processClient -- 
2025-05-27 19:22:13.901000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:22:13.911000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:22:34.370600 -- AlientechLinkService __construct -- 
2025-05-27 19:22:34.375300 -- AlientechLinkService processClient -- 
2025-05-27 19:22:34.376400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:22:34.380600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:22:54.543700 -- AlientechLinkService __construct -- 
2025-05-27 19:22:54.548400 -- AlientechLinkService processClient -- 
2025-05-27 19:22:54.549500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:22:54.553600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:23:14.766400 -- AlientechLinkService __construct -- 
2025-05-27 19:23:14.771000 -- AlientechLinkService processClient -- 
2025-05-27 19:23:14.772100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:23:14.776200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:23:34.987000 -- AlientechLinkService __construct -- 
2025-05-27 19:23:34.994500 -- AlientechLinkService processClient -- 
2025-05-27 19:23:34.995700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:23:35.005700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:23:55.525400 -- AlientechLinkService __construct -- 
2025-05-27 19:23:55.533300 -- AlientechLinkService processClient -- 
2025-05-27 19:23:55.534400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:23:55.544500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:24:15.969500 -- AlientechLinkService __construct -- 
2025-05-27 19:24:15.977200 -- AlientechLinkService processClient -- 
2025-05-27 19:24:15.978300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:24:15.987900 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:24:36.265500 -- AlientechLinkService __construct -- 
2025-05-27 19:24:36.270300 -- AlientechLinkService processClient -- 
2025-05-27 19:24:36.271600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:24:36.276100 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:24:56.658100 -- AlientechLinkService __construct -- 
2025-05-27 19:24:56.662900 -- AlientechLinkService processClient -- 
2025-05-27 19:24:56.664000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:24:56.668400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:25:16.701000 -- AlientechLinkService __construct -- 
2025-05-27 19:25:16.705700 -- AlientechLinkService processClient -- 
2025-05-27 19:25:16.706800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:25:16.711000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:25:36.936400 -- AlientechLinkService __construct -- 
2025-05-27 19:25:36.943900 -- AlientechLinkService processClient -- 
2025-05-27 19:25:36.945100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:25:36.954900 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:25:57.488200 -- AlientechLinkService __construct -- 
2025-05-27 19:25:57.495600 -- AlientechLinkService processClient -- 
2025-05-27 19:25:57.496700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:25:57.506700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:26:18.059700 -- AlientechLinkService __construct -- 
2025-05-27 19:26:18.067100 -- AlientechLinkService processClient -- 
2025-05-27 19:26:18.068300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:26:18.078600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:26:38.288700 -- AlientechLinkService __construct -- 
2025-05-27 19:26:38.293200 -- AlientechLinkService processClient -- 
2025-05-27 19:26:38.294300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:26:38.298500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:26:58.627200 -- AlientechLinkService __construct -- 
2025-05-27 19:26:58.631900 -- AlientechLinkService processClient -- 
2025-05-27 19:26:58.633000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:26:58.637200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:27:18.634100 -- AlientechLinkService __construct -- 
2025-05-27 19:27:18.638600 -- AlientechLinkService processClient -- 
2025-05-27 19:27:18.639800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:27:18.643900 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:27:39.011500 -- AlientechLinkService __construct -- 
2025-05-27 19:27:39.018700 -- AlientechLinkService processClient -- 
2025-05-27 19:27:39.019800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:27:39.029900 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:27:59.364200 -- AlientechLinkService __construct -- 
2025-05-27 19:27:59.371900 -- AlientechLinkService processClient -- 
2025-05-27 19:27:59.373300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:27:59.383400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:28:20.284800 -- AlientechLinkService __construct -- 
2025-05-27 19:28:20.292200 -- AlientechLinkService processClient -- 
2025-05-27 19:28:20.293300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:28:20.304300 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:28:40.410300 -- AlientechLinkService __construct -- 
2025-05-27 19:28:40.413600 -- AlientechLinkService processClient -- 
2025-05-27 19:28:40.414600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:28:40.417600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:29:00.837500 -- AlientechLinkService __construct -- 
2025-05-27 19:29:00.842200 -- AlientechLinkService processClient -- 
2025-05-27 19:29:00.843500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:29:00.847600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:29:21.118800 -- AlientechLinkService __construct -- 
2025-05-27 19:29:21.123400 -- AlientechLinkService processClient -- 
2025-05-27 19:29:21.124600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:29:21.128700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:29:41.319300 -- AlientechLinkService __construct -- 
2025-05-27 19:29:41.327500 -- AlientechLinkService processClient -- 
2025-05-27 19:29:41.328700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:29:41.339600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:30:01.881100 -- AlientechLinkService __construct -- 
2025-05-27 19:30:01.888700 -- AlientechLinkService processClient -- 
2025-05-27 19:30:01.890000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:30:01.900100 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:30:22.464500 -- AlientechLinkService __construct -- 
2025-05-27 19:30:22.472000 -- AlientechLinkService processClient -- 
2025-05-27 19:30:22.473100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:30:22.483000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:30:42.776700 -- AlientechLinkService __construct -- 
2025-05-27 19:30:42.781400 -- AlientechLinkService processClient -- 
2025-05-27 19:30:42.782600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:30:42.786800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:31:03.028900 -- AlientechLinkService __construct -- 
2025-05-27 19:31:03.033500 -- AlientechLinkService processClient -- 
2025-05-27 19:31:03.034700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:31:03.039000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:31:23.303400 -- AlientechLinkService __construct -- 
2025-05-27 19:31:23.308100 -- AlientechLinkService processClient -- 
2025-05-27 19:31:23.309200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:31:23.313500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:31:43.611000 -- AlientechLinkService __construct -- 
2025-05-27 19:31:43.618700 -- AlientechLinkService processClient -- 
2025-05-27 19:31:43.619800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:31:43.630400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:32:04.105700 -- AlientechLinkService __construct -- 
2025-05-27 19:32:04.113700 -- AlientechLinkService processClient -- 
2025-05-27 19:32:04.114900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:32:04.125200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:32:24.658000 -- AlientechLinkService __construct -- 
2025-05-27 19:32:24.665700 -- AlientechLinkService processClient -- 
2025-05-27 19:32:24.667200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:32:24.677300 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:32:44.881100 -- AlientechLinkService __construct -- 
2025-05-27 19:32:44.886000 -- AlientechLinkService processClient -- 
2025-05-27 19:32:44.887200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:32:44.891400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:33:05.280300 -- AlientechLinkService __construct -- 
2025-05-27 19:33:05.285000 -- AlientechLinkService processClient -- 
2025-05-27 19:33:05.286400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:33:05.290500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:33:25.443300 -- AlientechLinkService __construct -- 
2025-05-27 19:33:25.448100 -- AlientechLinkService processClient -- 
2025-05-27 19:33:25.449300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:33:25.453600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:33:45.985100 -- AlientechLinkService __construct -- 
2025-05-27 19:33:45.992400 -- AlientechLinkService processClient -- 
2025-05-27 19:33:45.993500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:33:46.003300 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:34:06.198400 -- AlientechLinkService __construct -- 
2025-05-27 19:34:06.205700 -- AlientechLinkService processClient -- 
2025-05-27 19:34:06.206900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:34:06.216800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:34:27.027800 -- AlientechLinkService __construct -- 
2025-05-27 19:34:27.035200 -- AlientechLinkService processClient -- 
2025-05-27 19:34:27.036500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:34:27.047000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:34:47.162900 -- AlientechLinkService __construct -- 
2025-05-27 19:34:47.168000 -- AlientechLinkService processClient -- 
2025-05-27 19:34:47.169200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:34:47.173500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:35:07.563500 -- AlientechLinkService __construct -- 
2025-05-27 19:35:07.568900 -- AlientechLinkService processClient -- 
2025-05-27 19:35:07.570100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:35:07.575000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:35:27.639800 -- AlientechLinkService __construct -- 
2025-05-27 19:35:27.645100 -- AlientechLinkService processClient -- 
2025-05-27 19:35:27.646200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:35:27.650500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:35:48.063500 -- AlientechLinkService __construct -- 
2025-05-27 19:35:48.070800 -- AlientechLinkService processClient -- 
2025-05-27 19:35:48.072000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:35:48.082600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:36:08.439200 -- AlientechLinkService __construct -- 
2025-05-27 19:36:08.447700 -- AlientechLinkService processClient -- 
2025-05-27 19:36:08.449000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:36:08.459200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:36:28.927900 -- AlientechLinkService __construct -- 
2025-05-27 19:36:28.935400 -- AlientechLinkService processClient -- 
2025-05-27 19:36:28.936600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:36:28.946600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:36:49.315500 -- AlientechLinkService __construct -- 
2025-05-27 19:36:49.320300 -- AlientechLinkService processClient -- 
2025-05-27 19:36:49.321400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:36:49.325600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:37:09.457400 -- AlientechLinkService __construct -- 
2025-05-27 19:37:09.462100 -- AlientechLinkService processClient -- 
2025-05-27 19:37:09.463300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:37:09.467300 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:37:29.699800 -- AlientechLinkService __construct -- 
2025-05-27 19:37:29.704900 -- AlientechLinkService processClient -- 
2025-05-27 19:37:29.706200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:37:29.710600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:37:49.925500 -- AlientechLinkService __construct -- 
2025-05-27 19:37:49.932800 -- AlientechLinkService processClient -- 
2025-05-27 19:37:49.933900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:37:49.944000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:38:10.285600 -- AlientechLinkService __construct -- 
2025-05-27 19:38:10.293000 -- AlientechLinkService processClient -- 
2025-05-27 19:38:10.294300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:38:10.304200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:38:30.752200 -- AlientechLinkService __construct -- 
2025-05-27 19:38:30.759400 -- AlientechLinkService processClient -- 
2025-05-27 19:38:30.760500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:38:30.770600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:38:51.106600 -- AlientechLinkService __construct -- 
2025-05-27 19:38:51.111200 -- AlientechLinkService processClient -- 
2025-05-27 19:38:51.112400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:38:51.116700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:39:11.340400 -- AlientechLinkService __construct -- 
2025-05-27 19:39:11.345100 -- AlientechLinkService processClient -- 
2025-05-27 19:39:11.346400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:39:11.350600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:39:31.305400 -- AlientechLinkService __construct -- 
2025-05-27 19:39:31.309900 -- AlientechLinkService processClient -- 
2025-05-27 19:39:31.311000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:39:31.315200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:39:51.705100 -- AlientechLinkService __construct -- 
2025-05-27 19:39:51.713000 -- AlientechLinkService processClient -- 
2025-05-27 19:39:51.714200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:39:51.724700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:40:11.939600 -- AlientechLinkService __construct -- 
2025-05-27 19:40:11.944800 -- AlientechLinkService processClient -- 
2025-05-27 19:40:11.945600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:40:11.951600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:40:32.480600 -- AlientechLinkService __construct -- 
2025-05-27 19:40:32.488000 -- AlientechLinkService processClient -- 
2025-05-27 19:40:32.489400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:40:32.499800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:40:52.981400 -- AlientechLinkService __construct -- 
2025-05-27 19:40:52.986200 -- AlientechLinkService processClient -- 
2025-05-27 19:40:52.987300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:40:52.991400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:41:13.045300 -- AlientechLinkService __construct -- 
2025-05-27 19:41:13.048800 -- AlientechLinkService processClient -- 
2025-05-27 19:41:13.049500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:41:13.052300 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:41:33.311800 -- AlientechLinkService __construct -- 
2025-05-27 19:41:33.316400 -- AlientechLinkService processClient -- 
2025-05-27 19:41:33.317600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:41:33.321700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:41:53.494600 -- AlientechLinkService __construct -- 
2025-05-27 19:41:53.502300 -- AlientechLinkService processClient -- 
2025-05-27 19:41:53.503500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:41:53.513600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:42:14.076300 -- AlientechLinkService __construct -- 
2025-05-27 19:42:14.083800 -- AlientechLinkService processClient -- 
2025-05-27 19:42:14.085000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:42:14.094900 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:42:34.662300 -- AlientechLinkService __construct -- 
2025-05-27 19:42:34.669800 -- AlientechLinkService processClient -- 
2025-05-27 19:42:34.671000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:42:34.680600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:42:55.088700 -- AlientechLinkService __construct -- 
2025-05-27 19:42:55.093400 -- AlientechLinkService processClient -- 
2025-05-27 19:42:55.094600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:42:55.098800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:43:15.212800 -- AlientechLinkService __construct -- 
2025-05-27 19:43:15.216000 -- AlientechLinkService processClient -- 
2025-05-27 19:43:15.216800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:43:15.219800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:43:35.326900 -- AlientechLinkService __construct -- 
2025-05-27 19:43:35.332100 -- AlientechLinkService processClient -- 
2025-05-27 19:43:35.333800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:43:35.338500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:43:55.590200 -- AlientechLinkService __construct -- 
2025-05-27 19:43:55.597500 -- AlientechLinkService processClient -- 
2025-05-27 19:43:55.598600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:43:55.608200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:44:15.976900 -- AlientechLinkService __construct -- 
2025-05-27 19:44:15.983300 -- AlientechLinkService processClient -- 
2025-05-27 19:44:15.984500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:44:15.995000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:44:36.389000 -- AlientechLinkService __construct -- 
2025-05-27 19:44:36.393400 -- AlientechLinkService processClient -- 
2025-05-27 19:44:36.394100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:44:36.399600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:44:56.877400 -- AlientechLinkService __construct -- 
2025-05-27 19:44:56.882400 -- AlientechLinkService processClient -- 
2025-05-27 19:44:56.883500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:44:56.888100 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:45:17.134400 -- AlientechLinkService __construct -- 
2025-05-27 19:45:17.139200 -- AlientechLinkService processClient -- 
2025-05-27 19:45:17.140300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:45:17.144400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:45:37.087700 -- AlientechLinkService __construct -- 
2025-05-27 19:45:37.092800 -- AlientechLinkService processClient -- 
2025-05-27 19:45:37.094000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:45:37.098800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:45:57.414600 -- AlientechLinkService __construct -- 
2025-05-27 19:45:57.421900 -- AlientechLinkService processClient -- 
2025-05-27 19:45:57.423200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:45:57.432800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:46:17.719000 -- AlientechLinkService __construct -- 
2025-05-27 19:46:17.726400 -- AlientechLinkService processClient -- 
2025-05-27 19:46:17.727600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:46:17.738100 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:46:38.358300 -- AlientechLinkService __construct -- 
2025-05-27 19:46:38.365400 -- AlientechLinkService processClient -- 
2025-05-27 19:46:38.366600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:46:38.376000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:46:58.674800 -- AlientechLinkService __construct -- 
2025-05-27 19:46:58.679400 -- AlientechLinkService processClient -- 
2025-05-27 19:46:58.680500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:46:58.684900 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:47:19.088900 -- AlientechLinkService __construct -- 
2025-05-27 19:47:19.093500 -- AlientechLinkService processClient -- 
2025-05-27 19:47:19.094700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:47:19.098800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:47:39.114200 -- AlientechLinkService __construct -- 
2025-05-27 19:47:39.118800 -- AlientechLinkService processClient -- 
2025-05-27 19:47:39.119900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:47:39.124400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:47:59.303900 -- AlientechLinkService __construct -- 
2025-05-27 19:47:59.311200 -- AlientechLinkService processClient -- 
2025-05-27 19:47:59.312300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:47:59.321900 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:48:19.942100 -- AlientechLinkService __construct -- 
2025-05-27 19:48:19.949800 -- AlientechLinkService processClient -- 
2025-05-27 19:48:19.950900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:48:19.960600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:48:40.470000 -- AlientechLinkService __construct -- 
2025-05-27 19:48:40.477600 -- AlientechLinkService processClient -- 
2025-05-27 19:48:40.478800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:48:40.488700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:49:00.696400 -- AlientechLinkService __construct -- 
2025-05-27 19:49:00.701200 -- AlientechLinkService processClient -- 
2025-05-27 19:49:00.702400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:49:00.706700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:49:20.931500 -- AlientechLinkService __construct -- 
2025-05-27 19:49:20.936100 -- AlientechLinkService processClient -- 
2025-05-27 19:49:20.937300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:49:20.941300 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:49:41.130500 -- AlientechLinkService __construct -- 
2025-05-27 19:49:41.135900 -- AlientechLinkService processClient -- 
2025-05-27 19:49:41.137100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:49:41.141600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:50:01.311100 -- AlientechLinkService __construct -- 
2025-05-27 19:50:01.318200 -- AlientechLinkService processClient -- 
2025-05-27 19:50:01.319400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:50:01.329700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:50:21.863900 -- AlientechLinkService __construct -- 
2025-05-27 19:50:21.871200 -- AlientechLinkService processClient -- 
2025-05-27 19:50:21.872400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:50:21.882100 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:50:42.315200 -- AlientechLinkService __construct -- 
2025-05-27 19:50:42.322500 -- AlientechLinkService processClient -- 
2025-05-27 19:50:42.323700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:50:42.334000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:51:02.640500 -- AlientechLinkService __construct -- 
2025-05-27 19:51:02.644500 -- AlientechLinkService processClient -- 
2025-05-27 19:51:02.645500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:51:02.649400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:51:23.015300 -- AlientechLinkService __construct -- 
2025-05-27 19:51:23.020000 -- AlientechLinkService processClient -- 
2025-05-27 19:51:23.021400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:51:23.025700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:51:43.076200 -- AlientechLinkService __construct -- 
2025-05-27 19:51:43.081000 -- AlientechLinkService processClient -- 
2025-05-27 19:51:43.082100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:51:43.086400 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:52:03.381900 -- AlientechLinkService __construct -- 
2025-05-27 19:52:03.389300 -- AlientechLinkService processClient -- 
2025-05-27 19:52:03.390400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:52:03.400600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:52:23.912700 -- AlientechLinkService __construct -- 
2025-05-27 19:52:23.920300 -- AlientechLinkService processClient -- 
2025-05-27 19:52:23.921500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:52:23.931500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:52:44.361100 -- AlientechLinkService __construct -- 
2025-05-27 19:52:44.369000 -- AlientechLinkService processClient -- 
2025-05-27 19:52:44.370200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:52:44.380100 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:53:04.711800 -- AlientechLinkService __construct -- 
2025-05-27 19:53:04.716500 -- AlientechLinkService processClient -- 
2025-05-27 19:53:04.717600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:53:04.721800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:53:24.683600 -- AlientechLinkService __construct -- 
2025-05-27 19:53:24.687000 -- AlientechLinkService processClient -- 
2025-05-27 19:53:24.687800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:53:24.690500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:53:44.879400 -- AlientechLinkService __construct -- 
2025-05-27 19:53:44.884900 -- AlientechLinkService processClient -- 
2025-05-27 19:53:44.886300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:53:44.891000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:54:05.074700 -- AlientechLinkService __construct -- 
2025-05-27 19:54:05.079200 -- AlientechLinkService processClient -- 
2025-05-27 19:54:05.079900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:54:05.085900 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:54:25.434800 -- AlientechLinkService __construct -- 
2025-05-27 19:54:25.442100 -- AlientechLinkService processClient -- 
2025-05-27 19:54:25.443200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:54:25.452800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:54:45.957800 -- AlientechLinkService __construct -- 
2025-05-27 19:54:45.965100 -- AlientechLinkService processClient -- 
2025-05-27 19:54:45.966300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:54:45.976000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:55:06.134200 -- AlientechLinkService __construct -- 
2025-05-27 19:55:06.138900 -- AlientechLinkService processClient -- 
2025-05-27 19:55:06.140000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:55:06.144200 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:55:26.312800 -- AlientechLinkService __construct -- 
2025-05-27 19:55:26.315700 -- AlientechLinkService processClient -- 
2025-05-27 19:55:26.316500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:55:26.319800 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:55:46.572600 -- AlientechLinkService __construct -- 
2025-05-27 19:55:46.577200 -- AlientechLinkService processClient -- 
2025-05-27 19:55:46.578400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:55:46.582500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:56:06.851500 -- AlientechLinkService __construct -- 
2025-05-27 19:56:06.859200 -- AlientechLinkService processClient -- 
2025-05-27 19:56:06.860300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:56:06.870300 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:56:27.188700 -- AlientechLinkService __construct -- 
2025-05-27 19:56:27.196200 -- AlientechLinkService processClient -- 
2025-05-27 19:56:27.197300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:56:27.207100 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:56:47.714300 -- AlientechLinkService __construct -- 
2025-05-27 19:56:47.721900 -- AlientechLinkService processClient -- 
2025-05-27 19:56:47.723000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:56:47.732600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:57:07.921500 -- AlientechLinkService __construct -- 
2025-05-27 19:57:07.926200 -- AlientechLinkService processClient -- 
2025-05-27 19:57:07.927400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:57:07.931700 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:57:28.186600 -- AlientechLinkService __construct -- 
2025-05-27 19:57:28.191300 -- AlientechLinkService processClient -- 
2025-05-27 19:57:28.192500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:57:28.196600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:57:48.234200 -- AlientechLinkService __construct -- 
2025-05-27 19:57:48.239000 -- AlientechLinkService processClient -- 
2025-05-27 19:57:48.240100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:57:48.244300 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:58:08.499500 -- AlientechLinkService __construct -- 
2025-05-27 19:58:08.506900 -- AlientechLinkService processClient -- 
2025-05-27 19:58:08.508000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:58:08.517600 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:58:28.765900 -- AlientechLinkService __construct -- 
2025-05-27 19:58:28.770300 -- AlientechLinkService processClient -- 
2025-05-27 19:58:28.771000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:58:28.777000 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:58:49.407500 -- AlientechLinkService __construct -- 
2025-05-27 19:58:49.414900 -- AlientechLinkService processClient -- 
2025-05-27 19:58:49.416100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:58:49.426500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:59:09.557400 -- AlientechLinkService __construct -- 
2025-05-27 19:59:09.562200 -- AlientechLinkService processClient -- 
2025-05-27 19:59:09.563400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:59:09.567500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:59:29.854300 -- AlientechLinkService __construct -- 
2025-05-27 19:59:29.859100 -- AlientechLinkService processClient -- 
2025-05-27 19:59:29.860300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:59:29.864500 -- AlientechLinkService initAccessToken -- 
2025-05-27 19:59:49.888000 -- AlientechLinkService __construct -- 
2025-05-27 19:59:49.893500 -- AlientechLinkService processClient -- 
2025-05-27 19:59:49.894700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 19:59:49.899000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:00:10.142400 -- AlientechLinkService __construct -- 
2025-05-27 20:00:10.149600 -- AlientechLinkService processClient -- 
2025-05-27 20:00:10.151100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:00:10.161100 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:00:30.488700 -- AlientechLinkService __construct -- 
2025-05-27 20:00:30.496000 -- AlientechLinkService processClient -- 
2025-05-27 20:00:30.497100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:00:30.506600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:00:51.012600 -- AlientechLinkService __construct -- 
2025-05-27 20:00:51.020200 -- AlientechLinkService processClient -- 
2025-05-27 20:00:51.021300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:00:51.031000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:01:11.201500 -- AlientechLinkService __construct -- 
2025-05-27 20:01:11.206100 -- AlientechLinkService processClient -- 
2025-05-27 20:01:11.207200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:01:11.211500 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:01:31.529400 -- AlientechLinkService __construct -- 
2025-05-27 20:01:31.534100 -- AlientechLinkService processClient -- 
2025-05-27 20:01:31.535300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:01:31.539500 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:01:51.539300 -- AlientechLinkService __construct -- 
2025-05-27 20:01:51.544100 -- AlientechLinkService processClient -- 
2025-05-27 20:01:51.545200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:01:51.549300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:02:11.764100 -- AlientechLinkService __construct -- 
2025-05-27 20:02:11.771500 -- AlientechLinkService processClient -- 
2025-05-27 20:02:11.772600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:02:11.782400 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:02:32.130000 -- AlientechLinkService __construct -- 
2025-05-27 20:02:32.138700 -- AlientechLinkService processClient -- 
2025-05-27 20:02:32.140300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:02:32.151800 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:02:52.662600 -- AlientechLinkService __construct -- 
2025-05-27 20:02:52.670000 -- AlientechLinkService processClient -- 
2025-05-27 20:02:52.671200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:02:52.681600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:03:12.984000 -- AlientechLinkService __construct -- 
2025-05-27 20:03:12.989100 -- AlientechLinkService processClient -- 
2025-05-27 20:03:12.990300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:03:12.994600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:03:33.196900 -- AlientechLinkService __construct -- 
2025-05-27 20:03:33.201800 -- AlientechLinkService processClient -- 
2025-05-27 20:03:33.202900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:03:33.207000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:03:53.139000 -- AlientechLinkService __construct -- 
2025-05-27 20:03:53.143600 -- AlientechLinkService processClient -- 
2025-05-27 20:03:53.144700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:03:53.148900 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:04:13.514600 -- AlientechLinkService __construct -- 
2025-05-27 20:04:13.523400 -- AlientechLinkService processClient -- 
2025-05-27 20:04:13.524700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:04:13.538600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:04:33.733100 -- AlientechLinkService __construct -- 
2025-05-27 20:04:33.740300 -- AlientechLinkService processClient -- 
2025-05-27 20:04:33.741300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:04:33.751500 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:04:54.414800 -- AlientechLinkService __construct -- 
2025-05-27 20:04:54.421900 -- AlientechLinkService processClient -- 
2025-05-27 20:04:54.423000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:04:54.432800 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:05:14.549000 -- AlientechLinkService __construct -- 
2025-05-27 20:05:14.553600 -- AlientechLinkService processClient -- 
2025-05-27 20:05:14.554700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:05:14.558900 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:05:34.955100 -- AlientechLinkService __construct -- 
2025-05-27 20:05:34.960000 -- AlientechLinkService processClient -- 
2025-05-27 20:05:34.961300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:05:34.965700 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:05:54.860500 -- AlientechLinkService __construct -- 
2025-05-27 20:05:54.865100 -- AlientechLinkService processClient -- 
2025-05-27 20:05:54.866200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:05:54.870200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:06:15.207500 -- AlientechLinkService __construct -- 
2025-05-27 20:06:15.214700 -- AlientechLinkService processClient -- 
2025-05-27 20:06:15.215900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:06:15.225800 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:06:35.553500 -- AlientechLinkService __construct -- 
2025-05-27 20:06:35.560900 -- AlientechLinkService processClient -- 
2025-05-27 20:06:35.562100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:06:35.572000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:06:56.071200 -- AlientechLinkService __construct -- 
2025-05-27 20:06:56.078300 -- AlientechLinkService processClient -- 
2025-05-27 20:06:56.079700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:06:56.090000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:07:16.261100 -- AlientechLinkService __construct -- 
2025-05-27 20:07:16.265700 -- AlientechLinkService processClient -- 
2025-05-27 20:07:16.267100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:07:16.271200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:07:36.538500 -- AlientechLinkService __construct -- 
2025-05-27 20:07:36.543100 -- AlientechLinkService processClient -- 
2025-05-27 20:07:36.544200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:07:36.548300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:07:56.585400 -- AlientechLinkService __construct -- 
2025-05-27 20:07:56.590100 -- AlientechLinkService processClient -- 
2025-05-27 20:07:56.591200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:07:56.595400 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:08:16.840600 -- AlientechLinkService __construct -- 
2025-05-27 20:08:16.848200 -- AlientechLinkService processClient -- 
2025-05-27 20:08:16.849300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:08:16.858900 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:08:37.177300 -- AlientechLinkService __construct -- 
2025-05-27 20:08:37.184300 -- AlientechLinkService processClient -- 
2025-05-27 20:08:37.185600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:08:37.195900 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:08:57.722300 -- AlientechLinkService __construct -- 
2025-05-27 20:08:57.729900 -- AlientechLinkService processClient -- 
2025-05-27 20:08:57.731000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:08:57.740900 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:09:17.904100 -- AlientechLinkService __construct -- 
2025-05-27 20:09:17.908600 -- AlientechLinkService processClient -- 
2025-05-27 20:09:17.909800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:09:17.914000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:09:38.132900 -- AlientechLinkService __construct -- 
2025-05-27 20:09:38.137800 -- AlientechLinkService processClient -- 
2025-05-27 20:09:38.138900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:09:38.143000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:09:58.188300 -- AlientechLinkService __construct -- 
2025-05-27 20:09:58.192800 -- AlientechLinkService processClient -- 
2025-05-27 20:09:58.193900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:09:58.197900 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:10:18.457700 -- AlientechLinkService __construct -- 
2025-05-27 20:10:18.464900 -- AlientechLinkService processClient -- 
2025-05-27 20:10:18.466000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:10:18.475800 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:10:38.816700 -- AlientechLinkService __construct -- 
2025-05-27 20:10:38.824600 -- AlientechLinkService processClient -- 
2025-05-27 20:10:38.825900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:10:38.835900 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:10:59.326700 -- AlientechLinkService __construct -- 
2025-05-27 20:10:59.334200 -- AlientechLinkService processClient -- 
2025-05-27 20:10:59.335400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:10:59.345600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:11:19.515300 -- AlientechLinkService __construct -- 
2025-05-27 20:11:19.520600 -- AlientechLinkService processClient -- 
2025-05-27 20:11:19.521900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:11:19.526400 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:11:39.726500 -- AlientechLinkService __construct -- 
2025-05-27 20:11:39.731100 -- AlientechLinkService processClient -- 
2025-05-27 20:11:39.732200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:11:39.736400 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:11:59.787600 -- AlientechLinkService __construct -- 
2025-05-27 20:11:59.792300 -- AlientechLinkService processClient -- 
2025-05-27 20:11:59.793400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:11:59.797500 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:12:20.055300 -- AlientechLinkService __construct -- 
2025-05-27 20:12:20.062700 -- AlientechLinkService processClient -- 
2025-05-27 20:12:20.063800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:12:20.073600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:12:40.399900 -- AlientechLinkService __construct -- 
2025-05-27 20:12:40.407300 -- AlientechLinkService processClient -- 
2025-05-27 20:12:40.408400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:12:40.418300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:13:00.900800 -- AlientechLinkService __construct -- 
2025-05-27 20:13:00.908100 -- AlientechLinkService processClient -- 
2025-05-27 20:13:00.909200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:13:00.919000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:13:21.100700 -- AlientechLinkService __construct -- 
2025-05-27 20:13:21.105300 -- AlientechLinkService processClient -- 
2025-05-27 20:13:21.106500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:13:21.110600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:13:41.461900 -- AlientechLinkService __construct -- 
2025-05-27 20:13:41.466900 -- AlientechLinkService processClient -- 
2025-05-27 20:13:41.468000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:13:41.472300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:14:01.396800 -- AlientechLinkService __construct -- 
2025-05-27 20:14:01.401500 -- AlientechLinkService processClient -- 
2025-05-27 20:14:01.402600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:14:01.407000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:14:21.597900 -- AlientechLinkService __construct -- 
2025-05-27 20:14:21.605200 -- AlientechLinkService processClient -- 
2025-05-27 20:14:21.606300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:14:21.616200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:14:41.850400 -- AlientechLinkService __construct -- 
2025-05-27 20:14:41.858000 -- AlientechLinkService processClient -- 
2025-05-27 20:14:41.859200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:14:41.869200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:15:02.394000 -- AlientechLinkService __construct -- 
2025-05-27 20:15:02.401300 -- AlientechLinkService processClient -- 
2025-05-27 20:15:02.402400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:15:02.412100 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:15:22.553100 -- AlientechLinkService __construct -- 
2025-05-27 20:15:22.558000 -- AlientechLinkService processClient -- 
2025-05-27 20:15:22.559100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:15:22.563300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:15:42.786800 -- AlientechLinkService __construct -- 
2025-05-27 20:15:42.791600 -- AlientechLinkService processClient -- 
2025-05-27 20:15:42.792700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:15:42.797400 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:16:02.845700 -- AlientechLinkService __construct -- 
2025-05-27 20:16:02.850900 -- AlientechLinkService processClient -- 
2025-05-27 20:16:02.852100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:16:02.856600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:16:23.111000 -- AlientechLinkService __construct -- 
2025-05-27 20:16:23.118400 -- AlientechLinkService processClient -- 
2025-05-27 20:16:23.119700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:16:23.130000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:16:43.467400 -- AlientechLinkService __construct -- 
2025-05-27 20:16:43.475200 -- AlientechLinkService processClient -- 
2025-05-27 20:16:43.476300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:16:43.486400 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:17:03.995000 -- AlientechLinkService __construct -- 
2025-05-27 20:17:04.002800 -- AlientechLinkService processClient -- 
2025-05-27 20:17:04.004000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:17:04.015200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:17:24.175700 -- AlientechLinkService __construct -- 
2025-05-27 20:17:24.180500 -- AlientechLinkService processClient -- 
2025-05-27 20:17:24.181600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:17:24.186200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:17:44.403500 -- AlientechLinkService __construct -- 
2025-05-27 20:17:44.408100 -- AlientechLinkService processClient -- 
2025-05-27 20:17:44.409300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:17:44.413600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:18:04.342300 -- AlientechLinkService __construct -- 
2025-05-27 20:18:04.347100 -- AlientechLinkService processClient -- 
2025-05-27 20:18:04.348200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:18:04.352300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:18:24.642500 -- AlientechLinkService __construct -- 
2025-05-27 20:18:24.649800 -- AlientechLinkService processClient -- 
2025-05-27 20:18:24.650900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:18:24.660600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:18:44.928300 -- AlientechLinkService __construct -- 
2025-05-27 20:18:44.935700 -- AlientechLinkService processClient -- 
2025-05-27 20:18:44.937000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:18:44.947000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:19:05.444500 -- AlientechLinkService __construct -- 
2025-05-27 20:19:05.452300 -- AlientechLinkService processClient -- 
2025-05-27 20:19:05.453400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:19:05.463300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:19:25.606700 -- AlientechLinkService __construct -- 
2025-05-27 20:19:25.611300 -- AlientechLinkService processClient -- 
2025-05-27 20:19:25.612500 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:19:25.616900 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:19:45.855800 -- AlientechLinkService __construct -- 
2025-05-27 20:19:45.860500 -- AlientechLinkService processClient -- 
2025-05-27 20:19:45.861700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:19:45.866000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:20:05.879400 -- AlientechLinkService __construct -- 
2025-05-27 20:20:05.884000 -- AlientechLinkService processClient -- 
2025-05-27 20:20:05.885100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:20:05.889500 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:20:26.130400 -- AlientechLinkService __construct -- 
2025-05-27 20:20:26.135600 -- AlientechLinkService processClient -- 
2025-05-27 20:20:26.136400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:20:26.142500 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:20:46.427100 -- AlientechLinkService __construct -- 
2025-05-27 20:20:46.434700 -- AlientechLinkService processClient -- 
2025-05-27 20:20:46.435800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:20:46.445500 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:21:06.970900 -- AlientechLinkService __construct -- 
2025-05-27 20:21:06.978200 -- AlientechLinkService processClient -- 
2025-05-27 20:21:06.979300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:21:06.989300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:21:27.158700 -- AlientechLinkService __construct -- 
2025-05-27 20:21:27.162400 -- AlientechLinkService processClient -- 
2025-05-27 20:21:27.163400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:21:27.166200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:21:47.638400 -- AlientechLinkService __construct -- 
2025-05-27 20:21:47.643000 -- AlientechLinkService processClient -- 
2025-05-27 20:21:47.644100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:21:47.648300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:22:07.572900 -- AlientechLinkService __construct -- 
2025-05-27 20:22:07.577700 -- AlientechLinkService processClient -- 
2025-05-27 20:22:07.579000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:22:07.583200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:22:27.869400 -- AlientechLinkService __construct -- 
2025-05-27 20:22:27.873700 -- AlientechLinkService processClient -- 
2025-05-27 20:22:27.874400 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:22:27.880200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:22:48.443300 -- AlientechLinkService __construct -- 
2025-05-27 20:22:48.450500 -- AlientechLinkService processClient -- 
2025-05-27 20:22:48.451700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:22:48.461200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:23:09.017700 -- AlientechLinkService __construct -- 
2025-05-27 20:23:09.025500 -- AlientechLinkService processClient -- 
2025-05-27 20:23:09.026900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:23:09.036800 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:23:29.145300 -- AlientechLinkService __construct -- 
2025-05-27 20:23:29.150100 -- AlientechLinkService processClient -- 
2025-05-27 20:23:29.151300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:23:29.155600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:23:49.498700 -- AlientechLinkService __construct -- 
2025-05-27 20:23:49.503800 -- AlientechLinkService processClient -- 
2025-05-27 20:23:49.504900 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:23:49.509300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:24:09.510400 -- AlientechLinkService __construct -- 
2025-05-27 20:24:09.515400 -- AlientechLinkService processClient -- 
2025-05-27 20:24:09.516600 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:24:09.520700 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:24:29.708400 -- AlientechLinkService __construct -- 
2025-05-27 20:24:29.715900 -- AlientechLinkService processClient -- 
2025-05-27 20:24:29.717100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:24:29.727000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:24:50.113600 -- AlientechLinkService __construct -- 
2025-05-27 20:24:50.121100 -- AlientechLinkService processClient -- 
2025-05-27 20:24:50.122300 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:24:50.132600 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:25:10.695600 -- AlientechLinkService __construct -- 
2025-05-27 20:25:10.703000 -- AlientechLinkService processClient -- 
2025-05-27 20:25:10.704200 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:25:10.714200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:25:31.003400 -- AlientechLinkService __construct -- 
2025-05-27 20:25:31.008500 -- AlientechLinkService processClient -- 
2025-05-27 20:25:31.009700 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:25:31.014400 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:25:51.333000 -- AlientechLinkService __construct -- 
2025-05-27 20:25:51.337800 -- AlientechLinkService processClient -- 
2025-05-27 20:25:51.339000 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:25:51.344100 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:26:11.259400 -- AlientechLinkService __construct -- 
2025-05-27 20:26:11.264000 -- AlientechLinkService processClient -- 
2025-05-27 20:26:11.265100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:26:11.269300 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:26:31.590600 -- AlientechLinkService __construct -- 
2025-05-27 20:26:31.599900 -- AlientechLinkService processClient -- 
2025-05-27 20:26:31.601800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:26:31.613000 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:26:51.829700 -- AlientechLinkService __construct -- 
2025-05-27 20:26:51.837000 -- AlientechLinkService processClient -- 
2025-05-27 20:26:51.838100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:26:51.848500 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:27:12.348200 -- AlientechLinkService __construct -- 
2025-05-27 20:27:12.355900 -- AlientechLinkService processClient -- 
2025-05-27 20:27:12.357100 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:27:12.367200 -- AlientechLinkService initAccessToken -- 
2025-05-27 20:27:32.622700 -- AlientechLinkService __construct -- 
2025-05-27 20:27:32.627600 -- AlientechLinkService processClient -- 
2025-05-27 20:27:32.628800 -- AlientechLinkService is_null($this->client) -- 
2025-05-27 20:27:32.633100 -- AlientechLinkService initAccessToken -- 
2025-05-29 16:07:54.744300 -- AlientechLinkService __construct -- 
2025-05-29 16:07:54.751800 -- AlientechLinkService processClient -- 
2025-05-29 16:07:54.753100 -- AlientechLinkService is_null($this->client) -- 
2025-05-29 16:07:54.760900 -- AlientechLinkService initAccessToken -- 
2025-05-29 16:07:54.762500 -- iR8V9-KAuaE -- 
2025-05-29 16:36:25.521000 -- AlientechLinkService __construct -- 
2025-05-29 16:36:25.528200 -- AlientechLinkService processClient -- 
2025-05-29 16:36:25.529900 -- AlientechLinkService is_null($this->client) -- 
2025-05-29 16:36:25.537500 -- AlientechLinkService initAccessToken -- 
2025-05-29 16:36:25.539100 -- iR8V9-KAuaE -- 
2025-05-31 16:29:25.992100 -- AlientechLinkService __construct -- 
2025-05-31 16:29:26.002900 -- AlientechLinkService processClient -- 
2025-05-31 16:29:26.004200 -- AlientechLinkService is_null($this->client) -- 
2025-05-31 16:29:26.013800 -- AlientechLinkService initAccessToken -- 
2025-05-31 16:29:26.015600 -- iR8V9-KAuaE -- 
2025-05-31 16:29:26.052200 -- AlientechProjectService __construct -- 
2025-05-31 16:29:26.066400 -- FileSlotService __construct -- 
2025-05-31 16:29:26.074200 -- FileSlotService __construct -- 
2025-05-31 16:29:26.075300 -- AsyncOperationService __construct -- 
2025-05-31 16:29:26.095800 -- Kess3Service __construct -- 
2025-05-31 16:29:26.097100 -- Kess3Service startEncoding -- 
2025-05-31 16:29:26.098300 -- AsyncOperationService startOperationEncode -- 
2025-05-31 16:29:26.258800 -- AsyncOperationService startOperationEncode no_files -- 
2025-05-31 16:29:26.259900 -- no_AsyncOperationDto -- 
2025-05-31 16:54:16.578600 -- AlientechLinkService __construct -- 
2025-05-31 16:54:16.585400 -- AlientechLinkService processClient -- 
2025-05-31 16:54:16.586600 -- AlientechLinkService is_null($this->client) -- 
2025-05-31 16:54:16.592700 -- AlientechLinkService initAccessToken -- 
2025-05-31 16:54:16.594100 -- iR8V9-KAuaE -- 
2025-05-31 16:54:16.612800 -- AlientechProjectService __construct -- 
2025-05-31 16:54:16.624700 -- FileSlotService __construct -- 
2025-05-31 16:54:16.629700 -- FileSlotService __construct -- 
2025-05-31 16:54:16.630900 -- AsyncOperationService __construct -- 
2025-05-31 16:54:16.650100 -- ApiController actionKess3Decoded -- 
2025-05-31 16:54:16.651200 -- AsyncOperationService processDataToOperation -- 
2025-05-31 16:54:16.745200 --  --- $log->save() --  ---614 -- 
2025-05-31 16:54:16.746400 -- AsyncOperationService createOperationDtoByData -- 
2025-05-31 16:54:16.747400 -- AsyncOperationRepository buildAsyncOperationByData -- 
2025-05-31 16:54:16.752300 -- AsyncOperationService processCompletedOperation -- 
2025-05-31 16:54:16.753500 -- AsyncOperationRepository updateAsyncOperation -- 
