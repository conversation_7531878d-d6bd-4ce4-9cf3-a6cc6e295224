<?php

namespace tests\integration;

use common\chip\event\core\EventInterface;
use common\chip\event\EventDispatcher;
use common\chip\event\EventHandler;
use common\chip\event\handlers\AlientechFileHandler;
use common\chip\event\handlers\AutopackFileHandler;
use common\chip\event\handlers\FileEventHandler;
use common\chip\event\handlers\OptionsRequestedHandler;
use common\chip\event\handlers\ProjectCreationHandler;
use common\chip\event\handlers\ProjectSaveHandler;
use common\chip\event\project\ProjectCreatedEvent;
use common\chip\event\project\ProjectEventFactory;
use common\chip\event\repositories\EventRepository;
use common\chip\externalIntegrations\kess3\Application\Handlers\StartEncodingHandler;
use common\chip\externalIntegrations\kess3\Infrastructure\EventHandler\DecodingEventHandler;
use common\chip\notification\handlers\UniversalNotificationHandler;
use common\models\EventLog;
use common\models\Notification;
use common\models\ProjectFiles;
use common\models\Projects;
use common\models\User;
use PHPUnit\Framework\TestCase;
use Yii;

/**
 * Интеграционные тесты для системы уведомлений
 * @property ProjectFiles|null $testFile
 */
class NotificationSystemTest extends TestCase
{
    /**
     * @var EventDispatcher
     */
    protected $eventDispatcher;
    
    /**
     * @var EventRepository
     */
    protected $eventRepository;
    
    /**
     * @var ProjectEventFactory
     */
    protected $eventFactory;
    
    /**
     * @var Projects
     */
    protected $testProject;
    protected $testFile;

    /**
     * @var User
     */
    protected $testUser;
    protected \common\chip\project\interfaces\CreateProjectFormInterface $modelForm;

    /**
     * Настройка перед каждым тестом
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Создаем репозиторий событий
        $this->eventRepository = new EventRepository();
        
        // Создаем диспетчер событий
        $this->eventDispatcher = new EventDispatcher($this->eventRepository);
//        $this->eventDispatcher->registerHandler(new OptionsRequestedHandler());
        $this->eventDispatcher->registerHandler(new ProjectCreationHandler());
        $this->eventDispatcher->registerHandler(Yii::$container->get(FileEventHandler::class));
        $this->eventDispatcher->registerHandler(Yii::$container->get(AlientechFileHandler::class));
        $this->eventDispatcher->registerHandler(Yii::$container->get(AutopackFileHandler::class));
        $this->eventDispatcher->registerHandler(Yii::$container->get(UniversalNotificationHandler::class));
        $this->eventDispatcher->registerHandler(Yii::$container->get(DecodingEventHandler::class));
        // Создаем фабрику событий
        $this->eventFactory = new ProjectEventFactory();
        
        // Находим тестового пользователя
        $this->testUser = User::find()->where(['id' => 4])->one();
        Yii::$app->user->setIdentity($this->testUser);
        if (!$this->testUser) {
            $this->markTestSkipped('Не найден тестовый пользователь');
        }
        $service = new \common\chip\project\services\CreateProjectService();
        $this->modelForm = $service->getModelForm();
        $this->modelForm->load($data = [
            'vehicle_id' => '1',
            'brand_id' => '5',
            'model_id' => '35',
            'generation_id' => '52',
            'engine_id' => '285',
            'timeframe' => '4',
            'master' => '0',
            'ecu_id' => '328',
            'readmethod_id' => '59', // 60 - мастер, 59 - слейв бенч
            'tarif_id' => null,
            'gearbox_id' => '15',
            'year' => '2005',
            'stage_id' => 2,
            'on_dyno' => '1',
            'vin_num' => 'vin123123123123',
            'comments' => 'comments',
            'client_name' => '',
            'registration_num' => 'ax1234ax',
            'engine_hp' => '160',
            'engine_kwt' => '120',
            'client_1c' => '',
            'order_1c' => '',
            'created_by' => null,
            'soft_num' => '09780978787878',
            'custom_ecu' => 'custom ecu',
            'hard_num' => '23414314124124',
            'additions' => [
                2 => [
                    'ecu_addition_id' => '2289',
                    'addition_id' => '2',
                    'comment' => 'dpf error'
                ],
                3 => [
                    'ecu_addition_id' => '5498',
                    'addition_id' => '3',
                    'comment' => 'egr error'
                ],
                4 => [
                    'ecu_addition_id' => '8664',
                    'comment' => ''
                ],
                'request' => [
                    'dtc' => 'request error',
                    'comment' => ', CAT Removal

, Speed Limiter Removal, Original file Request'
                ]
            ],
            'file' => [
                0 => 'xbWLU96oozNQ'
            ],
            'toolsArray' => [
                1 => 'Master',
                0 => 'Slave'
            ],
            'readonlyOptions' => [],
            'requestAdditions' => [
                8 => [
                    'addition_id' => '8'
                ],
                9 => [
                    'addition_id' => '9'
                ],
                10 => [
                    'addition_id' => '10'
                ]
            ]
        ], '');
        $this->assertTrue($this->modelForm->validate(), 'Не удалось создать тестовую форму проекта: ' . json_encode($this->modelForm->errors));
        try {
//            $id = $service->run($this->modelForm);
        } catch (\Exception $e) {
            $this->markTestSkipped('Не удалось создать тестовый проект: ' . $e->getMessage());
        }

//        $repository = new ProjectRepository();
//        $this->testProject = Projects::findOne(237);
//        $this->testFile = ProjectFiles::findOne(341);
    }
    
    /**
     * Очистка после каждого теста
     */
    protected function tearDown(): void
    {
        // Удаляем тестовый проект, если он был создан
        if ($this->testProject && $this->testProject->id) {
//            $this->testProject->delete();
        }
        
        parent::tearDown();
    }

    protected function AlientechInit(): void
    {
        $this->operation5StartedResponce = '{"guid":"b4452b4c-c757-472a-8fee-383482d9236a","clientApplicationGUID":"c7f43304-624b-42b9-99f8-9d36c9f234d3","asyncOperationType":5,"slotGUID":"f03d77ee-0976-41d5-a958-173277cf3235","status":0,"isCompleted":false,"recommendedPollingInterval":5,"startedOn":"2025-05-23T13:15:32.9210864Z","completedOn":null,"duration":"00:00:00.0043283","isSuccessful":false,"hasFailed":false,"result":null,"error":null,"additionalInfo":{},"userInfo":null,"callbackURL":null}';

        $this->operation5CompletedResponce = '{"GUID":"b4452b4c-c757-472a-8fee-383482d9236a","ClientApplicationGUID":"c7f43304-624b-42b9-99f8-9d36c9f234d3","AsyncOperationType":5,"SlotGUID":"f03d77ee-0976-41d5-a958-173277cf3235","Status":1,"IsCompleted":true,"RecommendedPollingInterval":5,"StartedOn":"2025-05-23T13:15:32.92","CompletedOn":"2025-05-23T13:15:36.0055904Z","Duration":"00:00:03.0855904","IsSuccessful":true,"HasFailed":false,"Result":{"kess3FileSlotGUID":"f03d77ee-0976-41d5-a958-173277cf3235","Name":"VW_JETTA_2,0_TDI_2012_EDC17CP14_BC9388OX_ORI.","ReadFileURL":null,"kess3Mode":"BootBench","idFileURL":"https://encodingapi.alientech.to/api/kess3/file-slots/f03d77ee-0976-41d5-a958-173277cf3235/files/?fileType=ID","obdDecodedFileURL":null,"BootBenchComponents":[{"Type":"EEPROM","DecodedFileURL":"https://encodingapi.alientech.to/api/kess3/file-slots/f03d77ee-0976-41d5-a958-173277cf3235/files/?fileType=BootBenchDecodedEEPROM"},{"Type":"Flash","DecodedFileURL":"https://encodingapi.alientech.to/api/kess3/file-slots/f03d77ee-0976-41d5-a958-173277cf3235/files/?fileType=BootBenchDecodedFlash"},{"Type":"Micro","DecodedFileURL":"https://encodingapi.alientech.to/api/kess3/file-slots/f03d77ee-0976-41d5-a958-173277cf3235/files/?fileType=BootBenchDecodedMicro"}],"WasOriginalFileFoundThroughVirtualReading":false,"IsOriginalFileNeeded":false,"WillChecksumBeCorrected":false,"IsChecksumCorrectionNeeded":false,"IsCVNCorrectionPossible":false,"Information":{"Family":"BOSCH EDC17CP14 TC1796 VAG","PRT":"716","HW":"","SW":"","VIN":"","SW Ver.":"","Spare":"","Engine":"","HW Ver.":"","SW upg.":"","System":"","SN":"0400897E2620","Customer Code":"CR253","Group Code":"CL365","PRT Version":"3.053 - 152.002","Recognized":"Y","Mode":"BootBench","Protocol":"716"}},"Error":null,"AdditionalInfo":{},"UserInfo":null,"CallbackURL":"https://chiptuning-ms.business/api/kess3-decoded","JobRequestGUID":"617e6c83-d54b-1b8d-1d59-af04f01fd433"}';
    }

    /**
     * Создает тестовый обработчик событий
     * 
     * @param bool $canHandle Может ли обработчик обрабатывать события
     * @param mixed $returnValue Значение, возвращаемое обработчиком
     * @return EventHandler
     */
    protected function createTestHandler(bool $canHandle = true, $returnValue = null): EventHandler
    {
        $handler = $this->getMockBuilder(EventHandler::class)
            ->getMock();
        
        $handler->method('canHandle')
            ->willReturn($canHandle);
        
        return $handler;
    }
    
    /**
     * Создает событие создания проекта
     * 
     * @return ProjectCreatedEvent
     */
    protected function createProjectCreatedEvent(): ProjectCreatedEvent
    {
        return $this->eventFactory->createProjectCreatedEvent($this->testProject);
    }
    
    /**
     * Проверяет, что событие было записано в журнал
     * 
     * @param EventInterface $event Событие
     * @param string $entityType Тип сущности
     * @param int $entityId ID сущности
     * @return EventLog|null
     */
    protected function assertEventLogged(EventInterface $event, string $entityType, int $entityId): ?EventLog
    {
        $eventLog = EventLog::find()
            ->where([
                'event_type' => $event->getType(),
                'entity_type' => $entityType,
                'entity_id' => $entityId,
            ])
            ->orderBy(['id' => SORT_DESC])
            ->one();
        
        $this->assertNotNull($eventLog, 'Событие не было записано в журнал');
        return $eventLog;
    }
    
    /**
     * Проверяет, что уведомление было создано
     * 
     * @param string $type Тип уведомления
     * @param string $sourceType Тип источника
     * @param string $sourceId ID источника
     * @return Notification|null
     */
    protected function assertNotificationCreated(string $type, string $sourceType, string $sourceId): ?Notification
    {
        $notification = Notification::find()
            ->where([
                'type' => $type,
                'source_type' => $sourceType,
                'source_id' => $sourceId,
            ])
            ->orderBy(['id' => SORT_DESC])
            ->one();
        
        $this->assertNotNull($notification, 'Уведомление не было создано');
        return $notification;
    }
}
