<?php

namespace tests\integration\kess3;

use common\chip\externalIntegrations\kess3\Application\Service\Kess3DecodingFacade;
use common\chip\externalIntegrations\kess3\Domain\Entity\DecodingOperation;
use common\chip\externalIntegrations\kess3\Domain\Repository\DecodingOperationRepositoryInterface;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\OperationId;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\ProjectId;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\FileId;
use common\chip\externalIntegrations\kess3\Infrastructure\Repository\DecodingOperationRepository;
use common\models\ProjectFiles;
use common\models\Projects;
use common\models\User;
use tests\integration\NotificationSystemTest;
use Yii;

/**
 * Базовый класс для всех тестов новой архитектуры Kess3
 */
abstract class BaseKess3Test extends NotificationSystemTest
{
    /**
     * @var Kess3DecodingFacade
     */
    protected $facade;

    /**
     * @var DecodingOperationRepositoryInterface
     */
    protected $operationRepository;

    /**
     * @var ProjectFiles[]
     */
    protected $testFiles = [];

    /**
     * @var string
     */
    protected $testFilesDir;

    /**
     * @var array Список тестовых операций для очистки
     */
    protected $createdOperations = [];

    /**
     * Настройка перед каждым тестом
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->testFilesDir = sys_get_temp_dir() . '/kess3_test_files';
        
        try {
            $this->initializeKess3Components();
            $this->prepareTestFiles();
        } catch (\Exception $e) {
            $this->markTestSkipped('Failed to initialize Kess3 components: ' . $e->getMessage());
        }
    }

    /**
     * Инициализирует компоненты Kess3
     */
    protected function initializeKess3Components(): void
    {
        // Получаем фасад из DI контейнера
        $this->facade = Yii::$container->get(Kess3DecodingFacade::class);
        
        // Получаем репозиторий операций
        $this->operationRepository = Yii::$container->get(DecodingOperationRepositoryInterface::class);
        
        $this->assertNotNull($this->facade, 'Kess3DecodingFacade not available');
        $this->assertNotNull($this->operationRepository, 'DecodingOperationRepository not available');
    }

    /**
     * Подготавливает тестовые файлы
     */
    protected function prepareTestFiles(): void
    {
        // Ищем существующие файлы в БД
        $existingFiles = ProjectFiles::find()
            ->where(['!=', 'path', ''])
            ->andWhere(['!=', 'filename', ''])
            ->andWhere(['!=', 'project_id', 0])
            ->limit(3)
            ->all();

        if (count($existingFiles) > 0) {
            foreach ($existingFiles as $file) {
                // Проверяем что файл существует на диске
                if ($file->path && file_exists($file->path)) {
                    $this->testFiles[] = $file;
                }
            }
        }

        // Если нет подходящих файлов, создаем тестовые
        if (empty($this->testFiles)) {
            $this->createTestFiles();
        }

        $this->assertNotEmpty($this->testFiles, 'No test files available');
    }

    /**
     * Создает тестовые файлы в БД и на диске
     */
    protected function createTestFiles(): void
    {
        $testProject = $this->getOrCreateTestProject();
        
        for ($i = 1; $i <= 3; $i++) {
            $testFile = $this->createTestFile($testProject->id, $i);
            if ($testFile) {
                $this->testFiles[] = $testFile;
            }
        }
    }

    /**
     * Получает или создает тестовый проект
     */
    protected function getOrCreateTestProject(): Projects
    {
        // Сначала ищем существующий проект
        $project = Projects::find()->limit(1)->one();
        
        if ($project) {
            return $project;
        }

        // Если нет проектов, создаем тестовый
        $project = new Projects();
        $project->title = 'Kess3 Test Project';
        $project->user_id = $this->testUser->id;
        $project->vehicle_id = 1;
        $project->brand_id = 1;
        $project->model_id = 1;
        $project->generation_id = 1;
        $project->engine_id = 1;
        $project->ecu_id = 1;
        $project->readmethod_id = 1;
        $project->stage_id = 1;
        $project->gearbox_id = 1;
        $project->year = 2020;
        $project->created_at = date('Y-m-d H:i:s');

        if (!$project->save()) {
            throw new \Exception('Failed to create test project: ' . json_encode($project->errors));
        }

        return $project;
    }

    /**
     * Создает тестовый файл
     */
    protected function createTestFile(int $projectId, int $index): ?ProjectFiles
    {
        // Создаем физический файл
        $filename = "kess3_test_file_{$index}.bin";
        $filePath = $this->testFilesDir . '/' . $filename;
        
        // Генерируем тестовые данные (имитируем бинарный файл)
        $testData = str_repeat(pack('C*', rand(0, 255)), 1024); // 1KB of random binary data
        file_put_contents($filePath, $testData);

        // Создаем запись в БД
        $file = new ProjectFiles();
        $file->title = "Kess3 Test File {$index}";
        $file->filename = $filename;
        $file->path = $filePath;
        $file->project_id = $projectId;
        $file->created_at = date('Y-m-d H:i:s');

        if ($file->save()) {
            return $file;
        }

        return null;
    }

    /**
     * Получает случайный тестовый файл
     */
    protected function getRandomTestFile(): ProjectFiles
    {
        return $this->testFiles[array_rand($this->testFiles)];
    }

    /**
     * Создает тестовую операцию декодирования
     */
    protected function createTestOperation(?ProjectFiles $file = null): DecodingOperation
    {
        $file = $file ?: $this->getRandomTestFile();
        
        $operation = DecodingOperation::create(
            OperationId::generate(),
            ProjectId::fromInt($file->project_id),
            FileId::fromInt($file->id),
            $file->path,
            'https://test.example.com/callback/kess3'
        );

        // Сохраняем операцию
        $this->operationRepository->save($operation);
        
        // Запоминаем для очистки
        $this->createdOperations[] = $operation->getOperationId()->getValue();
        
        return $operation;
    }

    /**
     * Проверяет, что операция была сохранена в БД
     */
    protected function assertOperationExists(OperationId $operationId): void
    {
        $operation = $this->operationRepository->findById($operationId);
        $this->assertNotNull($operation, "Operation {$operationId->getValue()} not found in database");
    }

    /**
     * Проверяет, что событие было обработано
     */
    protected function assertEventHandled(string $eventType, string $entityType, int $entityId): void
    {
        $eventLog = $this->assertEventLogged(
            $this->createMockEvent($eventType),
            $entityType,
            $entityId
        );
        
        $this->assertNotNull($eventLog, "Event {$eventType} was not logged");
    }

    /**
     * Создает мок-событие для тестирования
     */
    protected function createMockEvent(string $eventType): object
    {
        return new class($eventType) {
            private $type;
            
            public function __construct(string $type) {
                $this->type = $type;
            }
            
            public function getType(): string {
                return $this->type;
            }
        };
    }

    /**
     * Ждет завершения асинхронных операций
     */
    protected function waitForAsyncOperations(int $timeoutSeconds = 30): void
    {
        $startTime = time();
        
        while (time() - $startTime < $timeoutSeconds) {
            $inProgressOperations = $this->facade->getInProgressOperations();
            
            if (empty($inProgressOperations)) {
                return;
            }
            
            sleep(1);
        }
        
        $this->fail('Async operations did not complete within timeout');
    }

    /**
     * Очистка после каждого теста
     */
    protected function tearDown(): void
    {
        // Удаляем созданные операции
        foreach ($this->createdOperations as $operationId) {
            try {
                $operation = $this->operationRepository->findById(OperationId::fromString($operationId));
                if ($operation) {
                    $this->operationRepository->delete($operation);
                }
            } catch (\Exception $e) {
                // Игнорируем ошибки удаления
            }
        }

        // Удаляем тестовые файлы
        foreach ($this->testFiles as $file) {
            if ($file->filename && strpos($file->filename, 'kess3_test_file_') === 0) {
                // Удаляем физический файл
                if ($file->path && file_exists($file->path)) {
                    unlink($file->path);
                }
                
                // Удаляем из БД
                try {
                    $file->delete();
                } catch (\Exception $e) {
                    // Игнорируем ошибки удаления
                }
            }
        }

        parent::tearDown();
    }

    /**
     * Выводит отладочную информацию
     */
    protected function debug(string $message): void
    {
        if (defined('YII_DEBUG') && YII_DEBUG) {
            echo "\n[DEBUG] " . $message . "\n";
        }
    }

    /**
     * Печатает статистику операций для отладки
     */
    protected function printOperationStats(): void
    {
        $stats = $this->facade->getSystemStatistics();
        $this->debug("Operation Statistics: " . json_encode($stats, JSON_PRETTY_PRINT));
    }
}
