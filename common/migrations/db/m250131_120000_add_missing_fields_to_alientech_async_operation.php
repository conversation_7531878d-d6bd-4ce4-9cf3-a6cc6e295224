<?php

use yii\db\Migration;

/**
 * Миграция для добавления недостающих полей в таблицу alientech_async_operation
 * для полной поддержки новой структуры API ответа
 */
class m250131_120000_add_missing_fields_to_alientech_async_operation extends Migration
{
    public function safeUp()
    {
        $tableName = '{{%alientech_async_operation}}';
        
        // Проверяем существование таблицы
        $schema = $this->db->getTableSchema($tableName);
        if (!$schema) {
            echo "Table {$tableName} does not exist, skipping migration.\n";
            return;
        }

        // Добавляем недостающие поля из новой структуры API
        $columnsToAdd = [
            'client_application_guid' => $this->string(255)->null()->comment('GUID клиентского приложения'),
            'duration' => $this->string(50)->null()->comment('Продолжительность операции'),
            'job_request_guid' => $this->string(255)->null()->comment('GUID запроса задания'),
            'api_status' => $this->integer()->null()->comment('Статус из API (отличается от внутреннего статуса)'),
        ];

        foreach ($columnsToAdd as $columnName => $columnDefinition) {
            if (!isset($schema->columns[$columnName])) {
                echo "Adding column {$columnName} to {$tableName}...\n";
                $this->addColumn($tableName, $columnName, $columnDefinition);
            } else {
                echo "Column {$columnName} already exists in {$tableName}, skipping...\n";
            }
        }

        // Добавляем индексы для производительности
        $indexesToAdd = [
            'idx_alientech_async_operation_client_app_guid' => 'client_application_guid',
            'idx_alientech_async_operation_job_request_guid' => 'job_request_guid',
            'idx_alientech_async_operation_api_status' => 'api_status',
        ];

        foreach ($indexesToAdd as $indexName => $columnName) {
            try {
                $this->createIndex($indexName, $tableName, $columnName);
                echo "Created index {$indexName} on {$tableName}.{$columnName}\n";
            } catch (Exception $e) {
                echo "Index {$indexName} already exists or failed to create: " . $e->getMessage() . "\n";
            }
        }

        echo "Added missing fields to alientech_async_operation table for new API structure support\n";
    }

    public function safeDown()
    {
        $tableName = '{{%alientech_async_operation}}';
        
        // Удаляем индексы
        $indexesToRemove = [
            'idx_alientech_async_operation_api_status',
            'idx_alientech_async_operation_job_request_guid',
            'idx_alientech_async_operation_client_app_guid',
        ];

        foreach ($indexesToRemove as $indexName) {
            try {
                $this->dropIndex($indexName, $tableName);
                echo "Dropped index {$indexName}\n";
            } catch (Exception $e) {
                echo "Index {$indexName} does not exist or failed to drop: " . $e->getMessage() . "\n";
            }
        }

        // Удаляем добавленные поля
        $columnsToRemove = [
            'api_status',
            'job_request_guid', 
            'duration',
            'client_application_guid',
        ];

        $schema = $this->db->getTableSchema($tableName);
        if ($schema) {
            foreach ($columnsToRemove as $columnName) {
                if (isset($schema->columns[$columnName])) {
                    echo "Removing column {$columnName} from {$tableName}...\n";
                    $this->dropColumn($tableName, $columnName);
                } else {
                    echo "Column {$columnName} does not exist in {$tableName}, skipping...\n";
                }
            }
        }

        echo "Removed added fields from alientech_async_operation table\n";
    }
}
