<?php

use common\chip\externalIntegrations\kess3\Application\Handler\ProcessDecodingResultHandler;
use common\chip\externalIntegrations\kess3\Application\Handler\ProcessEncodingResultHandler;
use common\chip\externalIntegrations\kess3\Application\Handler\StartDecodingHandler;
use common\chip\externalIntegrations\kess3\Application\Handler\StartEncodingHandler;
use common\chip\externalIntegrations\kess3\Application\Service\EncodingFacade;
use common\chip\externalIntegrations\kess3\Application\Service\Kess3DecodingFacade;
use common\chip\externalIntegrations\kess3\Domain\Repository\EncodingOperationRepositoryInterface;
use common\chip\externalIntegrations\kess3\Domain\Repository\DecodingOperationRepositoryInterface;
use common\chip\externalIntegrations\kess3\Domain\Service\DecodingDomainService;
use common\chip\externalIntegrations\kess3\Domain\Service\EncodingDomainService;
use common\chip\externalIntegrations\kess3\Infrastructure\Bridge\LegacyBridge;
use common\chip\externalIntegrations\kess3\Infrastructure\Console\DecodingConsoleController;
use common\chip\externalIntegrations\kess3\Infrastructure\EventHandler\DecodingEventHandler;
use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClient;
use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Repository\DecodingOperationRepository;
use common\chip\externalIntegrations\kess3\Infrastructure\Repository\EncodingOperationRepository;
use common\chip\externalIntegrations\kess3\Infrastructure\Service\ModernAsyncOperationService;
use common\chip\externalIntegrations\kess3\Infrastructure\Service\ModernKess3Service;
use common\chip\externalIntegrations\kess3\Interfaces\Http\DecodingController;
use common\chip\alientech\services\AlientechLinkService;
use common\chip\alientech\services\AlientechService;
use common\chip\alientech\services\FileSlotService;
use common\chip\alientech\services\LogService as AlientechLogService;
use common\chip\alientech\interfaces\Kess3ServiceInterface;
use common\chip\alientech\services\AsyncOperationService;
use common\chip\event\EventDispatcher;
use common\chip\project\services\MessageService;

// Новые компоненты рефакторинга
use common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClient;
use common\chip\externalIntegrations\kess3\Infrastructure\Http\AuthProviderInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Http\AuthProvider;
use common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClientFactory;
use common\chip\externalIntegrations\kess3\Infrastructure\Configuration\ConfigProviderInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Configuration\ConfigProvider;
use common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement\SlotManagerInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement\SlotManager;
use common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement\SlotManagerFactory;

return [
    // ========================================
    // CORE INTERFACES
    // ========================================

    DecodingOperationRepositoryInterface::class => DecodingOperationRepository::class,
    EncodingOperationRepositoryInterface::class => EncodingOperationRepository::class,
    AlientechApiClientInterface::class => AlientechApiClient::class,

    // ========================================
    // DOMAIN SERVICES
    // ========================================

    DecodingDomainService::class => function ($container) {
        return new DecodingDomainService(
            $container->get(DecodingOperationRepositoryInterface::class)
        );
    },

    // ========================================
    // INFRASTRUCTURE SERVICES
    // ========================================

    DecodingOperationRepository::class => function () {
        return new DecodingOperationRepository();
    },

    AlientechApiClient::class => function ($container) {
        return new AlientechApiClient(
            $container->get(HttpClientInterface::class),
            $container->get(ConfigProviderInterface::class),
            $container->get(SlotManagerInterface::class),
            Yii::$app->get('logger')
        );
    },

    // ========================================
    // NEW REFACTORED COMPONENTS
    // ========================================

    // Configuration
    ConfigProviderInterface::class => ConfigProvider::class,

    ConfigProvider::class => function () {
        return new ConfigProvider(require __DIR__ . '/alientech_api.php');
    },

    // HTTP Components
    HttpClientInterface::class => function ($container) {
        $factory = $container->get(HttpClientFactory::class);
        $config = $container->get(ConfigProviderInterface::class);
        return $factory->createHttpClient($config);
    },

    AuthProviderInterface::class => AuthProvider::class,

    HttpClient::class => function ($container) {
        return new HttpClient(Yii::$app->get('logger'));
    },

    AuthProvider::class => function ($container) {
        return new AuthProvider(
            Yii::$app->get('db'),
            Yii::$app->get('cache'),
            Yii::$app->get('logger'),
            '/api/access-tokens/request',
            3600,
            300
        );
    },

    // Slot Management
    SlotManagerInterface::class => function ($container) {
        $factory = $container->get(SlotManagerFactory::class);
        $httpClient = $container->get(HttpClientInterface::class);
        $config = $container->get(ConfigProviderInterface::class);
        return $factory->createSlotManager($httpClient, $config);
    },

    SlotManager::class => function ($container) {
        return new SlotManager(
            $container->get(HttpClientInterface::class),
            Yii::$app->get('cache'),
            Yii::$app->get('logger'),
            60,
            3
        );
    },

    // Factories
    HttpClientFactory::class => function ($container) {
        return new HttpClientFactory(
            Yii::$app->get('db'),
            Yii::$app->get('cache'),
            Yii::$app->get('logger')
        );
    },

    SlotManagerFactory::class => function ($container) {
        return new SlotManagerFactory(
            Yii::$app->get('cache'),
            Yii::$app->get('logger')
        );
    },

    // ========================================
    // APPLICATION HANDLERS
    // ========================================

    StartDecodingHandler::class => function ($container) {
        return new StartDecodingHandler(
            $container->get(DecodingDomainService::class),
            $container->get(DecodingOperationRepositoryInterface::class),
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    ProcessDecodingResultHandler::class => function ($container) {
        return new ProcessDecodingResultHandler(
            $container->get(DecodingOperationRepositoryInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    // ========================================
    // APPLICATION SERVICES
    // ========================================

    Kess3DecodingFacade::class => function ($container) {
        return new Kess3DecodingFacade(
            $container->get(StartDecodingHandler::class),
            $container->get(ProcessDecodingResultHandler::class),
            $container->get(DecodingOperationRepositoryInterface::class)
        );
    },

    EncodingFacade::class => function ($container) {
        return new EncodingFacade(
            $container->get(StartEncodingHandler::class),
            $container->get(ProcessEncodingResultHandler::class),
            $container->get(EncodingOperationRepositoryInterface::class),
            $container->get(EncodingDomainService::class)
        );
    },

    // ========================================
    // INFRASTRUCTURE BRIDGE & MODERN SERVICES
    // ========================================

    LegacyBridge::class => function ($container) {
        return new LegacyBridge(
            $container->get(Kess3DecodingFacade::class)
        );
    },

    ModernKess3Service::class => function ($container) {
        return new ModernKess3Service(
            $container->get(LegacyBridge::class)
        );
    },

    ModernAsyncOperationService::class => function ($container) {
        return new ModernAsyncOperationService(
            $container->get(LegacyBridge::class)
        );
    },

    // ========================================
    // EVENT HANDLERS
    // ========================================

    DecodingEventHandler::class => function ($container) {
        return new DecodingEventHandler(
            $container->get(AlientechApiClientInterface::class),
            $container->get(MessageService::class)
        );
    },

    // ========================================
    // CONTROLLERS
    // ========================================

//    DecodingController::class => function ($container) {
//        return new DecodingController(
//            $container->get(Kess3DecodingFacade::class)
//        );
//    },

//    DecodingConsoleController::class => function ($container) {
//        return new DecodingConsoleController(
//            $container->get(Kess3DecodingFacade::class)
//        );
//    },

    // ========================================
    // LEGACY SERVICES (for backward compatibility)
    // ========================================

    AlientechLinkService::class => function () {
        return new AlientechLinkService();
    },

    AlientechService::class => function () {
        return new AlientechService();
    },

    FileSlotService::class => function ($container) {
        return new FileSlotService(
            $container->get(AlientechLinkService::class),
            $container->get(AlientechLogService::class)
        );
    },

    AlientechLogService::class => function () {
        return new AlientechLogService();
    },

    MessageService::class => function () {
        return new MessageService();
    },

    // ========================================
    // INTERFACE REPLACEMENTS (for full migration)
    // ========================================

    // Uncomment these lines to fully replace old services with new ones
    /*
    Kess3ServiceInterface::class => ModernKess3Service::class,
    AsyncOperationService::class => ModernAsyncOperationService::class,
    */

    // ========================================
    // DEPENDENCIES FOR TESTING
    // ========================================

    // These are primarily for testing and development
    'kess3.test.mock_repository' => function () {
        return new DecodingOperationRepository();
    },

    'kess3.test.mock_api_client' => function ($container) {
        return new AlientechApiClient(
            $container->get(HttpClientInterface::class),
            $container->get(ConfigProviderInterface::class),
            $container->get(SlotManagerInterface::class),
            Yii::$app->get('logger')
        );
    },
];
