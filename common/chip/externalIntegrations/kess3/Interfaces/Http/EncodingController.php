<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Interfaces\Http;

use common\chip\externalIntegrations\kess3\Application\Service\EncodingFacade;
use common\chip\externalIntegrations\kess3\Domain\Entity\EncodingOperation;
use yii\filters\ContentNegotiator;
use yii\filters\VerbFilter;
use yii\rest\Controller;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use Yii;

/**
 * REST API контроллер для операций энкодирования
 */
final class EncodingController extends Controller
{
    public function __construct(
        $id,
        $module,
        private readonly EncodingFacade $encodingFacade,
        $config = []
    ) {
        parent::__construct($id, $module, $config);
    }

    public function behaviors(): array
    {
        return [
            'contentNegotiator' => [
                'class' => ContentNegotiator::class,
                'formats' => [
                    'application/json' => Response::FORMAT_JSON,
                ],
            ],
            'verbFilter' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'start' => ['POST'],
                    'status' => ['GET'],
                    'project' => ['GET'],
                    'cancel' => ['POST', 'DELETE'],
                    'callback' => ['POST'],
                    'statistics' => ['GET'],
                ],
            ],
        ];
    }

    /**
     * Запустить энкодирование проекта
     * 
     * POST /api/encoding/start
     * {
     *   "projectId": 123,
     *   "service": "alientech",
     *   "callbackUrl": "https://example.com/callback"
     * }
     */
    public function actionStart(): array
    {
        try {
            $request = Yii::$app->request;
            $projectId = $request->getBodyParam('projectId');
            $service = $request->getBodyParam('service', 'alientech');
            $callbackUrl = $request->getBodyParam('callbackUrl', '');

            if (!$projectId) {
                throw new BadRequestHttpException('Project ID is required');
            }

            if (!is_int($projectId) && !ctype_digit($projectId)) {
                throw new BadRequestHttpException('Project ID must be a number');
            }

            $operation = $this->encodingFacade->startEncoding(
                projectId: (int) $projectId,
                service: $service,
                callbackUrl: $callbackUrl
            );

            return [
                'status' => 'success',
                'data' => $this->serializeOperation($operation),
                'message' => 'Encoding started successfully'
            ];
        } catch (\InvalidArgumentException $e) {
            throw new BadRequestHttpException($e->getMessage());
        } catch (\DomainException $e) {
            throw new BadRequestHttpException($e->getMessage());
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to start encoding via API: {$e->getMessage()}",
                category: 'kess3.encoding.api'
            );

            return [
                'status' => 'error',
                'message' => 'Failed to start encoding operation',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Получить статус операции энкодирования
     * 
     * GET /api/encoding/status/{operationId}
     */
    public function actionStatus(string $operationId): array
    {
        try {
            $operation = $this->encodingFacade->getOperationStatus($operationId);

            if (!$operation) {
                throw new NotFoundHttpException("Encoding operation not found: {$operationId}");
            }

            return [
                'status' => 'success',
                'data' => $this->serializeOperation($operation)
            ];
        } catch (NotFoundHttpException $e) {
            throw $e;
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to get encoding status via API: {$e->getMessage()}",
                category: 'kess3.encoding.api'
            );

            return [
                'status' => 'error',
                'message' => 'Failed to get operation status',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Получить операции энкодирования по проекту
     * 
     * GET /api/encoding/project/{projectId}
     */
    public function actionProject(int $projectId): array
    {
        try {
            $operations = $this->encodingFacade->getProjectOperations($projectId);
            $statistics = $this->encodingFacade->getProjectStatistics($projectId);

            return [
                'status' => 'success',
                'data' => [
                    'projectId' => $projectId,
                    'operations' => array_map([$this, 'serializeOperation'], $operations),
                    'statistics' => $statistics,
                    'totalCount' => count($operations)
                ]
            ];
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to get project encoding operations via API: {$e->getMessage()}",
                category: 'kess3.encoding.api'
            );

            return [
                'status' => 'error',
                'message' => 'Failed to get project operations',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Отменить операцию энкодирования
     * 
     * POST /api/encoding/cancel
     * DELETE /api/encoding/{operationId}
     */
    public function actionCancel(?string $operationId = null): array
    {
        try {
            if (!$operationId) {
                $operationId = Yii::$app->request->getBodyParam('operationId');
            }

            if (!$operationId) {
                throw new BadRequestHttpException('Operation ID is required');
            }

            $this->encodingFacade->cancelOperation($operationId);

            return [
                'status' => 'success',
                'message' => 'Operation cancelled successfully',
                'data' => ['operationId' => $operationId]
            ];
        } catch (\RuntimeException $e) {
            throw new NotFoundHttpException($e->getMessage());
        } catch (\DomainException $e) {
            throw new BadRequestHttpException($e->getMessage());
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to cancel encoding operation via API: {$e->getMessage()}",
                category: 'kess3.encoding.api'
            );

            return [
                'status' => 'error',
                'message' => 'Failed to cancel operation',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Обработать callback от внешнего сервиса энкодирования
     * 
     * POST /api/encoding/callback
     * или
     * POST /api/kess3-encoded (совместимость)
     */
    public function actionCallback(): array
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        try {
            $requestData = $this->getRequestData();

            if (empty($requestData)) {
                throw new BadRequestHttpException('Callback data is required');
            }

            Yii::info(
                message: "Received encoding callback: " . json_encode($requestData),
                category: 'kess3.encoding.api'
            );

            $operation = $this->encodingFacade->processEncodingResult($requestData);

            return [
                'status' => 'success',
                'message' => 'Callback processed successfully',
                'data' => $this->serializeOperation($operation)
            ];
        } catch (\RuntimeException $e) {
            Yii::error(
                message: "Encoding callback processing failed: {$e->getMessage()}",
                category: 'kess3.encoding.api'
            );

            throw new BadRequestHttpException($e->getMessage());
        } catch (\Exception $e) {
            Yii::error(
                message: "Unexpected error in encoding callback: {$e->getMessage()}",
                category: 'kess3.encoding.api'
            );

            return [
                'status' => 'error',
                'message' => 'Failed to process callback',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Получить статистику операций энкодирования
     * 
     * GET /api/encoding/statistics
     * GET /api/encoding/statistics?projectId=123
     */
    public function actionStatistics(): array
    {
        try {
            $projectId = Yii::$app->request->get('projectId');

            if ($projectId) {
                $statistics = $this->encodingFacade->getProjectStatistics((int) $projectId);
                $data = [
                    'type' => 'project',
                    'projectId' => (int) $projectId,
                    'statistics' => $statistics
                ];
            } else {
                // Общая статистика по всем проектам
                $data = [
                    'type' => 'global',
                    'statistics' => $this->getGlobalStatistics()
                ];
            }

            return [
                'status' => 'success',
                'data' => $data
            ];
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to get encoding statistics via API: {$e->getMessage()}",
                category: 'kess3.encoding.api'
            );

            return [
                'status' => 'error',
                'message' => 'Failed to get statistics',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Отменить просроченные операции
     * 
     * POST /api/encoding/cleanup-expired
     */
    public function actionCleanupExpired(): array
    {
        try {
            $timeoutMinutes = (int) Yii::$app->request->getBodyParam('timeoutMinutes', 30);
            
            $cancelledCount = $this->encodingFacade->cancelExpiredOperations($timeoutMinutes);

            return [
                'status' => 'success',
                'message' => "Cleanup completed",
                'data' => [
                    'cancelledOperations' => $cancelledCount,
                    'timeoutMinutes' => $timeoutMinutes
                ]
            ];
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to cleanup expired encoding operations: {$e->getMessage()}",
                category: 'kess3.encoding.api'
            );

            return [
                'status' => 'error',
                'message' => 'Failed to cleanup expired operations',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Сериализация операции для API ответа
     */
    private function serializeOperation(EncodingOperation $operation): array
    {
        return [
            'operationId' => $operation->getOperationId()->getValue(),
            'projectId' => $operation->getProjectId()->getValue(),
            'fileIds' => $operation->getFileIds(),
            'filesCount' => count($operation->getFileIds()),
            'status' => $operation->getStatus()->getValue(),
            'isStarted' => $operation->isStarted(),
            'isCompleted' => $operation->isCompleted(),
            'isFailed' => $operation->isFailed(),
            'isFinished' => $operation->isFinished(),
            'externalOperationId' => $operation->getExternalOperationId(),
            'slotGuid' => $operation->getSlotGuid(),
            'result' => $operation->getResult(),
            'error' => $operation->getError(),
            'createdAt' => $operation->getCreatedAt()->format('Y-m-d H:i:s'),
            'startedAt' => $operation->getStartedAt()?->format('Y-m-d H:i:s'),
            'completedAt' => $operation->getCompletedAt()?->format('Y-m-d H:i:s'),
            'userInfo' => $operation->getUserInfo(),
        ];
    }

    /**
     * Получить глобальную статистику
     */
    private function getGlobalStatistics(): array
    {
        // Простая реализация глобальной статистики
        // В реальном проекте это можно вынести в отдельный сервис
        $query = \common\models\AlientechAsyncOperation::find()
            ->where(['operation_type' => 'encoding']);

        $total = $query->count();
        $inProgress = $query->andWhere(['status' => \common\chip\externalIntegrations\kess3\Domain\ValueObject\EncodingStatus::IN_PROGRESS])->count();
        $completed = $query->andWhere(['status' => \common\chip\externalIntegrations\kess3\Domain\ValueObject\EncodingStatus::COMPLETED])->count();
        $failed = $query->andWhere(['status' => \common\chip\externalIntegrations\kess3\Domain\ValueObject\EncodingStatus::FAILED])->count();
        $cancelled = $query->andWhere(['status' => \common\chip\externalIntegrations\kess3\Domain\ValueObject\EncodingStatus::CANCELLED])->count();

        return [
            'total' => $total,
            'inProgress' => $inProgress,
            'completed' => $completed,
            'failed' => $failed,
            'cancelled' => $cancelled,
        ];
    }
}
