<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Command;

/**
 * Команда обработки результата энкодирования
 */
final readonly class ProcessEncodingResultCommand
{
    public function __construct(
        public string $externalOperationId,
        public bool $isCompleted,
        public bool $isSuccessful,
        public ?array $result = null,
        public ?array $error = null
    ) {
    }

    public static function fromCallbackData(array $callbackData): self
    {
        return new self(
            externalOperationId: $callbackData['GUID'] ?? '',
            isCompleted: (bool) ($callbackData['isCompleted'] ?? false),
            isSuccessful: (bool) ($callbackData['isSuccessful'] ?? false),
            result: $callbackData['result'] ?? null,
            error: $callbackData['error'] ?? null
        );
    }
}
