<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Examples;

use common\chip\externalIntegrations\kess3\Application\Service\Kess3DecodingFacade;
use common\chip\externalIntegrations\kess3\Infrastructure\Job\StartDecodingJob;
use common\chip\externalIntegrations\kess3\Infrastructure\Adapter\LegacyKess3ServiceAdapter;
use common\chip\externalIntegrations\kess3\Infrastructure\Bridge\DecodingJobBridge;
use common\chip\alientech\jobs\AlientechStartDecodingJob;
use common\models\ProjectFiles;
use Yii;

/**
 * Примеры использования новой рефакторированной системы декодирования Kess3
 */
class UsageExamples
{
    /**
     * Пример 1: Простое использование через фасад
     */
    public function example1_BasicUsage(): void
    {
        echo "=== Пример 1: Базовое использование фасада ===\n";

        // Получаем фасад из DI контейнера
        $facade = Yii::$container->get(Kess3DecodingFacade::class);

        // Запускаем декодирование
        $operation = $facade->startDecoding(
            projectId: 123,
            fileId: 456,
            filePath: '/path/to/file.bin'
        );

        echo "✓ Декодирование запущено!\n";
        echo "  ID операции: {$operation->getOperationId()}\n";
        echo "  Внешний ID: {$operation->getExternalOperationId()}\n";
        echo "  Статус: {$operation->getStatus()}\n";

        // Получаем статистику по проекту
        $stats = $facade->getProjectStatistics(123);
        echo "  Статистика проекта:\n";
        echo "    Всего операций: {$stats['total']}\n";
        echo "    В процессе: {$stats['in_progress']}\n";
        echo "    Завершено: {$stats['completed']}\n";
        echo "    Ошибок: {$stats['failed']}\n";
    }

    /**
     * Пример 2: Асинхронное выполнение через очередь
     */
    public function example2_AsyncExecution(): void
    {
        echo "\n=== Пример 2: Асинхронное выполнение ===\n";

        // Создаем Job для асинхронного выполнения
        $job = new StartDecodingJob([
            'projectId' => 123,
            'fileId' => 456,
            'filePath' => '/path/to/file.bin',
            'callbackUrl' => 'https://example.com/api/kess3-decoded'
        ]);

        // Добавляем в очередь
        $jobId = Yii::$app->queue->push($job);

        echo "✓ Задача добавлена в очередь!\n";
        echo "  ID задачи: {$jobId}\n";
        echo "  Выполнение начнется автоматически\n";
    }

    /**
     * Пример 3: Обработка callback от Alientech API
     */
    public function example3_CallbackProcessing(): void
    {
        echo "\n=== Пример 3: Обработка callback ===\n";

        // Симуляция данных callback от Alientech
        $callbackData = [
            'guid' => 'test-operation-guid-123',
            'isCompleted' => true,
            'isSuccessful' => true,
            'result' => [
                'kess3FileSlotGUID' => 'slot-guid-456',
                'decodedFileUrls' => [
                    'OBD' => 'https://api.alientech.to/download/decoded-file-1'
                ]
            ]
        ];

        $facade = Yii::$container->get(Kess3DecodingFacade::class);
        $operation = $facade->processDecodingResult($callbackData);

        if ($operation && $operation->isCompleted()) {
            echo "✓ Callback обработан успешно!\n";
            echo "  Операция: {$operation->getOperationId()}\n";
            echo "  Статус: {$operation->getStatus()}\n";
            echo "  Результат: " . json_encode($operation->getResult()) . "\n";
        } else {
            echo "✗ Операция не найдена или не завершена\n";
        }
    }

    /**
     * Пример 4: Использование адаптера для совместимости
     */
    public function example4_LegacyCompatibility(): void
    {
        echo "\n=== Пример 4: Обратная совместимость ===\n";

        // Получаем адаптер для работы с существующим интерфейсом
        $legacyService = Yii::$container->get(LegacyKess3ServiceAdapter::class);

        // Создаем объект файла (как в старой системе)
        $file = new ProjectFiles([
            'id' => 456,
            'project_id' => 123,
            'path' => '/path/to/file.bin',
            'title' => 'test_file.bin'
        ]);

        // Используем старый интерфейс, но с новой архитектурой внутри
        $result = $legacyService->start($file);

        echo $result ? "✓ Декодирование запущено через адаптер!\n" : "✗ Ошибка запуска\n";

        // Получаем статистику через новый метод адаптера
        $stats = $legacyService->getProjectStatistics(123);
        echo "  Статистика: " . json_encode($stats) . "\n";

        // Получаем операции через новый метод адаптера
        $operations = $legacyService->getProjectOperations(123);
        echo "  Количество операций: " . count($operations) . "\n";
    }

    /**
     * Пример 5: Миграция старых Job'ов
     */
    public function example5_JobMigration(): void
    {
        echo "\n=== Пример 5: Миграция Job'ов ===\n";

        // Создаем старый Job
        $file = new ProjectFiles([
            'id' => 456,
            'project_id' => 123,
            'path' => '/path/to/file.bin'
        ]);

        $legacyJob = new AlientechStartDecodingJob(['file' => $file]);

        // Проверяем, нужно ли использовать новую архитектуру
        if (DecodingJobBridge::shouldUseNewArchitecture(123)) {
            echo "✓ Используем новую архитектуру для проекта 123\n";
            
            // Мигрируем Job
            $newJobId = DecodingJobBridge::migrateJob($legacyJob);
            echo "  Новый Job ID: {$newJobId}\n";
        } else {
            echo "→ Продолжаем использовать старую архитектуру\n";
            // Выполняем старый Job
            Yii::$app->queue->push($legacyJob);
        }
    }

    /**
     * Пример 6: Отмена просроченных операций
     */
    public function example6_CancelExpiredOperations(): void
    {
        echo "\n=== Пример 6: Отмена просроченных операций ===\n";

        $facade = Yii::$container->get(Kess3DecodingFacade::class);

        // Отменяем операции, которые выполняются более 30 минут
        $cancelledCount = $facade->cancelExpiredOperations(30);

        echo "✓ Отменено просроченных операций: {$cancelledCount}\n";

        // Получаем список активных операций
        $activeOperations = $facade->getInProgressOperations();
        echo "  Активных операций осталось: " . count($activeOperations) . "\n";

        foreach ($activeOperations as $operation) {
            $duration = $operation->getCreatedAt()->diff(new \DateTimeImmutable());
            echo "    {$operation->getOperationId()} - {$duration->format('%h:%I:%S')}\n";
        }
    }

    /**
     * Пример 7: Обработка ошибок и исключений
     */
    public function example7_ErrorHandling(): void
    {
        echo "\n=== Пример 7: Обработка ошибок ===\n";

        $facade = Yii::$container->get(Kess3DecodingFacade::class);

        try {
            // Попытка запустить декодирование с невалидными данными
            $operation = $facade->startDecoding(
                projectId: 0, // Невалидный ID
                fileId: 0,    // Невалидный ID
                filePath: '/nonexistent/file.bin' // Несуществующий файл
            );

            echo "✓ Операция создана: {$operation->getOperationId()}\n";

        } catch (\InvalidArgumentException $e) {
            echo "✗ Ошибка валидации: {$e->getMessage()}\n";
        } catch (\DomainException $e) {
            echo "✗ Доменная ошибка: {$e->getMessage()}\n";
        } catch (\RuntimeException $e) {
            echo "✗ Ошибка выполнения: {$e->getMessage()}\n";
        } catch (\Exception $e) {
            echo "✗ Общая ошибка: {$e->getMessage()}\n";
        }

        // Попытка получить несуществующую операцию
        $operation = $facade->getOperation('nonexistent-operation-id');
        
        if ($operation === null) {
            echo "✓ Корректная обработка отсутствующей операции\n";
        }
    }

    /**
     * Пример 8: Интеграция с Event System
     */
    public function example8_EventIntegration(): void
    {
        echo "\n=== Пример 8: События и уведомления ===\n";

        // События автоматически генерируются при операциях декодирования
        $facade = Yii::$container->get(Kess3DecodingFacade::class);

        echo "→ Запуск декодирования (будет сгенерировано событие DecodingStartedEvent)\n";
        $operation = $facade->startDecoding(123, 456, '/path/to/file.bin');

        echo "→ События автоматически обрабатываются DecodingEventHandler:\n";
        echo "  - Создаются системные заметки к проекту\n";
        echo "  - Обновляются статусы проектов\n";
        echo "  - Скачиваются декодированные файлы\n";
        echo "  - Отправляются уведомления через Event System\n";

        // Симуляция завершения операции
        $callbackData = [
            'guid' => $operation->getExternalOperationId(),
            'isCompleted' => true,
            'isSuccessful' => true,
            'result' => ['test' => 'data']
        ];

        echo "→ Обработка callback (будет сгенерировано событие DecodingCompletedEvent)\n";
        $facade->processDecodingResult($callbackData);
        echo "✓ События отправлены через EventBus\n";
    }

    /**
     * Запустить все примеры
     */
    public function runAllExamples(): void
    {
        echo "🚀 Запуск примеров использования рефакторированной системы Kess3\n";
        echo "================================================================\n";

        $examples = [
            'example1_BasicUsage' => 'Базовое использование фасада',
            'example2_AsyncExecution' => 'Асинхронное выполнение',
            'example3_CallbackProcessing' => 'Обработка callback',
            'example4_LegacyCompatibility' => 'Обратная совместимость',
            'example5_JobMigration' => 'Миграция Job\'ов',
            'example6_CancelExpiredOperations' => 'Отмена просроченных операций',
            'example7_ErrorHandling' => 'Обработка ошибок',
            'example8_EventIntegration' => 'Интеграция с событиями',
        ];

        foreach ($examples as $method => $description) {
            try {
                echo "\n📌 {$description}\n";
                $this->{$method}();
            } catch (\Exception $e) {
                echo "❌ Ошибка в примере: {$e->getMessage()}\n";
            }
        }

        echo "\n✅ Все примеры завершены!\n";
        echo "\n📖 Подробную документацию см. в README.md и INTEGRATION_GUIDE.md\n";
    }
}

// Использование:
// $examples = new UsageExamples();
// $examples->runAllExamples();
