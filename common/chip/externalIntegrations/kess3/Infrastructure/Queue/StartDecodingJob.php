<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Queue;

use common\chip\externalIntegrations\kess3\Application\Service\Kess3DecodingFacade;
use yii\base\BaseObject;
use yii\queue\JobInterface;
use Yii;

/**
 * Job для запуска декодирования через очередь
 */
final class StartDecodingJob extends BaseObject implements JobInterface
{
    /**
     * @var int ID проекта
     */
    public int $projectId;

    /**
     * @var int ID файла
     */
    public int $fileId;

    /**
     * @var string Путь к файлу
     */
    public string $filePath;

    /**
     * @var string Callback URL
     */
    public string $callbackUrl = '';

    public function execute($queue): void
    {
        try {
            Yii::info(
                message: "Starting decoding job for project {$this->projectId}, file {$this->fileId}",
                category: 'kess3.queue'
            );

            /** @var Kess3DecodingFacade $facade */
            $facade = Yii::$container->get(Kess3DecodingFacade::class);

            $operation = $facade->startDecoding(
                projectId: $this->projectId,
                fileId: $this->fileId,
                filePath: $this->filePath,
                callbackUrl: $this->callbackUrl
            );

            Yii::info(
                message: "Decoding job completed successfully. Operation ID: {$operation->getOperationId()}",
                category: 'kess3.queue'
            );
        } catch (\Exception $e) {
            Yii::error(
                message: "Decoding job failed: {$e->getMessage()}",
                category: 'kess3.queue'
            );
            
            throw $e; // Re-throw to mark job as failed
        }
    }
}
