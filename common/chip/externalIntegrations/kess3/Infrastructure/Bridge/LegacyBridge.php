<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Bridge;

use common\chip\alientech\entities\dto\AsyncOperationDto;
use common\chip\externalIntegrations\kess3\Application\Service\Kess3DecodingFacade;
use common\chip\externalIntegrations\kess3\Infrastructure\Queue\StartDecodingJob;
use common\models\ProjectFiles;
use Yii;

/**
 * Мост между старой и новой архитектурой
 * Позволяет постепенно мигрировать существующий код
 */
final readonly class LegacyBridge
{
    public function __construct(
        private Kess3DecodingFacade $decodingFacade
    ) {
    }

    /**
     * Заменяет старый Kess3Service::start()
     * Совместимый интерфейс для существующего кода
     */
    public function startDecoding(ProjectFiles $file): bool
    {
        try {
            // Используем новую архитектуру через очередь для обратной совместимости
            $job = new StartDecodingJob([
                'projectId' => $file->project_id,
                'fileId' => $file->id,
                'filePath' => $file->path,
                'callbackUrl' => $this->generateCallbackUrl(),
            ]);

            $jobId = Yii::$app->queue->push($job);
            
            Yii::info(
                message: "Decoding queued via legacy bridge. Job ID: {$jobId}",
                category: 'kess3.legacy_bridge'
            );

            return (bool) $jobId;
        } catch (\Exception $e) {
            Yii::error(
                message: "Legacy bridge decoding failed: {$e->getMessage()}",
                category: 'kess3.legacy_bridge'
            );
            
            return false;
        }
    }

    /**
     * Заменяет старый AsyncOperationService::decoded()
     * Обрабатывает callback от Alientech
     */
    public function processCallback(string $callbackData): bool
    {
        try {
            $data = json_decode($callbackData, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                Yii::error(
                    message: "Invalid JSON in legacy bridge callback: {$callbackData}",
                    category: 'kess3.legacy_bridge'
                );
                return false;
            }

            $operation = $this->decodingFacade->processDecodingResult($data);
            
            return $operation !== null;
        } catch (\Exception $e) {
            Yii::error(
                message: "Legacy bridge callback processing failed: {$e->getMessage()}",
                category: 'kess3.legacy_bridge'
            );
            
            return false;
        }
    }

    /**
     * Конвертирует новую доменную сущность в старый DTO для совместимости
     */
    public function convertToLegacyDto(
        \common\chip\externalIntegrations\kess3\Domain\Entity\DecodingOperation $operation
    ): AsyncOperationDto {
        $data = [
            'guid' => $operation->getOperationId()->getValue(),
            'clientApplicationGUID' => '', // Не доступен в новой архитектуре
            'asyncOperationType' => 5, // KESS3 Decoding
            'slotGUID' => $operation->getSlotGuid() ?? '',
            'status' => $operation->getStatus()->isCompleted() ? 1 : 0,
            'isCompleted' => $operation->isFinished(),
            'recommendedPollingInterval' => 10,
            'startedOn' => $operation->getStartedAt()?->format('Y-m-d\TH:i:s.v\Z') ?? '',
            'completedOn' => $operation->getCompletedAt()?->format('Y-m-d\TH:i:s.v\Z') ?? '',
            'duration' => $this->calculateDuration($operation),
            'isSuccessful' => $operation->isCompleted(),
            'hasFailed' => $operation->isFailed(),
            'result' => $operation->getResult(),
            'error' => $operation->getError(),
            'additionalInfo' => null,
            'userInfo' => [
                'projectId' => $operation->getProjectId()->getValue(),
                'fileId' => $operation->getFileId()->getValue(),
            ],
        ];

        return new AsyncOperationDto((object) $data);
    }

    /**
     * Получить операцию в формате старого DTO
     */
    public function getOperationAsLegacyDto(string $operationId): ?AsyncOperationDto
    {
        $operation = $this->decodingFacade->getOperation($operationId);
        
        if ($operation === null) {
            return null;
        }

        return $this->convertToLegacyDto($operation);
    }

    /**
     * Получить операцию по внешнему ID в формате старого DTO
     */
    public function getOperationByExternalIdAsLegacyDto(string $externalOperationId): ?AsyncOperationDto
    {
        $operation = $this->decodingFacade->getOperationByExternalId($externalOperationId);
        
        if ($operation === null) {
            return null;
        }

        return $this->convertToLegacyDto($operation);
    }

    /**
     * Проверить совместимость с существующими проверками
     */
    public function isKess3Project(ProjectFiles $file): bool
    {
        // Логика определения, является ли проект Kess3
        // Можно расширить на основе параметров проекта
        return $file->project && in_array($file->project->readmethod_id, [58, 59]);
    }

    private function generateCallbackUrl(): string
    {
        return Yii::$app->urlManagerFrontend->createAbsoluteUrl('/api/kess3-decoded');
    }

    private function calculateDuration(
        \common\chip\externalIntegrations\kess3\Domain\Entity\DecodingOperation $operation
    ): string {
        $startedAt = $operation->getStartedAt() ?? $operation->getCreatedAt();
        $completedAt = $operation->getCompletedAt();

        if ($completedAt === null) {
            return '';
        }

        $duration = $completedAt->diff($startedAt);
        return $duration->format('%H:%I:%S');
    }
}
