<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Repository;

use common\chip\externalIntegrations\kess3\Domain\Entity\EncodingOperation;
use common\chip\externalIntegrations\kess3\Domain\Repository\EncodingOperationRepositoryInterface;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\EncodingStatus;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\OperationId;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\ProjectId;
use common\models\AlientechAsyncOperation;
use common\models\ProjectFiles;
use yii\db\Query;

/**
 * Репозиторий операций энкодирования
 */
final class EncodingOperationRepository implements EncodingOperationRepositoryInterface
{
    private const asyncOperationType = 7;

    public function save(EncodingOperation $operation): void
    {
        $model = $this->findModel($operation->getOperationId()) ?? new AlientechAsyncOperation();

        $model->guid = $operation->getOperationId()->getValue();
        $model->asyncOperationType = self::asyncOperationType;
        $model->project_id = $operation->getProjectId()->getValue();
        $model->status = $operation->getStatus()->getValue();
        $model->external_guid = $operation->getExternalOperationId();
        $model->slotGUID = $operation->getSlotGuid();
        $model->result = $operation->getResult() ? json_encode($operation->getResult()) : null;
        $model->error = $operation->getError() ? json_encode($operation->getError()) : null;
        $model->userInfo = $operation->getUserInfo() ? json_encode($operation->getUserInfo()) : null;
        $model->startedOn = $operation->getStartedAt()?->format('Y-m-d H:i:s');
        $model->completedOn = $operation->getCompletedAt()?->format('Y-m-d H:i:s');
        $model->created_at = $operation->getCreatedAt()->format('Y-m-d H:i:s');

        // Сохраняем fileIds как JSON
        $model->file_id = $this->getInitFileId($operation->getProjectId()->getValue());
        $model->files = json_encode($operation->getFileIds());
        $model->isSuccessful = 0;
        $model->hasFailed = 0;
        $model->callback_url = $operation->getCallbackUrl();

        if (!$model->save()) {
            throw new \RuntimeException('Failed to save encoding operation: ' . json_encode($model->errors));
        }
    }

    public function findById(OperationId $operationId): ?EncodingOperation
    {
        $model = $this->findModel($operationId);
        return $model ? $this->mapToEntity($model) : null;
    }

    public function findByExternalId(string $externalOperationId): ?EncodingOperation
    {
        $model = AlientechAsyncOperation::find()
            ->where([
                'external_guid' => $externalOperationId,
                'asyncOperationType' => self::asyncOperationType,
            ])
            ->one();

        return $model ? $this->mapToEntity($model) : null;
    }

    public function findByProjectId(ProjectId $projectId): array
    {
        $models = AlientechAsyncOperation::find()
            ->where([
                'project_id' => $projectId->getValue(),
                'asyncOperationType' => self::asyncOperationType,
            ])
            ->orderBy(['created_at' => SORT_DESC])
            ->all();

        return array_map([$this, 'mapToEntity'], $models);
    }

    public function findInProgress(): array
    {
        $models = AlientechAsyncOperation::find()
            ->where([
                'status' => EncodingStatus::IN_PROGRESS,
                'asyncOperationType' => self::asyncOperationType,
            ])
            ->all();

        return array_map([$this, 'mapToEntity'], $models);
    }

    public function delete(EncodingOperation $operation): void
    {
        $model = $this->findModel($operation->getOperationId());
        if ($model) {
            $model->delete();
        }
    }

    private function findModel(OperationId $operationId): ?AlientechAsyncOperation
    {
        return AlientechAsyncOperation::find()
            ->where([
                'guid' => $operationId->getValue(),
                'asyncOperationType' => self::asyncOperationType,
            ])
            ->one();
    }

    private function mapToEntity(AlientechAsyncOperation $model): EncodingOperation
    {
        $fileIds = json_decode($model->files, true) ?? [];
        
        $operation = EncodingOperation::create(
            operationId: OperationId::fromString($model->guid),
            projectId: ProjectId::fromInt((int) $model->project_id),
            fileIds: $fileIds,
            callbackUrl: '', // Callback URL не хранится в модели
            userInfo: $model->userInfo ? json_decode($model->userInfo, true) : []
        );

        // Устанавливаем статус и другие поля через рефлексию или дополнительные методы
        $reflection = new \ReflectionClass($operation);

        $statusProperty = $reflection->getProperty('status');
        $statusProperty->setAccessible(true);
        $statusProperty->setValue($operation, new EncodingStatus((int) $model->status));

        if ($model->external_guid) {
            $externalIdProperty = $reflection->getProperty('externalOperationId');
            $externalIdProperty->setAccessible(true);
            $externalIdProperty->setValue($operation, $model->external_guid);
        }

        if ($model->slotGUID) {
            $slotGuidProperty = $reflection->getProperty('slotGuid');
            $slotGuidProperty->setAccessible(true);
            $slotGuidProperty->setValue($operation, $model->slotGUID);
        }

        if ($model->result) {
            $operation->setResult(json_decode($model->result, true));
        }

        if ($model->error) {
            $operation->setError(json_decode($model->error, true));
        }

        if ($model->startedOn) {
            $startedAtProperty = $reflection->getProperty('startedAt');
            $startedAtProperty->setAccessible(true);
            $startedAtProperty->setValue($operation, new \DateTimeImmutable($model->startedOn));
        }

        if ($model->completedOn) {
            $completedAtProperty = $reflection->getProperty('completedAt');
            $completedAtProperty->setAccessible(true);
            $completedAtProperty->setValue($operation, new \DateTimeImmutable($model->completedOn));
        }

        if ($model->created_at) {
            $createdAtProperty = $reflection->getProperty('createdAt');
            $createdAtProperty->setAccessible(true);
            $createdAtProperty->setValue($operation, new \DateTimeImmutable($model->created_at));
        }

        return $operation;
    }

    private function getInitFileId(int $projectId): ?int
    {
        return ProjectFiles::find()
            ->where([
                'project_id' => $projectId,
                'type' => 'init',
            ])
            ->one()?->id ?? null;
    }
}
