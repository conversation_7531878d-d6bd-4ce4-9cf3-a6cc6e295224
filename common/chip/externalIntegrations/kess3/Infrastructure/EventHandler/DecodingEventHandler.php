<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\EventHandler;

use common\chip\event\core\EventInterface;
use common\chip\event\EventHandler;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingCompletedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingFailedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingStartedEvent;
use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClient;
use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClientInterface;
use common\chip\alientech\entities\dto\AsyncOperationResultDto;
use common\chip\project\services\MessageService;
use common\chip\project\entities\dto\ProjectNoteSystemDto;
use common\models\ProjectFiles;
use common\models\Projects;
use common\helpers\ProjectHelper;
use Yii;

/**
 * Обработчик событий декодирования для интеграции с существующей системой
 */
final readonly class DecodingEventHandler implements EventHandler
{

    public function __construct(
        private AlientechApiClient $apiClient,
        private MessageService $messageService
    ) {
    }

    public function canHandle(EventInterface $event): bool
    {
        return $event instanceof DecodingStartedEvent
            || $event instanceof DecodingCompletedEvent
            || $event instanceof DecodingFailedEvent;
    }

    public function handle(EventInterface $event): void
    {
        try {
            if ($event instanceof DecodingStartedEvent) {
                $this->handleDecodingStarted($event);
            } elseif ($event instanceof DecodingCompletedEvent) {
                $this->handleDecodingCompleted($event);
            } elseif ($event instanceof DecodingFailedEvent) {
                $this->handleDecodingFailed($event);
            }
        } catch (\Exception $e) {
            Yii::error(
                message: "Error handling decoding event: {$e->getMessage()}",
                category: 'kess3.event_handler'
            );
        }
    }

    private function handleDecodingStarted(DecodingStartedEvent $event): void
    {
        $project = $this->getProject($event->getProjectId());
        if (!$project) {
            return;
        }

        // Добавляем системную заметку о начале декодирования
        $note = new ProjectNoteSystemDto(
            projectId: $event->getProjectId(),
            noteTitle: 'Kess3 Decoding Started',
            noteContent: "Decoding process started for operation {$event->getOperationId()}",
            noteComment: "External operation ID: {$event->getExternalOperationId()}"
        );

        $this->messageService->addNoteSystemFromAlientech($note);

        Yii::info(
            message: "Decoding started event handled for project {$event->getProjectId()}",
            category: 'kess3.event_handler'
        );
    }

    private function handleDecodingCompleted(DecodingCompletedEvent $event): void
    {
        $project = $this->getProject($event->getProjectId());
        if (!$project) {
            return;
        }

        try {
            // Скачиваем декодированные файлы
            $this->downloadDecodedFiles($event);

            // Добавляем системную заметку об успешном завершении
            $note = new ProjectNoteSystemDto(
                projectId: $event->getProjectId(),
                noteTitle: 'Kess3 Decoding Completed',
                noteContent: "Decoding completed successfully for operation {$event->getOperationId()}",
                noteComment: 'Decoded files have been downloaded and added to the project'
            );

            $this->messageService->addNoteSystemFromAlientech($note);

            // Обновляем статус проекта
            $this->updateProjectStatus($project, ProjectHelper::STATUS_CHANGED);

            Yii::info(
                message: "Decoding completed event handled for project {$event->getProjectId()}",
                category: 'kess3.event_handler'
            );
        } catch (\Exception $e) {
            Yii::error(
                message: "Error processing completed decoding: {$e->getMessage()}",
                category: 'kess3.event_handler'
            );

            $this->handleDecodingError($event->getProjectId(), $e->getMessage());
        }
    }

    private function handleDecodingFailed(DecodingFailedEvent $event): void
    {
        $project = $this->getProject($event->getProjectId());
        if (!$project) {
            return;
        }

        // Добавляем системную заметку об ошибке
        $note = new ProjectNoteSystemDto(
            projectId: $event->getProjectId(),
            noteTitle: 'Kess3 Decoding Failed',
            noteContent: "Decoding failed for operation {$event->getOperationId()}: {$event->getErrorMessage()}",
            noteComment: json_encode($event->getError())
        );

        $this->messageService->addNoteSystemFromAlientech($note);

        // Обновляем статус проекта
        $this->updateProjectStatus($project, ProjectHelper::STATUS_ERROR);

        Yii::error(
            message: "Decoding failed for project {$event->getProjectId()}: {$event->getErrorMessage()}",
            category: 'kess3.event_handler'
        );
    }

    private function downloadDecodedFiles(DecodingCompletedEvent $event): void
    {
        $result = $event->getResult();

        if (empty($result)) {
            Yii::warning(
                message: "No decoded file URLs found in result for operation {$event->getOperationId()}",
                category: 'kess3.event_handler'
            );
            return;
        }

        $operationResultDto = new AsyncOperationResultDto((object) $result);
        $urls = $operationResultDto->generateDecodedFileUrls();

        foreach ($urls as $type => $url) {
            try {
                if (YII_DEBUG) {
                    Yii::info(
                        message: "Downloading decoded file from {$url}",
                        category: 'kess3.event_handler'
                    );

                    $fileContent = file_get_contents(Yii::getAlias('@storage') . "/payload/alientechDecoded{$type}.json");
                    $fileData = json_decode($fileContent, true);
                } else {
                    $fileData = $this->apiClient->downloadFile($url);
                }

                if (empty($fileData['data'])) {
                    continue;
                }

                $this->saveDecodedFile($event, $fileData, $type);
            } catch (\Exception $e) {
                Yii::error(
                    message: "Failed to download decoded file from {$url}: {$e->getMessage()}",
                    category: 'kess3.event_handler'
                );
            }
        }
    }

    private function saveDecodedFile(DecodingCompletedEvent $event, array $fileData, string $type): void
    {
        $fileName = $this->generateDecodedFileName($event, $type);
        $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $fileName;

        // Декодируем и сохраняем файл
        $decodedData = base64_decode($fileData['data']);
        
        if (file_put_contents($filePath, $decodedData) === false) {
            throw new \RuntimeException("Failed to save decoded file: {$fileName}");
        }
        $fileData['data'] = '';
        // Создаем запись в БД
        $projectFile = new ProjectFiles([
            'type' => 'external',
            'title' => $fileName,
            'project_id' => $event->getProjectId(),
            'file_id' => $event->getFileId(),
            'file_type' => ProjectHelper::FILE_TYPE_ORIGINAL_DECODED,
            'orig' => ProjectHelper::PROJECT_FILE_ORIGINAL,
            'params' => json_encode($fileData),
            'path' => $filePath,
            'filename' => $fileName,
            'hash' => Yii::$app->security->generateRandomString(12),
        ]);

        if (!$projectFile->save()) {
            Yii::error(
                message: "Failed to save project file record: " . json_encode($projectFile->errors),
                category: 'kess3.event_handler'
            );
        }
    }

    private function generateDecodedFileName(DecodingCompletedEvent $event, string $type): string
    {
        $suffix = $type ? "_DECODED_{$type}" : '_DECODED';
        return "project_{$event->getProjectId()}_file_{$event->getFileId()}{$suffix}.bin";
    }

    private function getProject(int $projectId): ?Projects
    {
        return Projects::findOne($projectId);
    }

    private function updateProjectStatus(Projects $project, int $status): void
    {
        $project->status_admin = $status;
        $project->updated_by = $project->created_by;
        $project->save(false);
    }

    private function handleDecodingError(int $projectId, string $errorMessage): void
    {
        $project = $this->getProject($projectId);
        if ($project) {
            $this->updateProjectStatus($project, ProjectHelper::STATUS_ERROR);
        }

        $note = new ProjectNoteSystemDto(
            projectId: $projectId,
            noteTitle: 'Kess3 Decoding Error',
            noteContent: "Error processing decoded files: {$errorMessage}",
            noteComment: $errorMessage
        );

        $this->messageService->addNoteSystemFromAlientech($note);
    }
}
