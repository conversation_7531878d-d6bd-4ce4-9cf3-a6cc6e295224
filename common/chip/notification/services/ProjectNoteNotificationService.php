<?php

namespace common\chip\notification\services;

use common\chip\event\EventDispatcher;
use common\chip\notification\events\NoteAddedEvent;
use common\chip\project\entities\dto\ProjectNoteDto;
use common\models\Projects;
use Psr\Log\LoggerInterface;
use Yii;

/**
 * Упрощенный сервис для отправки уведомлений о заметках
 * Наследуется от системы событий common/chip/event
 * Содержит только логику создания событий
 */
class ProjectNoteNotificationService
{
    private EventDispatcher $eventDispatcher;
    private LoggerInterface $logger;

    public function __construct(
        EventDispatcher $eventDispatcher = null,
        LoggerInterface $logger = null
    ) {
        $this->eventDispatcher = $eventDispatcher ?: Yii::$container->get(EventDispatcher::class);
        $this->logger = $logger ?: Yii::getLogger();
    }

    /**
     * Отправляет уведомление о добавлении заметки от автора проекта
     */
    public function notifyNoteFromAuthor(ProjectNoteDto $note, Projects $project): void
    {
        try {
            $event = new NoteAddedEvent(
                projectId: $note->getProjectId(),
                noteTitle: $note->getNoteTitle(),
                noteContent: $note->getNoteContent(),
                noteComment: $note->getNoteComment(),
                createdBy: $project->created_by,
                noteType: 'client_note'
            );

            // Используем существующий EventDispatcher для асинхронной отправки
            $this->eventDispatcher->dispatchAsync(
                $event,
                'project',
                $project->id,
                $project->created_by
            );

            $this->logger->info('Note notification event dispatched', [
                'project_id' => $note->getProjectId(),
                'note_type' => 'client_note',
                'created_by' => $project->created_by
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to dispatch note notification event', [
                'project_id' => $note->getProjectId(),
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to notify about note from author', 0, $e);
        }
    }

    /**
     * Отправляет уведомление о добавлении заметки от менеджера
     */
    public function notifyNoteFromManager(ProjectNoteDto $note, Projects $project, int $managerId): void
    {
        try {
            $event = new NoteAddedEvent(
                projectId: $note->getProjectId(),
                noteTitle: $note->getNoteTitle(),
                noteContent: $note->getNoteContent(),
                noteComment: $note->getNoteComment(),
                createdBy: $managerId,
                noteType: 'manager_response'
            );

            $this->eventDispatcher->dispatchAsync(
                $event,
                'project',
                $project->id,
                $managerId
            );

            $this->logger->info('Manager note notification event dispatched', [
                'project_id' => $note->getProjectId(),
                'manager_id' => $managerId
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to dispatch manager note notification event', [
                'project_id' => $note->getProjectId(),
                'manager_id' => $managerId,
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to notify about note from manager', 0, $e);
        }
    }

    /**
     * Отправляет уведомление о системной заметке
     */
    public function notifySystemNote(ProjectNoteDto $note, Projects $project, string $systemType): void
    {
        try {
            $event = new NoteAddedEvent(
                projectId: $note->getProjectId(),
                noteTitle: $note->getNoteTitle(),
                noteContent: $note->getNoteContent(),
                noteComment: $note->getNoteComment(),
                createdBy: $this->getSystemUserId($systemType),
                noteType: "system_{$systemType}"
            );

            $this->eventDispatcher->dispatchAsync(
                $event,
                'project',
                $project->id,
                $this->getSystemUserId($systemType)
            );

            $this->logger->info('System note notification event dispatched', [
                'project_id' => $note->getProjectId(),
                'system_type' => $systemType
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to dispatch system note notification event', [
                'project_id' => $note->getProjectId(),
                'system_type' => $systemType,
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to notify about system note', 0, $e);
        }
    }

    /**
     * Получает ID системного пользователя
     */
    private function getSystemUserId(string $systemType): int
    {
        return match($systemType) {
            'alientech' => \common\helpers\MessageHelper::ALIENTECH_USER_ID ?? 0,
            'autopack' => \common\helpers\MessageHelper::AUTOPACK_USER_ID ?? 0,
            default => \common\helpers\MessageHelper::SYSTEM_USER_ID ?? 0
        };
    }
}
