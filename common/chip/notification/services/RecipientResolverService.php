<?php

namespace common\chip\notification\services;

use common\chip\event\core\EventInterface;
use common\chip\event\core\OptionsRequestEvent;
use common\chip\notification\events\NoteAddedEvent;
use common\chip\notification\events\ProjectStatusChangedEvent;
use common\chip\notification\events\SystemErrorEvent;
use common\chip\event\core\FileUploadedEvent;
use common\chip\event\core\FileProcessedEvent;
use common\chip\event\core\FileProcessingErrorEvent;
use common\chip\event\core\ProjectCreatedEvent;
use common\chip\event\core\ProjectNoteAddedEvent;
use common\models\Projects;

/**
 * Сервис для определения получателей уведомлений
 * Содержит только логику выбора получателей и каналов
 */
class RecipientResolverService
{
    /**
     * Определяет получателей для события
     */
    public function resolveRecipients(EventInterface $event): array
    {
        return match(get_class($event)) {
            NoteAddedEvent::class => $this->resolveNoteRecipients($event),
            ProjectStatusChangedEvent::class => $this->resolveProjectRecipients($event),
            SystemErrorEvent::class => $this->resolveSystemErrorRecipients($event),
            FileUploadedEvent::class => $this->resolveFileUploadedRecipients($event),
            FileProcessedEvent::class => $this->resolveFileProcessedRecipients($event),
            FileProcessingErrorEvent::class => $this->resolveFileErrorRecipients($event),
            ProjectCreatedEvent::class => $this->resolveProjectCreatedRecipients($event),
            ProjectNoteAddedEvent::class => $this->resolveProjectNoteRecipients($event),
            OptionsRequestEvent::class => $this->resolveOptionsRequestRecipients($event),
            default => []
        };
    }

    /**
     * Получатели для событий заметок
     */
    private function resolveNoteRecipients(NoteAddedEvent $event): array
    {
        return match($event->getNoteType()) {
            'client_note' => [
                [
                    'type' => 'role',
                    'id' => 'admin',
                    'channels' => ['ui_popup', 'telegram'],
                    'display_location' => 'top-right',
                    'show_until_clicked' => true
                ],
                [
                    'type' => 'role',
                    'id' => 'manager',
                    'channels' => ['ui_popup'],
                    'display_location' => 'top-right',
                    'show_until_clicked' => true
                ]
            ],
            'manager_response' => [
                [
                    'type' => 'user',
                    'id' => (string)$this->getProjectAuthor($event->getProjectId()),
                    'channels' => ['ui_popup', 'email'],
                    'display_location' => 'center',
                    'show_until_clicked' => true
                ]
            ],
            'system_alientech', 'system_autopack' => [
                [
                    'type' => 'role',
                    'id' => 'admin',
                    'channels' => ['ui_popup', 'telegram'],
                    'display_location' => 'top-right',
                    'show_until_clicked' => false
                ]
            ],
            default => []
        };
    }

    /**
     * Получатели для событий проектов
     */
    private function resolveProjectRecipients(ProjectStatusChangedEvent $event): array
    {
        $recipients = [
            [
                'type' => 'user',
                'id' => (string)$this->getProjectAuthor($event->getProjectId()),
                'channels' => ['ui_popup', 'email'],
                'show_until_clicked' => true
            ]
        ];

        // Для завершенных проектов добавляем SMS
        if ($event->getNewStatus() === 'completed') {
            $recipients[0]['channels'][] = 'sms';
        }

        return $recipients;
    }

    /**
     * Получатели для системных ошибок
     */
    private function resolveSystemErrorRecipients(SystemErrorEvent $event): array
    {
        $channels = ['ui_popup', 'telegram'];

        // Для критических ошибок добавляем SMS
        if ($event->getSeverity() === 'critical') {
            $channels[] = 'sms';
        }

        return [
            [
                'type' => 'role',
                'id' => 'admin',
                'channels' => $channels,
                'show_until_clicked' => true
            ]
        ];
    }

    /**
     * Получатели для событий загрузки файлов
     */
    private function resolveFileUploadedRecipients(FileUploadedEvent $event): array
    {
        return [
            [
                'type' => 'role',
                'id' => 'admin',
                'channels' => ['ui_popup'],
                'display_location' => 'top-right',
                'show_until_clicked' => false
            ]
        ];
    }

    /**
     * Получатели для событий обработки файлов
     */
    private function resolveFileProcessedRecipients(FileProcessedEvent $event): array
    {
        return [
            [
                'type' => 'user',
                'id' => (string)$this->getProjectAuthor($event->getProjectId()),
                'channels' => ['ui_popup', 'email'],
                'display_location' => 'center',
                'show_until_clicked' => true
            ]
        ];
    }

    /**
     * Получатели для ошибок обработки файлов
     */
    private function resolveFileErrorRecipients(FileProcessingErrorEvent $event): array
    {
        return [
            [
                'type' => 'role',
                'id' => 'admin',
                'channels' => ['ui_popup', 'telegram', 'sms'],
                'display_location' => 'center',
                'show_until_clicked' => true
            ],
            [
                'type' => 'user',
                'id' => (string)$this->getProjectAuthor($event->getProjectId()),
                'channels' => ['ui_popup', 'email'],
                'display_location' => 'center',
                'show_until_clicked' => true
            ]
        ];
    }

    /**
     * Получатели для событий создания проекта
     */
    private function resolveProjectCreatedRecipients(ProjectCreatedEvent $event): array
    {
        return [
            [
                'type' => 'role',
                'id' => 'admin',
                'channels' => ['ui_popup', 'telegram'],
                'display_location' => 'top-right',
                'show_until_clicked' => false
            ]
        ];
    }

    /**
     * Получатели для событий добавления заметки к проекту
     */
    private function resolveProjectNoteRecipients(ProjectNoteAddedEvent $event): array
    {
        return [
            [
                'type' => 'role',
                'id' => 'admin',
                'channels' => ['ui_popup', 'telegram'],
                'display_location' => 'top-right',
                'show_until_clicked' => true
            ]
        ];
    }

    /**
     * Получает автора проекта
     */
    private function getProjectAuthor(int $projectId): int
    {
        $project = Projects::findOne($projectId);
        return $project ? $project->created_by : 0;
    }

    private function resolveOptionsRequestRecipients(OptionsRequestEvent $event): array
    {
        return [
            [
                'type' => 'role',
                'id' => 'admin',
                'channels' => ['project_notes']
            ]
        ];
    }
}
